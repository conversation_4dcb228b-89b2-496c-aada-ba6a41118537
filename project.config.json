{"miniprogramRoot": "", "projectname": "quantum-weapp", "description": "", "appid": "wxa336bca6a1571664", "setting": {"urlCheck": true, "es6": true, "enhance": true, "compileHotReLoad": false, "postcss": false, "minified": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "condition": false, "bigPackageSizeSupport": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": false, "minifyWXML": false, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "2.30.4", "packOptions": {"ignore": [], "include": []}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "simulatorPluginLibVersion": {}}