import { defineConfig } from '@tarojs/cli';

const path = require('path');

export default defineConfig(async (merge, { command, mode }) => {
    console.log('command', command, mode);
    let dist = 'dist';
    if (mode === 'production') {
        dist = 'dist';
    } else if (mode === 'development') {
        dist = 'dist_dev';
    } else if (mode === 'yubeiai') {
        dist = 'dist_yubeiai';
    }
    const config = {
        projectName: 'hera-hygia-mini',
        date: '2023-7-3',
        designWidth: 750,
        deviceRatio: {
            640: 2.34 / 2,
            750: 1,
            828: 1.81 / 2
        },
        sourceRoot: 'src',
        outputRoot: dist,
        defineConstants: {},
        copy: {
            patterns: [],
            options: {}
        },
        framework: 'react',
        compiler: {
            type: 'webpack5',
            prebundle: {
                enable: false
            }
        },
        cache: {
            enable: false // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
        },
        alias: {
            '@': path.join(__dirname, '../src')
        },
        plugins: ['@tarojs/plugin-html'],
        mini: {
            postcss: {
                pxtransform: {
                    enable: true,
                    config: {}
                },
                url: {
                    enable: true,
                    config: {
                        limit: 1024 // 设定转换尺寸上限
                    }
                },
                cssModules: {
                    enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
                    config: {
                        namingPattern: 'global', // 转换模式，取值为 global/module
                        generateScopedName: '[name]__[local]___[hash:base64:5]'
                    }
                }
            },
            miniCssExtractPluginOption: {
                ignoreOrder: true
            }
        },
        h5: {
            publicPath: '/',
            staticDirectory: 'static',
            esnextModules: [/@antmjs[\/]vantui/],
            postcss: {
                autoprefixer: {
                    enable: true,
                    config: {
                        limit: 1024 // 设定转换尺寸上限
                    }
                },
                pxtransform: {
                    enable: true,
                    config: {}
                },
                cssModules: {
                    enable: true, // 默认为 false，如需使用 css modules 功能，则设为 true
                    config: {
                        namingPattern: 'global', // 转换模式，取值为 global/module
                        generateScopedName: '[name]__[local]___[hash:base64:5]'
                    }
                }
            }
        }
    };
    if (process.env.NODE_ENV === 'development') {
        return merge({}, config, require('./dev'));
    }
    return merge({}, config, require('./prod'));
});
