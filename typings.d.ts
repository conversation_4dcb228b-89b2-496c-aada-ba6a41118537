// / <reference types="@tarojs/taro" />

declare module '*.png';
declare module '*.gif';
declare module '*.jpg';
declare module '*.jpeg';
declare module '*.svg';
declare module '*.css';
declare module '*.less';
declare module '*.scss';
declare module '*.sass';
declare module '*.styl';

declare namespace NodeJS {
    interface ProcessEnv {
        TARO_ENV: 'weapp' | 'swan' | 'alipay' | 'h5' | 'rn' | 'tt' | 'quickapp' | 'qq' | 'jd';
    }
}

declare namespace wx {
    interface qyMethods {
        login: (options: {
            success?: (res: { code: string; errMsg: string }) => void;
            fail?: (err: any) => void;
        }) => void;
        getAvatar: (options: { success?: (res: { avatar: string }) => void; fail?: (err: any) => void }) => void;
        checkSession: (options: { success?: (res: { errMsg: string }) => void; fail?: (err: any) => void }) => void;
        getEnterpriseUserInfo: (options: {
            success?: (res: {
                userInfo: { name: string; gender: number; language: string };
                rawData: string;
                signature: string;
                encryptedData: string;
                iv: string;
            }) => void;
            fail?: (err: any) => void;
        }) => void;
    }
    const qy: qyMethods;
}
declare function wx(): void;
