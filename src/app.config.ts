const APP_MODE = process.env.TARO_APP_MODE;
console.log('APP_MODE', APP_MODE);

const tabbarList = [
    {
        pagePath: 'pages/home/<USER>',
        text: '对话',
        iconPath: './assets/workbench.png',
        selectedIconPath: './assets/selectedWorkbench.png'
    },
    {
        pagePath: 'pages/pk/index',
        text: 'PK赛',
        iconPath: './assets/workbench.png',
        selectedIconPath: './assets/selectedWorkbench.png'
    },
    {
        pagePath: 'pages/mini/index',
        text: '我的',
        iconPath: './assets/mine.png',
        selectedIconPath: './assets/selectedMine.png'
    }
];

const aiSparringPage = ['pages/aiSparring/index', 'pages/scriptPage/index'];

const emanPage = [
    'pages/eman/home/<USER>',
    'pages/eman/characterStepOne/index',
    'pages/eman/characterStepTwo/index',
    'pages/eman/certification/index',
    'pages/eman/certificationAgreement/index',
    'pages/eman/certificationResult/index'
];

const pages = [
    'pages/middle/index',
    'pages/home/<USER>',
    'pages/pk/index',
    'pages/mini/index',
    'pages/login/index/index',
    'pages/login/choose/index',
    'pages/login/register/index',
    'pages/emanDetail/index',
    'pages/shuziren/index'
];

const subPackages = [
    {
        root: 'pages/settings',
        pages: ['setting/index', 'user/index']
    },
    {
        root: 'pages/chat',
        pages: [
            'sceneExercise/index',
            'dialogue/index',
            'history/index',
            'report/index',
            'enterpriseIntelligence/index'
        ]
    },
    {
        root: 'pages/qrcode',
        pages: ['end/index']
    }
];

const preloadRule: any = {
    'pages/home/<USER>': {
        network: 'all',
        packages: ['pages/chat']
    },
    'pages/middle/index': {
        network: 'all',
        packages: ['pages/chat']
    }
};

if (APP_MODE !== 'yubeiai') {
    // tabbarlist第二个插入
    tabbarList.splice(1, 0, {
        pagePath: 'pages/aiSparring/index',
        text: '陪练',
        iconPath: './assets/AI.png',
        selectedIconPath: './assets/AIIcon.png'
    });
    pages.push(...aiSparringPage);
    subPackages.push(
        {
            root: 'pages/points',
            pages: ['center/index', 'detail/index', 'rank/index']
        },
        {
            root: 'pages/data',
            pages: ['board/index', 'detail/index', 'points/index']
        },
        {
            root: 'pages/activity',
            pages: ['checkpoint/list/index', 'checkpoint/detail/index']
        },
        {
            root: 'pages/practicePPT',
            pages: ['detail/index', 'practice/index', 'historyPPT/index']
        }
    );
    Object.assign(preloadRule, {
        'pages/mini/index': {
            network: 'all',
            packages: ['pages/points', 'pages/data', 'pages/activity']
        },
        'pages/aiSparring/index': {
            network: 'all',
            packages: ['pages/chat', 'pages/practicePPT']
        }
    });
} else {
    pages.push(...emanPage);
}

export default defineAppConfig({
    pages,
    subPackages,
    preloadRule,
    tabBar: {
        list: tabbarList,
        selectedColor: '#167FFF'
    },
    window: {
        backgroundTextStyle: 'light',
        navigationBarBackgroundColor: '#fff',
        navigationBarTextStyle: 'black',
        navigationStyle: 'default'
    }
    // plugins: {
    // QCloudAIVoice: {
    //     version: '2.3.10',
    //     provider: 'wx3e17776051baf153'
    // }
    // }
});
