export enum VoiceStatus {
    Start, // 开始
    BeforeListen, // 开启聆听前
    Listening, // 语音聆听
    WaitListen, // 等待聆听
    Waiting, // 等待AI回复
    Speaking, // AI说话
    Cancel, // 触发取消发送
    Canceled, // 确定取消发送
    Overtime, // 聆听超时
    Standby, // 聆听待机
    Stop, // 停止
    Complete, // 完成答题
    Loading, // 加载中
    WaitSend, // 等待发送
    AllowSend // 允许发送
}

// 根据值，获取VoiceStatus的key名称
export function getVoiceStatusKey(value: number): string {
    for (const key in VoiceStatus) {
        if (VoiceStatus[key] === value) {
            return key;
        }
    }
    return '';
}

export enum RecognizeStatus {
    Start, // 识别开始
    SentenceBegin, // 一句话开始
    SentenceChange, // 识别变化中
    SentenceEnd, // 一句话完成
    RecorderStop, // 录音停止
    Error, // 识别错误
    Complete // 识别结束
}

export enum VoiceInteraction {
    Auto = 1, // 自动发送语音
    Manual = 2, // 长按发送语音
    TapManual = 3 // 手动开始、结束发送语音
}
export enum ChatMode {
    AUDIO = 1,
    SHUZIREN = 2
}

export enum SubtitleVisible {
    Show = 1,
    Hide = 2
}

export enum DeepThinkStatus {
    Off = 0, // 关闭
    On = 1 // 开启
}
