import Taro from '@tarojs/taro';

/** 本地缓存 key */
const { envVersion } = Taro.getAccountInfoSync().miniProgram;

export const StorageEnvKey = {
    CHAT_MODE: `${envVersion}_CHAT_MODE`,
    TOKEN: `${envVersion}_TOKEN`,
    OPENID: `${envVersion}_OPENID`,
    TENANT_ID: `${envVersion}_TENANT_ID`,
    USERINFO: `${envVersion}_USERINFO`,
    IS_WEWORK: `${envVersion}_IS_WEWORK`,
    IS_STAET_HEART: `${envVersion}_IS_STAET_HEART`,
    Script_Type: `${envVersion}_Script_Type`,
    Ppt_Number: `${envVersion}_Ppt_Number`,
    IS_ENTERPRISE: `${envVersion}_IS_ENTERPRISE`,
    TENANT_NAME: `${envVersion}_TENANT_NAME`,
    TENANT_LIST: `${envVersion}_TENANT_LIST`,
    REFRESH_HOME: `${envVersion}_REFRESH_HOME`,
    HOME_DATA: `${envVersion}_HOME_DATA`,
    REPORT_DATA: `${envVersion}_REPORT_DATA`,
    QUESTION_INTERACTION_TYPE: `${envVersion}_QUESTION_INTERACTION_TYPE`,
    SKILL_INTERACTION_TYPE: `${envVersion}_SKILL_INTERACTION_TYPE`,
    INTELLIGENCE_INTERACTION_TYPE: `${envVersion}_INTELLIGENCE_INTERACTION_TYPE`,
    SUBSCRIBE_SCRIPT: `${envVersion}_SUBSCRIBE_SCRIPT`,
    DATA_TIME_RANGE: `${envVersion}_DATA_TIME_RANGE`,
    SUBTITLE_VISIBLE: `${envVersion}_SUBTITLE_VISIBLE`,
    IS_GROUNDING: `${envVersion}_IS_GROUNDING`,
    HOME_PATH: `${envVersion}_HOME_PATH`,
    DEEP_THINK: `${envVersion}_DEEP_THINK`
};

console.log(StorageEnvKey);
