import consoleUtils from '@/utils/consoleUtils';

function removeDuplicateText(text: string): string {
    // 将文本按句号分割成段落
    if (text.length > 30) {
        const paragraphs = text.split(/。|\./).filter((p) => p.trim() !== '');

        // 用于存储已经出现过的段落
        const seen = new Set();
        const result = [];

        // 遍历每个段落，保留未重复的部分
        for (const paragraph of paragraphs) {
            const trimmedParagraph = paragraph.trim();
            if (!seen.has(trimmedParagraph)) {
                seen.add(trimmedParagraph);
                result.push(trimmedParagraph);
            } else {
                consoleUtils.warn(`重复段落 ${trimmedParagraph}`);
            }
        }

        // 将处理后的段落重新拼接成完整文本
        return `${result.join('。')}。`;
    } else {
        return text;
    }
}

const removeMarkdown = (text: string) => {
    return text
        .replace(/(\*\*|__)(.*?)\1/g, '$2') // 粗体
        .replace(/(\*|_)(.*?)\1/g, '$2') // 斜体
        .replace(/(~~)(.*?)\1/g, '$2') // 删除线
        .replace(/`([^`]+)`/g, '$1') // 行内代码
        .replace(/!\[.*?\]\(.*?\)/g, '') // 图片
        .replace(/\[([^\]]+)\]\(.*?\)/g, '$1') // 链接
        .replace(/^\s*#{1,3}\s*(.*)/gm, '$1') // 修改后的标题匹配规则，支持1-3个#号
        .replace(/^\s*>\s+(.*)/gm, '$1') // 引用
        .replace(/^\s*[-*+]\s+(.*)/gm, '$1') // 列表
        .replace(/^\s*---+/gm, '') // 分隔线
        .replace(/^\s*___+/gm, '') // 分隔线
        .replace(/^\s*\*\*\*+/gm, '') // 分隔线
        .replace(/&gt;|&lt;/g, '')
        .replace(/\n/g, '');
};

export { removeDuplicateText, removeMarkdown };
