import Taro from '@tarojs/taro';

export function checkNetworkStatus() {
    return new Promise((resolve, reject) => {
        Taro.getNetworkType({
            success: (res) => {
                console.log('getNetworkType', res);
                if (res.networkType === 'none') {
                    Taro.showToast({
                        icon: 'none',
                        title: '网络不给力，\r\n请检查网络设置'
                    });
                    reject('none');
                } else {
                    resolve(res.networkType);
                }
            },
            fail: (err) => {
                Taro.showToast({
                    icon: 'none',
                    title: '网络不给力，\r\n请检查网络设置'
                });
                reject(err);
            }
        });
    });
}
