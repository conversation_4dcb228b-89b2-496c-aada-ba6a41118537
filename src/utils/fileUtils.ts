import Taro from '@tarojs/taro';

const createFilePath = (folderName: string) => {
    // 对话tts文件目录
    const fs = Taro.getFileSystemManager();
    const dir = `${Taro.env.USER_DATA_PATH}/${folderName}`;

    fs.access({
        path: dir,
        success: () => {
            fs.rmdir({
                dirPath: dir,
                recursive: true,
                success: (res) => {
                    console.log('rmdir', res);
                    fs.mkdir({
                        dirPath: dir,
                        success: (res) => {
                            console.log('mkdir', res);
                        },
                        fail: (e) => {
                            console.error(e);
                        }
                    });
                },
                fail: (e) => {
                    console.error(e);
                }
            });
        },
        fail: () => {
            fs.mkdir({
                dirPath: dir,
                success: (res) => {
                    console.log('mkdir', res);
                },
                fail: (e) => {
                    console.error(e);
                }
            });
        }
    });
};

const createChatPath = () => createFilePath('chat');

export { createFilePath, createChatPath };
