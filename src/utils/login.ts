import { Storage } from '@/common';
import { StorageEnvKey } from '@/constants/storage';
import type { UserInfo } from '@/types/common';

export function loginSuccess({ userInfo, token }: { token: string; userInfo: UserInfo }) {
    Storage.set(StorageEnvKey.USERINFO, userInfo);
    Storage.set(StorageEnvKey.TOKEN, token);
    Storage.set(StorageEnvKey.OPENID, userInfo.openId);
}
export function updateUserStorage(userInfo: UserInfo) {
    Storage.set(StorageEnvKey.USERINFO, userInfo);
}
