import { medicalOccupation } from '@/constants/occupation';

function emanJob(occupation: string, title: string, department: string) {
    console.log('emanJob', occupation, title, department);
    let occupationText = '';
    if (medicalOccupation.includes(occupation)) {
        const t = (title || '') && title.split('-')[title.split('-').length - 1];
        occupationText = `${(department || '') && department.split('-')[department.split('-').length - 1]}${
            department && (title || '') && title !== '无' ? ' | ' : ''
        }${t === '无' ? '' : t || ''}`;
    } else {
        occupationText = `${occupation}${occupation && title && title !== '无' ? ' | ' : ''}${
            title !== '无' ? title : ''
        }`;
    }
    return occupationText;
}

export default emanJob;
