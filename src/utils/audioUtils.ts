// 一些工具函数，将数据进行转码、封装
function encodeWAV(samples: ArrayBuffer, numChannels: number, sampleRate: number) {
    const buffer = new ArrayBuffer(44 + samples.byteLength);
    const view = new DataView(buffer);
    /* RIFF identifier */
    writeString(view, 0, 'RIFF');
    /* RIFF chunk length */
    view.setUint32(4, 36 + samples.byteLength, true);
    /* RIFF type */
    writeString(view, 8, 'WAVE');
    /* format chunk identifier */
    writeString(view, 12, 'fmt ');
    /* format chunk length */
    view.setUint32(16, 16, true);
    /* sample format (raw) */
    view.setUint16(20, 1, true);
    /* channel count */
    view.setUint16(22, numChannels, true);
    /* sample rate */
    view.setUint32(24, sampleRate, true);
    /* byte rate (sample rate * block align) */
    view.setUint32(28, sampleRate * 4, true);
    /* block align (channel count * bytes per sample) */
    view.setUint16(32, numChannels * 2, true);
    /* bits per sample */
    view.setUint16(34, 16, true);
    /* data chunk identifier */
    writeString(view, 36, 'data');
    /* data chunk length */
    view.setUint32(40, samples.byteLength, true);

    copyBytes(view, 44, samples);

    return view.buffer;
}
function copyBytes(output: DataView, offset: number, input: ArrayBuffer) {
    const dataView = new DataView(input);
    for (let i = 0; i < input.byteLength; i++, offset++) {
        output.setInt8(offset, dataView.getInt8(i));
    }
}
function writeString(view: DataView, offset: number, str: string) {
    for (let i = 0; i < str.length; i++) {
        view.setUint8(offset + i, str.charCodeAt(i));
    }
}

export { encodeWAV };
