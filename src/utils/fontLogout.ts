import { Storage } from '@/common';
import { EventKey } from '@/constants/eventKey';
import { StorageEnvKey } from '@/constants/storage';
import Taro from '@tarojs/taro';

export default () => {
    Storage.del(StorageEnvKey.OPENID);
    Storage.del(StorageEnvKey.TOKEN);
    Storage.del(StorageEnvKey.USERINFO);
    Storage.del(StorageEnvKey.TENANT_ID);
    Taro.eventCenter.trigger(EventKey.LOGOUT);
};
