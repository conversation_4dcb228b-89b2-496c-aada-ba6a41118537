import config from '@/config';
import Taro from '@tarojs/taro';

interface RecordUploaderOptions {
    chatId: string;
    pptId: string;
    token: string;
    onMessage?: (message: string) => void;
    onError?: (error: Error) => void;
}

export class RecordUploader {
    private socketTask: Taro.SocketTask | null = null;
    private chatId: string;
    private pptId: string;
    private token: string;

    constructor(options: RecordUploaderOptions) {
        this.chatId = options.chatId;
        this.pptId = options.pptId;
        this.token = options.token;
    }

    async connect() {
        const url = `${config.server.replace('http', 'ws')}/ws/audioStream/${this.chatId}/${this.pptId}?token=${
            this.token
        }`;
        console.log(`Connecting to ${url}`);
        this.socketTask = await Taro.connectSocket({
            url,
            success: () => {
                console.log('WebSocket connected');
            },
            fail: (err) => {
                console.error('WebSocket connection failed', err);
                this.OnError(err);
            }
        });

        this.socketTask.onOpen((e) => {
            console.log('WebSocket opened', e);
            this.OnSocketOpen();
        });

        this.socketTask.onMessage((res) => {
            if (res.data) {
                console.log('Received message:', res.data);
            }
        });

        this.socketTask.onError((err) => {
            console.error('WebSocket error:', err);
            this.socketTask?.close({});
            this.OnError(err);
        });

        this.socketTask.onClose((e) => {
            console.log('WebSocket closed', e);
            this.OnSocketClose();
        });
    }

    sendAudioChunk(chunk: ArrayBuffer) {
        // console.log('sendAudioChunk', this.socketTask);

        if (!this.socketTask || this.socketTask.readyState !== 1) {
            console.warn('WebSocket is not connected');
            return;
        }
        this.socketTask.send({
            data: chunk,
            success: () => {
                // console.log('Audio chunk sent successfully');
            },
            fail: (err) => {
                console.log('Failed to send audio chunk', err);
            }
        });
    }

    close() {
        if (this.socketTask && this.socketTask.readyState === 1) {
            this.socketTask.close({});
        }
    }

    // 获取socket状态
    getSocketState(): number {
        return this.socketTask ? this.socketTask.readyState : 0;
    }
    OnSocketOpen(): void {}
    OnSocketClose(): void {}
    OnError(error: Error) {}
}
