interface SpeechRecognizerParams {
    appid: number;
    secretid: string;
    secretkey: string;
    engine_model_type: string;
    needvad?: number;
    voice_format?: number; // 音频格式，默认为1，代表pcm格式。目前仅支持，1: pcm, 8: mp3
    frameSize?: number; // 指定帧大小，单位 KB。传入 frameSize 后，每录制指定帧大小的内容后，会回调录制的文件内容，不指定则是默认值. 请谨慎设置此参数，会影响识别速度
    duration?: number; // 录音最大时长，单位 ms，默认60000
    filter_empty_result?: number;
    vad_silence_time?: number;
    max_speak_time?: number;
    noise_threshold?: number;
    input_sample_rate?: number;
    convert_num_mode?: number;
    signCallback?: (queryStr: string) => string;
}
interface SpeechRecognizerFlashResult {
    request_id: string;
    data: string;
}
interface SpeechRecognizerFlashParams {
    appid: number;
    data: any;
    dataLen: number;
    secretid: string;
    secretkey: string;
    engine_type: string;
    voice_format: string;
    hotword_id?: string;
    customization_id?: string;
    input_sample_rate?: number;
    success?: (result: { request_id: string; result: string }) => void;
    fail?: (error: TaroGeneral.CallbackResult) => void;
}
