/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */

import type { LogInfo } from '@/types/common';
import Taro from '@tarojs/taro';
import SpeechRecognizer from './SpeechRecognizer';
import { guid } from './utils';

export default class AudioSpeechRecognizer {
    params: SpeechRecognizerParams;
    recorder: Taro.RecorderManager | null;
    speechRecognizer: SpeechRecognizer | null;
    isCanSendData: boolean;
    isNormalEndStop: boolean;
    audioData: any[];
    isLog: boolean;
    requestId: string | null;

    status: 'start' | 'stop' = 'stop';
    recordStatus: 'start' | 'stop' = 'stop';

    constructor(params: SpeechRecognizerParams, isLog: boolean) {
        this.params = params;
        this.recorder = null;
        this.speechRecognizer = null;
        this.isCanSendData = false;
        this.isNormalEndStop = false;
        this.audioData = [];
        this.isLog = isLog;
        this.requestId = null;
    }

    start(): void {
        try {
            this.isLog && console.log('start function is click');
            this.requestId = guid();
            this.recorder = Taro.getRecorderManager();
            this.recorder.onFrameRecorded(
                (res: {frameBuffer: ArrayBuffer; isLastFrame: boolean; }) => {
                    // this.isLog && console.log('onFrameRecorded', this.isCanSendData, res);
                    if (this.isCanSendData) {
                        // this.isLog && console.log('OnReceivedData', this.OnFrameRecorded);
                        this.OnFrameRecorded(res);
                        this.speechRecognizer && this.speechRecognizer.write(res.frameBuffer);
                    }

                }
            );
            // 录音失败时
            this.recorder.onError((err: any) => {
                this.isLog && console.log('recorder OnError', err);
                if (this.speechRecognizer) {
                    this.speechRecognizer.stop();
                    // if (this.speechRecognizer.getSocketState() === 0) {
                    //     this.speechRecognizer = null;
                    // }
                }
                // this.stop();
                this.OnError(err);
            });
            this.recorder.onPause((res: any) => {
                this.isLog && console.log('recorder onPause', res);

                if (this.recordStatus === 'start') {
                    this.recorder?.stop();
                }
                this.recordStatus = 'stop';
                if (this.speechRecognizer) {
                    this.speechRecognizer.stop();
                }
            });
            this.recorder.onStop((res: any) => {
                this.isLog && console.log('recorder onStop', res);
                this.recordStatus = 'stop';
                if (this.speechRecognizer) {
                    this.speechRecognizer.stop();
                    // if (this.speechRecognizer.getSocketState() === 0) {
                    //     this.speechRecognizer = null;
                    // }
                }
                this.OnRecorderStop(res);
            });
            this.recorder.onStart((res: any) => {
                this.isLog && console.log('recorder onStart', res);
                this.recordStatus = 'start';
                if (this.status === 'stop') {
                    this.recorder?.stop();
                }
                this.OnRecorderStart(res);
            });

            this.recorder.start({
                sampleRate: (this.params.input_sample_rate as keyof Taro.RecorderManager.SampleRate) || 16000,
                duration: this.params.duration || 600000,
                frameSize: this.params.frameSize || 0.32,
                numberOfChannels: 1,
                format: this.params.voice_format ? (this.params.voice_format === 1 ? 'PCM' : 'mp3') : 'PCM'
            });
            if (!this.speechRecognizer) {
                this.speechRecognizer = new SpeechRecognizer(this.params, this.requestId, this.isLog);
            }
            // 开始识别
            this.speechRecognizer.OnRecognitionStart = (res: any) => {
                this.isLog && console.log('OnRecognitionStart', res);
                if (this.recorder) {
                    // 录音正常
                    this.OnRecognitionStart(res);
                    this.isCanSendData = true;
                } else {
                    if (this.speechRecognizer) {
                        this.speechRecognizer.stop();
                        if (this.speechRecognizer.getSocketState() === 0) {
                            this.speechRecognizer = null;
                        }
                    }
                }
            };
            // 一句话开始
            this.speechRecognizer.OnSentenceBegin = (res: any) => {
                this.OnSentenceBegin(res);
            };
            // 识别变化时
            this.speechRecognizer.OnRecognitionResultChange = (res: any) => {
                this.OnRecognitionResultChange(res);
            };
            // 一句话结束
            this.speechRecognizer.OnSentenceEnd = (res: any) => {
                this.OnSentenceEnd(res);
            };
            // 识别结束
            this.speechRecognizer.OnRecognitionComplete = (res: any) => {
                this.OnRecognitionComplete(res);
                this.isCanSendData = false;
                this.isNormalEndStop = true;
            };
            // 识别错误
            this.speechRecognizer.OnError = (res: any) => {
                if (this.speechRecognizer && !this.isNormalEndStop) {
                    this.OnError(res);
                }
                this.speechRecognizer = null;
                if (this.recorder) {
                    this.recorder.stop();
                }
                this.isCanSendData = false;
            };
            this.speechRecognizer.OnLog = (log: LogInfo) => {
                this.OnLog(log);
            };
            // 建立连接
            this.speechRecognizer.start();
            this.status = 'start';
        } catch (e) {
            console.log(e);
        }
    }

    stop(): void {
        this.isLog && console.log('stop function is click');
        this.status = 'stop';
        if (this.recorder && this.recordStatus === 'start') {
            // 如果录音未onStart，在ios下stop会报错
            this.recorder.stop();
        }
        // if (this.speechRecognizer) {
        //     this.speechRecognizer.stop();
        //     this.speechRecognizer = null;
        // }
    }

    destroyStream(): void {
        this.isLog && console.log('destroyStream function is click', this.recorder);
        if (this.recorder) {
            this.recorder.stop();
        }
    }

    // 开始识别的时候
    OnRecognitionStart(res: any): void {}

    // 一句话开始的时候
    OnSentenceBegin(res: any): void {}

    // 识别结果发生变化的时候
    OnRecognitionResultChange(res: any): void {}

    // 一句话结束的时候
    OnSentenceEnd(res: any): void {}

    // 识别结束的时候
    OnRecognitionComplete(res: any): void {}

    // 识别失败
    OnError(res: any): void {}

    // 监听已录制完指定帧
    OnFrameRecorded(res: { frameBuffer: ArrayBuffer; isLastFrame: boolean; }): void {}

    OnRecorderStart(res: any): void {}
    OnRecorderStop(res: any): void {}
    OnRecorderPause(res: any): void {}
    OnLog(log: LogInfo) {}
}
