/* eslint-disable @typescript-eslint/no-unused-expressions */
import Taro from '@tarojs/taro';
import CryptoJS from 'crypto-js';

// 识别需要过滤的参数
const needFiltrationParams: string[] = ['appid', 'secretkey', 'signCallback'];
export const guid = (): string => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
        const r = (Math.random() * 16) | 0;
        const v = c === 'x' ? r : (r & 0x3) | 0x8;
        return v.toString(16);
    });
};
function formatSignString(query: any, params: any): string {
    let strParam: string = '';
    let signStr: string = 'asr.cloud.tencent.com/asr/v2/';
    if (query.appid) {
        signStr += query.appid;
    }
    const keys: string[] = Object.keys(params);
    keys.sort();
    for (let i = 0; i < keys.length; i++) {
        strParam += `&${keys[i]}=${params[keys[i]]}`;
    }
    return `${signStr}?${strParam.slice(1)}`;
}

async function createQuery(query: SpeechRecognizerParams): Promise<any> {
    let params: any = {};
    const time: number = new Date().getTime();

    async function getServerTime(): Promise<string> {
        return new Promise<string>((resolve, reject) => {
            Taro.request({
                url: 'https://asr.cloud.tencent.com/server_time',
                method: 'GET',
                success: (res) => {
                    resolve(res.data);
                },
                fail: (error) => {
                    reject(error);
                }
            });
        });
    }

    const serverTime: string = await getServerTime();

    params.secretid = query.secretid || '';
    params.engine_model_type = query.engine_model_type || '16k_zh';
    params.timestamp = parseInt(serverTime, 10) || Math.round(time / 1000);
    params.expired = Math.round(time / 1000) + 24 * 60 * 60;
    params.nonce = Math.round(time / 100000);
    params.voice_id = guid();
    params.voice_format = query.voice_format || 1;

    const tempQuery = { ...query };
    for (let i = 0; i < needFiltrationParams.length; i++) {
        if (tempQuery.hasOwnProperty(needFiltrationParams[i])) {
            delete tempQuery[needFiltrationParams[i]];
        }
    }

    params = {
        ...tempQuery,
        ...params
    };
    return params;
}

// 获取签名原文
export async function getUrl(self: any, params: SpeechRecognizerParams): Promise<string> {
    if (!params.appid || !params.secretid) {
        self.isLog && console.log(self.requestId, '请确认是否填入账号信息');
        self.OnError('请确认是否填入账号信息');
        return '';
    }
    try {
        const urlQuery: any = await createQuery(params);
        const queryStr: string = formatSignString(params, urlQuery);
        let signature: string = '';
        if (params.signCallback) {
            signature = params.signCallback(queryStr);
        } else {
            signature = signCallback(params.secretkey, queryStr);
        }
        return `wss://${queryStr}&signature=${encodeURIComponent(signature)}`;
    } catch (error) {
        console.log(error);
        return '';
    }
}

/** 获取签名 start */

function toUint8Array(wordArray: any): Uint8Array {
    // Shortcuts
    const { words } = wordArray;
    const { sigBytes } = wordArray;

    // Convert
    const u8: Uint8Array = new Uint8Array(sigBytes);
    for (let i = 0; i < sigBytes; i++) {
        u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    }
    return u8;
}

// function Uint8ArrayToString(fileData: Uint8Array): string {
//     let dataString: string = '';
//     for (let i = 0; i < fileData.length; i++) {
//         dataString += String.fromCharCode(fileData[i]);
//     }
//     return dataString;
// }

// 签名函数示例
function signCallback(secretKey: string, signStr: string): string {
    const hash = CryptoJS.HmacSHA1(signStr, secretKey);
    const bytes = toUint8Array(hash);
    return Taro.arrayBufferToBase64(bytes);
}
