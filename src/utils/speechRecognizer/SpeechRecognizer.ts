/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-unused-expressions */

import type { SocketTask } from '@tarojs/taro';
import Taro from '@tarojs/taro';
import { getUrl } from './utils';
import type { LogInfo } from "@/types/common";

const TAG: string = 'SpeechRecognizer';
export default class SpeechRecognizer {
    socket: SocketTask | null;
    isSignSuccess: boolean; // 是否鉴权成功
    isSentenceBegin: boolean; // 是否一句话开始
    query: SpeechRecognizerParams;
    isRecognizeComplete: boolean; // 当前是否识别结束
    requestId: string | null;
    isLog: boolean;
    sendCount: number;
    getMessageList: string[];

    constructor(params: any, requestId: string, isLog: boolean) {
        this.socket = null;
        this.isSignSuccess = false;
        this.isSentenceBegin = false;
        this.query = {
            ...params
        };
        this.isRecognizeComplete = false;
        this.requestId = requestId;
        this.isLog = isLog;
        this.sendCount = 0;
        this.getMessageList = [];
    }

    // 暂停识别，关闭连接
    stop(): void {
        console.log('stop socket', this.socket, this.socket?.readyState);
        if (this.socket && this.socket.readyState === 1) {
            this.socket.send({
                data: JSON.stringify({ type: 'end' })
            });
            this.isRecognizeComplete = true;
        } else {
            // this.OnError({ code : 6003, message: '连接未建立或连接已关闭' });
            if (this.socket) {
                this.socket.close({});
            }
        }
    }

    // 建立websocket链接 data 为用户收集的音频数据
    async start(): Promise<void> {
        this.socket = null;
        this.getMessageList = [];
        const url: string = await getUrl(this, this.query);
        if (!url) {
            this.isLog && console.log(this.requestId, '鉴权失败', TAG);
            this.OnError('鉴权失败');
            return;
        }
        this.isLog && console.log(this.requestId, 'get ws url', url, TAG);
        this.OnLog({
            level: 'info',
            message: `get ws url ${url}`,
            data: {
                url,
                query: this.query
            }
        });
        this.socket = await Taro.connectSocket({
            url,
            header: {
                'content-type': 'application/json'
            },
            success: () => {
                if (this.isLog) console.log('connect success');
            },
            fail: (err: TaroGeneral.CallbackResult) => {
                if (this.isLog) console.log('connect fail', err);
                this.OnError(err);
            }
        });
        this.socket.onOpen((e) => {
            // 连接建立时触发
            this.isLog && console.log(this.requestId, '连接建立', e, TAG);
            this.onSocketOpen();
        });
        this.socket.onMessage(async (e: any) => {
            // 连接建立时触发
            try {
                this.getMessageList.push(JSON.stringify(e));
                const response = JSON.parse(e.data);
                this.isLog && console.log(this.requestId, JSON.stringify(response), TAG);
                if (response.code !== 0) {
                    if (this.socket?.readyState === 1) {
                        this.socket.close({
                            reason: '鉴权失败'
                        });
                    }
                    this.OnError(response);
                } else {
                    if (!this.isSignSuccess) {
                        this.OnRecognitionStart(response);
                        this.isSignSuccess = true;
                    }
                    if (response.final === 1) {
                        this.OnRecognitionComplete(response);
                        return;
                    }
                    if (response.result) {
                        if (response.result.slice_type === 0) {
                            this.OnSentenceBegin(response);
                            this.isSentenceBegin = true;
                        } else if (response.result.slice_type === 2) {
                            if (!this.isSentenceBegin) {
                                this.OnSentenceBegin(response);
                            }
                            this.OnSentenceEnd(response);
                        } else {
                            this.OnRecognitionResultChange(response);
                        }
                    }
                    this.isLog && console.log(this.requestId, response, TAG);
                }
            } catch (e) {
                this.isLog && console.log(this.requestId, 'socket.onmessage catch error', JSON.stringify(e), TAG);
            }
        });
        this.socket.onError((e: any) => {
            // 通信发生错误时触发
            this.isLog && console.log(this.requestId, 'socket error callback', e, TAG);
            this.socket?.close({});
            this.OnError(e);
        });
        this.socket.onClose((event: any) => {
            this.isLog && console.log(this.requestId, 'socket is close and error', JSON.stringify(event), TAG);
            this.onSocketClose();
            // if (!this.isRecognizeComplete) {
            //     this.OnError(event);
            // }

            this.isSignSuccess = false;
        });
    }

    close(): void {
        this.socket && this.socket.readyState === 1 && this.socket.close({});
    }

    // 发送数据
    write(data: any): void {
        try {
            if (!this.socket || String(this.socket.readyState) !== '1') {
                setTimeout(() => {
                    if (this.socket && this.socket.readyState === 1) {
                        this.socket.send({
                            data
                        });
                    }
                }, 100);
                return;
            }
            this.sendCount += 1;
            this.socket.send({
                data
            });
        } catch (e) {
            this.isLog && console.log(this.requestId, '发送数据 error catch', e, TAG);
        }
    }

    // 获取socket状态
    getSocketState(): number {
        return this.socket ? this.socket.readyState : 0;
    }

    onSocketOpen(): void {}
    onSocketClose(): void {}
    // 开始识别的时候
    OnRecognitionStart(res: any): void {}

    // 一句话开始的时候
    OnSentenceBegin(res: any): void {}

    // 识别结果发生变化的时候
    OnRecognitionResultChange(res: any): void {}

    // 一句话结束的时候
    OnSentenceEnd(res: any): void {}

    // 识别结束的时候
    OnRecognitionComplete(res: any): void {}

    // 识别失败
    OnError(res: any): void {}

    OnLog(log: LogInfo): void {}
}
