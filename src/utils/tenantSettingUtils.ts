import { Storage } from '@/common';
import { AppIdConsts } from '@/constants/appid';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import { tenantsettings } from '@/services/common';
import Taro from '@tarojs/taro';

const tenantSettingUtil = async (fn?: (path: string) => void) => {
    const miniProgram = Taro.getAccountInfoSync();
    let homePath = HomePath.DIALOG;
    if (miniProgram.miniProgram.appId === AppIdConsts.ybDoctor) {
        Storage.set(StorageEnvKey.HOME_PATH, homePath);
        if (fn) {
            fn(homePath);
        }
    } else {
        try {
            const res = await tenantsettings();
            const tenantSettings = res.data.data;

            // AI_PRACTISE,CHAT_PAGE
            if (tenantSettings.indexPage === 'AI_PRACTISE') {
                homePath = HomePath.AIEXERCISE;
            } else if (tenantSettings.indexPage === 'CHAT_PAGE') {
                homePath = HomePath.DIALOG;
            }
            Storage.set(StorageEnvKey.HOME_PATH, homePath);
            if (fn) {
                fn(homePath);
            }
        } catch (e) {
            Storage.set(StorageEnvKey.HOME_PATH, homePath);
            if (fn) {
                fn(homePath);
            }
        }
    }
};

export default tenantSettingUtil;
