import type { dialogProps, DialogProps } from '@antmjs/vantui/types/dialog';
import Taro from '@tarojs/taro';

const checkRecordPermission = (Dialog_: React.FunctionComponent<DialogProps> & dialogProps) => {
    return new Promise((resolve, reject) => {
        Taro.getSetting({
            success(settingRes) {
                if (settingRes.authSetting['scope.record']) {
                    console.log('录音已授权');
                    resolve(true);
                } else {
                    console.log('录音未授权');
                    Taro.authorize({
                        scope: 'scope.record',
                        success(res: any) {
                            resolve(true);
                            console.log('录音授权成功', res);
                        },
                        fail(res: any) {
                            console.log('录音授权失败', res);
                            Dialog_.confirm({
                                title: '语音权限',
                                message: '请在设置中打开麦克风权限',
                                async onConfirm() {
                                    const openSettingRes = await Taro.openSetting();
                                    if (openSettingRes.authSetting['scope.record']) {
                                        resolve(true);
                                        console.log('设置录音已授权');
                                    } else {
                                        reject(new Error('设置录音未授权'));
                                        console.log('设置录音未授权');
                                    }
                                }
                            }).then((value) => {
                                if (value === 'cancel') {
                                    reject('cancel');
                                }
                            });
                        }
                    });
                }
            },
            fail(res) {
                reject(res);
            }
        });
    });
};

// 判断是否是鸿蒙5.0.0以上
const checkHarmony = (): Promise<boolean> => {
    return new Promise((resolve) => {
        const systemInfo = Taro.getSystemInfoSync();
        const { platform, system } = systemInfo;

        if (platform === 'ohos') {
            resolve(true);
        } else {
            if (platform === 'devtools' && system === 'HarmonyOS') {
                resolve(true);
            } else {
                resolve(false);
            }
        }
    });
};

export { checkRecordPermission, checkHarmony };
