import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { getSetting, openSetting, requestSubscribeMessage, showModal, showToast } from '@tarojs/taro';
import dayjs from 'dayjs';

export const useSubscribe = () => {
    const handleSubscribe = (resolve: any) => {
        const lastSubscribe = Storage.get(StorageEnvKey.SUBSCRIBE_SCRIPT);
        // dayjs判断lastSubscribe是否是今天
        if (!lastSubscribe || !dayjs().isSame(dayjs(lastSubscribe), 'day')) {
            requestSubscribeMessage({
                tmplIds: [config.tmplScriptId],
                success: () => {
                    Storage.set(StorageEnvKey.SUBSCRIBE_SCRIPT, Date.now());
                },
                complete: (res: any) => {
                    resolve(res);
                    console.log('订阅结果', res);
                }
            });
        } else {
            console.log('当天已经订阅');
            resolve('当天已经订阅');
        }
        /* requestSubscribeMessage({
            tmplIds: [config.tmplScriptId],
            success: () => {
                Storage.set(StorageEnvKey.SUBSCRIBE_SCRIPT, Date.now());
            },
            complete: (res: any) => {
                resolve(res);
                console.log('订阅结果', res);
            }
        }); */
    };

    const checkSubscribe = () => {
        return new Promise((resolve, reject) => {
            getSetting({
                withSubscriptions: true,
                success(res) {
                    console.log('订阅消息设置', res);
                    if (res.subscriptionsSetting.mainSwitch) {
                        // 订阅消息总开关开启
                        if (res.subscriptionsSetting.itemSettings) {
                            // 用户同意总是保持是否推送消息的选择, 这里表示以后不会再拉起推送消息的授权
                            const moIdState = res.subscriptionsSetting.itemSettings[config.tmplScriptId];
                            if (moIdState === 'accept') {
                                // 同意订阅
                                handleSubscribe(resolve);
                            } else if (moIdState === 'reject') {
                                // 拒绝订阅
                                showModal({
                                    title: '订阅消息权限',
                                    content: '通知管理/订阅消息中打开接收通知',
                                    confirmText: '我知道了',
                                    showCancel: false
                                }).then(async (mRes: any) => {
                                    if (mRes.confirm) {
                                        const openSettingRes = await openSetting({
                                            withSubscriptions: true
                                        });
                                        console.log('openSettingRes1', openSettingRes.subscriptionsSetting);
                                        if (
                                            openSettingRes.subscriptionsSetting.itemSettings[config.tmplScriptId] ===
                                            'accept'
                                        ) {
                                            // 如果是接受，那就不跳转，让用户再点一次
                                            reject(openSettingRes);
                                        } else {
                                            resolve(openSettingRes);
                                        }
                                    }
                                });
                            } else if (moIdState === 'ban') {
                                showToast({
                                    title: '已被后台封禁',
                                    icon: 'none',
                                    duration: 3000
                                });
                                resolve(false);
                            }
                        } else {
                            // 没勾选始终选择
                            handleSubscribe(resolve);
                        }
                    } else {
                        // 总开关没打开，引导设置打开
                        showModal({
                            title: '订阅消息权限',
                            content: '请在通知管理/订阅消息中打开接收通知',
                            confirmText: '我知道了',
                            showCancel: false
                        }).then(async (mRes: any) => {
                            if (mRes.confirm) {
                                const openSettingRes = await openSetting({
                                    withSubscriptions: true
                                });
                                console.log('openSettingRes2', openSettingRes);
                                if (
                                    openSettingRes.subscriptionsSetting.itemSettings[config.tmplScriptId] === 'accept'
                                ) {
                                    // 如果是接受，那就不跳转，让用户再点一次
                                    reject(openSettingRes);
                                } else {
                                    resolve(openSettingRes);
                                }
                            }
                        });
                    }
                },
                fail(err) {
                    resolve(err);
                }
            });
        });
    };

    return { checkSubscribe };
};
