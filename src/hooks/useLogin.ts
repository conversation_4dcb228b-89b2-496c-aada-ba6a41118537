import { Storage } from '@/common';
import { OpenMode } from '@/constants/OpenMode';
import { StorageEnvKey } from '@/constants/storage';
import { getWxCode, getWxWorkCode, weworkLogin, wxlogin } from '@/services/user';
import type { UserInfo } from '@/types/common';
import { loginSuccess } from '@/utils/login';
import { useEvent } from '@hera/react-utils';
import Taro from '@tarojs/taro';
import { useState } from 'react';

interface ExtraData {
    url?: string;
    openMode?: string;
}
export const useLogin = (options: {
    onSuccess: (userInfo: UserInfo, data?: ExtraData) => void;
    onError: (error: any, data?: ExtraData) => void;
}) => {
    const { onSuccess, onError } = options;
    const [loading, setLoading] = useState(false);
    const login = useEvent(async (extraData?: ExtraData) => {
        setLoading(true);
        try {
            const isWework = Storage.get(StorageEnvKey.IS_WEWORK);
            console.log('isWework', isWework);
            if (isWework === '1') {
                // 企业微信小程序
                const { code } = await getWxWorkCode();
                console.log('wxwork code', code);
                const loginRes = await weworkLogin(code);
                const { data: loginData } = loginRes.data;

                if (loginRes.data.code === 200) {
                    loginSuccess(loginData); // 存token和用户信息
                    const { userInfo } = loginData;
                    Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息

                    onSuccess(userInfo, extraData);
                } else {
                    onError(new Error(loginRes.data.message), extraData);
                }
            } else {
                // 微信小程序
                try {
                    const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);
                    if (isEnterprise === 0) {
                        // 语贝登录
                        const { code } = await getWxCode();
                        const loginRes = await wxlogin(code);
                        const { data: loginData } = loginRes.data;

                        if (loginRes.data.code === 200) {
                            loginSuccess(loginData); // 存token和用户信息
                            const { userInfo } = loginData;

                            const phoneData = userInfo.phoneNumber || '';
                            if (!phoneData) {
                                const redirectUrl = extraData?.url ? `?url=${encodeURIComponent(extraData.url)}` : '';
                                Taro.redirectTo({
                                    url: `/pages/login/index/index${redirectUrl}`
                                });
                            } else {
                                // 有手机号则保存信息
                                Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息

                                onSuccess(userInfo, extraData);
                            }
                        } else {
                            onError(new Error(loginRes.data.message), extraData);
                        }
                    } else {
                        // 药企登录
                        const redirectUrl = extraData?.url ? `?url=${encodeURIComponent(extraData.url)}` : '';
                        console.log('userLogin redirectUrl', redirectUrl);
                        if (
                            extraData &&
                            (extraData.openMode === OpenMode.Script || extraData.openMode === OpenMode.Eman)
                        ) {
                            // 如果是脚本扫码进来，跳转注册
                            Taro.reLaunch({
                                url: `/pages/login/register/index${redirectUrl}`
                            });
                        } else {
                            Taro.redirectTo({
                                url: `/pages/login/index/index${redirectUrl}`
                            });
                        }
                    }
                } catch (error) {
                    console.log(error);
                    onError(error, extraData);
                    setLoading(false);
                }
            }
        } catch (error) {
            console.error('login error', error);
            onError(error, extraData);
            setLoading(false);
        }
    });

    return { login, loading };
};
