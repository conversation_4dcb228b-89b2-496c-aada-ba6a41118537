import { useEvent, useStorage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { getChatResponse } from '@/services/chat';
import Taro from '@tarojs/taro';
import { useRef, useState } from 'react';

/** 对话流处理 */
export function useChatStream(options: {
    onProcess: (content: string) => void;
    onEnd: () => void;
    onError?: (error: any) => void;
    onAbort?: () => void;
    onSuccess?: () => void;
    onHeadersReceived?: (header: any) => void;
}) {
    const { onEnd, onProcess, onError, onAbort, onSuccess, onHeadersReceived } = options;

    const [token] = useStorage<string>(StorageEnvKey.TOKEN);
    const [tenantId] = useStorage<string>(StorageEnvKey.TENANT_ID);
    const [isEnterprise] = useStorage<number>(StorageEnvKey.IS_ENTERPRISE);
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [loading, setLoading] = useState(false);
    const segmentCount = useRef(0);
    const requestTaskRef = useRef<any>();
    const segmentRequestTaskRef = useRef<any>();
    const segmentTimer = useRef<any>();

    const resetSegmentCount = useEvent(() => {
        segmentCount.current = 0;
    });

    const abortRequest = () => {
        console.log(requestTaskRef.current, 'request abort');
        if (requestTaskRef.current) {
            requestTaskRef.current.abort();
            requestTaskRef.current = null;
        }
        if(isWework === '1') {
            resetSegmentCount();
            if(segmentRequestTaskRef.current) {
                segmentRequestTaskRef.current.abort();
            }
            if(segmentTimer.current) {
                clearTimeout(segmentTimer.current);
            }

        }
        setLoading(false);
        onAbort && onAbort();
    };

    const run = useEvent(
        (options: { chatId: string; message: string; thinkFlag: boolean; responseId?: string; timeout?: number, audioFileName?: string }) => {
            const { chatId, message, responseId, thinkFlag, timeout = 60000, audioFileName } = options;
            console.log('options', options);
            requestTaskRef.current = null;
            segmentCount.current = 0;
            setLoading(true);
            const header: any = {
                'content-type': 'application/json',
                Authorization: token
            };
            if (tenantId) {
                header.tenantId = tenantId;
            }
            if (isEnterprise === 0) {
                header.appid = Taro.getAccountInfoSync().miniProgram.appId;
            }

            const requestSegment = async () => {
                if (segmentCount.current > 0) {
                    if (segmentCount.current >= timeout / 100) {
                        setLoading(false);
                        abortRequest();
                        onError?.({ errMsg: 'timeout' });
                        return;
                    }
                    segmentRequestTaskRef.current = Taro.request({
                        url: config.server + `/chat/getResponse`,
                        method: 'GET',
                        // responseType: 'arraybuffer',
                        data: { id: responseId },
                        header,
                        responseType: 'text',
                        success: (res) => {
                            if (res.data.code === 200) {
                                const textArr = res.data.data;
                                console.log('textArr', textArr);
                                if (textArr.length === 0) {
                                    segmentTimer.current = setTimeout(() => {
                                        segmentCount.current++;
                                        requestSegment();
                                    }, 100);
                                } else {
                                    clearTimeout(segmentTimer.current);
                                    if (textArr[textArr.length - 1] === '0\r\n') {
                                        console.log('onEnd()', textArr);
                                        setLoading(false);
                                        const t = textArr.join('').replace('0\r\n', '');
                                        if (t.includes('当前人数使用过多')) {
                                            abortRequest();
                                            onError?.({ errMsg: 'limit' });
                                        } else {
                                            t && onProcess(t);
                                            segmentCount.current = 0;
                                            onEnd();
                                        }
                                    } else {
                                        console.log('onProcess(textArr)', textArr);
                                        const t = textArr.join('');
                                        if (t.includes('"code":500')) {
                                            console.log('500', t);
                                            setLoading(false);
                                            abortRequest();
                                            onError?.({ errMsg: '500' });
                                        } else if (t.includes('"code":401')) {
                                            console.log('401', t);
                                            setLoading(false);
                                            abortRequest();
                                            onError?.({ errMsg: '401' });
                                        } else if (t.includes('当前人数使用过多')) {
                                            setLoading(false);
                                            abortRequest();
                                            onError?.({ errMsg: 'limit' });
                                        } else {
                                            const t = textArr.join('');
                                            t && onProcess(t);
                                            segmentTimer.current = setTimeout(() => {
                                                requestSegment();
                                            }, 100);
                                        }
                                    }
                                }
                            } else {
                                setLoading(false);
                                abortRequest();
                                onError?.({ errMsg: '请求错误', res });
                            }
                        },
                        fail: (error) => {
                            setLoading(false);
                            abortRequest();
                            onError?.({ errMsg: '请求错误', error });
                        }
                    });
                };

            };

            requestTaskRef.current = Taro.request({
                url: `${config.server}/chat/${chatId}/completions`,
                // url: `http://172.16.8.188:7001/api/v1/chat/${chatId}/completions`,
                method: 'POST',
                responseType: isWework === '0' ? 'arraybuffer' : 'text',
                enableChunked: isWework === '0',
                header,
                timeout,
                data: {
                    input: message,
                    responseId,
                    thinkFlag,
                    audioFileName: audioFileName || null,
                    type: isWework === '0' ? 2 : 3 // 响应类型，1-sseEmitter， 2-responseBodyEmitter， 3-http轮询获取
                },
                fail: (res) => {
                    console.log('chat fail', res);
                    setLoading(false);
                    if(isWework === '1') {
                        resetSegmentCount();
                        if(segmentRequestTaskRef.current) {
                            segmentRequestTaskRef.current.abort();
                        }
                        if(segmentTimer.current) {
                            clearTimeout(segmentTimer.current);
                        };

                    }
                    onError?.(res);
                },
                success: (res) => {
                    console.log('chat success', res);
                    onSuccess && onSuccess();
                }
            });
            console.log('requestTaskRef.current', requestTaskRef.current);
            requestTaskRef.current.onHeadersReceived((res) => {
                onHeadersReceived && onHeadersReceived(res);
            });
            if (isWework === '1') {
                segmentCount.current = 1;
                requestSegment();
            }
            if (isWework === '0') {
                // @ts-ignore
                requestTaskRef.current.onChunkReceived(({ data }) => {
                    const uint8Array = new Uint8Array(data);
                    let text = String.fromCharCode.apply(null, uint8Array as unknown as number[]);
                    text = decodeURIComponent(escape(text));
                    // console.log('onChunkReceived-text', text);
                    if (text === '0\r\n' || text.indexOf('0\r\n') !== -1) {
                        // console.log('onEnd()', text);
                        setLoading(false);
                        onProcess(text.replace('0\r\n', ''));
                        onEnd();
                    } else {
                        // 判断text中是否带有'"code": 500'文本
                        // console.log('onProcess(text)', text);
                        if (text.includes('"code":500')) {
                            console.log('500', text);
                            setLoading(false);
                            onError?.({ errMsg: '500' });
                        } else if (text.includes('"code":401')) {
                            console.log('401', text);
                            setLoading(false);
                            onError?.({ errMsg: '401' });
                        } else {
                            onProcess(text);
                        }
                    }
                });
            }
        }
    );
    return { loading, run, resetSegmentCount, abortRequest };
}
