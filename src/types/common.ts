/** 分页类型 */
export interface QueryPagination {
    /** 当前页 */
    pageNo?: number;
    /** 每页数量 */
    pageSize?: number;
    /** 模糊查询 */
    keyword?: number;
}

/** 请求响应 */
export type Response<T = void> = Promise<
    {
        data: {
            code: number;
            data: T;
            message: string;
        };
    } & Record<string, any>
>;

/** 分页响应数据 */
export type Pagination<T = any> = {
    size: number;
    current: number;
    pages: number;
    records: T[];
    total: number;
};
/** 分页请求响应 */
export type PagingResponse<T = any> = Response<Pagination<T>>;

/** 用户信息 */
export interface UserInfo {
    phoneNumber: string;
    avatar: string;
    createTime: string;
    deleted: boolean;
    id: string;
    name: string;
    openId: string;
    updateTime: string;
    companyName: string;
}

export interface VoList {
    tenantId: string;
    companyName: string;
    domain: string;
    appid: string;
    enterpriseFlag: boolean;
}

export interface TenantList {
    loginStr: string;
    voList: VoList[];
}

export interface TtsVoiceQuery {
    name: string;
    type: number;
    text: string;
    mainId: string | null;
}

export interface LogInfo {
    level: 'error' | 'warn' | 'info' | 'debug' | 'trace';
    message: string;
    data?: any;
    time?: string;
}

export interface FeedbackData {
    appId: string;
    version: string;
    brand: string;
    model: string;
    system: string;
    platform: string;
    wxVersion: string;
    SDKVersion: string;
    environment: string;
    path: string;
    client: 'h5' | 'wechat';
    chatId: string;
    name: string;
    company: string;
    phone: string;
    microphoneAuthorized: boolean;
    description: string;
    logs: LogInfo[];
}

export interface WeappSettingParam {
    indexPage: string;
    aiPractisePage: string;
    chatPage: string;
}
