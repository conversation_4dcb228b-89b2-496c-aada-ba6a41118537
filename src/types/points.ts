export interface UserPointStaticsVo {
    monthPoints: number;
    totalPoints: number;
    consumePoints: number;
}

/**
 *  任务名称: string;
    积分时间: string;
    完成任务前的总积分: number;
    完成任务所获得的积分: number;
    完成任务后的总积分: number;
 */
export interface PointRecordVo {
    missionName: string;
    pointTime: string;
    totalPointsBefore: number;
    points: number;
    totalPointsAfter: number;
}

export interface RankVo {
    userId: number;
    userName: string;
    points: number;
    avatar: string;
}

export interface PointMissionVo {
    missionName: string;
    completeCount: number;
    targetCount: number;
    missionDesc: string;
    points: number;
    userPointType: string; // DAILY_LOGIN,MONTH_SIGN_IN_DAYS,DAILY_PRACTISE,DAILY_PRACTISE_SCORE
}

export interface MonthSignInVo {
    date: string;
    signInFlag: boolean;
}
