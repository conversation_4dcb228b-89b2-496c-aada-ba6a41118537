export interface LevelStageItemVO {
    id: string;
    name: string;
    levelStageNum: number;
    startDate: string;
    endDate: string;
    deptName: string;
    progress: number;
}

export interface LevelStageDetailVO {
    eventLevel: EventLevel;
    eventLevelStageList: EventLevelStageList[];
}

export interface EventLevel {
    id: string;
    name: string;
    levelStageNum: number;
    startDate: string;
    endDate: string;
    deptName: string;
    progress: number;
}

export interface EventLevelStageList {
    openTime: string;
    timeLimitFlag: boolean;
    id: string;
    levelId: string;
    finishFlag: boolean;
    lockFlag: boolean;
    unlockTime: string;
    unlockCondition: UnlockCondition[];
}

export interface UnlockCondition {
    id: string;
    levelStageId: string;
    time: number;
    score: number;
    currentTime: number;
    scriptId: string;
    scriptName: string;
    errorMessage: string;
}
