import type { EmanVO } from './chat';

export interface SceneVo {
    id: string | number;
    createTime: string;
    updateTime: string;
    scene: string;
}

export interface EManFormVo {
    name: string;
    avatar: string;
    background: string;
    personality: string;
    personalityArray: string[];
    tone: string;
    occupation: string;
    skill: string;
}
export interface EManVo extends EManFormVo {
    id: string | number;
    createTime: string;
    updateTime: string;
    department: string;
    title: string;
    organization: string;
    scene: SceneVo;
    gender: number;
}

export interface ProfessionalCertificationDetailVo {
    id: string | number;
    createTime: string;
    updateTime: string;
    name: string;
    department: string;
    status: number;
    title: string;
    organization: string;
    file: [];
    videoUrl: string | null;
    zipFileUrl: string | null;
}

export interface EManDetailVo extends EmanVO {
    number: string | number;
    followerNum: number;
    chatNum: number;
    status: number;
    professionalCertificationDetail: ProfessionalCertificationDetailVo;
}
export interface VoiceVo {
    name: string;
    speed: number | null;
    voiceType: number | null;
    volume: number | null | string;
}

export interface EmanAvatarVo {
    show3dFlag: string | null;
    avatar: string;
    background: string;
    videoUrl: string | null;
    zipFileUrl: string | null;
}
