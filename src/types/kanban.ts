import type { QueryPagination } from './common';

export interface UserDataAnalyseOV {
    uv: number;
    averageScore: number;
    averageTime: number;
    averageCount: number;
}
export interface UserDataAnalyseParams {
    startTime: string;
    endTime: string;
    deptIds?: number[];
    userId?: string;
}

export interface UserDataItem {
    id: number;
    userId: number;
    name: string;
    totalTime: number;
    totalCount: number;
    maxScore: number;
    avgScore: number;
    deptId: number;
    deptName: string;
    lastPractiseTime: string;
}

export interface UserDataParams extends QueryPagination {
    startTime: string;
    endTime: string;
    deptIds?: number[];
    isAsc?: 'desc' | 'asc';
    orderByColumn?: string;
}

export interface UserPointParams extends QueryPagination {
    startTime: string;
    endTime: string;
    deptIds?: number[];
    isAsc?: 'desc' | 'asc';
    orderByColumn?: string;
}

export interface UserPointItem {
    id: number;
    avatar: string;
    name: string;
    deptId: number;
    deptName: string;
    points: number;
    filterPoints: number;
}

export interface UserDataDetailAnalyseVo {
    name: string;
    avatar: string;
    maxScore: number;
    averageScore: number;
    totalTime: number;
    averageTime: number;
    totalCount: number;
}

export interface UserDetailChatParams extends QueryPagination {
    startTime: string;
    endTime: string;
    userId: string;
}

export interface UserDetailChatItem {
    id: number;
    status: number;
    userId: number;
    userName: string;
    scriptName: string;
    scriptType: number;
    timeLong: number;
    score: number;
    createTime: string;
}
