import type { CosConfig } from '@/services/cos';

export interface EmanChatHistoryVo {
    chatId: number;
    chatReportId: number;
    scriptName: string;
    createTime: string;
    finishTime: string;
    score: number;
    status: number;
}
export interface EmanVO {
    show3dFlag: string;
    avatar: string;
    background: string;
    createTime: string;
    department: string;
    id: string;
    name: string;
    occupation: string;
    organization: string;
    personality: string;
    title: string;
    tone: string;
    skill: string;
    updateTime: string;
    chatList: EmanChatHistoryVo[];
    status: number;
    type: number; // 1-情景演练 2-企业智脑
    gender: number;
    scene: {
        id: string;
        scene: string;
    };
    zipFileUrl: string;
    videoUrl: string;
    introductionType: number; // 开场白，0-关闭，1-立刻开场，2-延迟开场
    introductionDelay: number; // 开场白延迟时间（秒）
    introduction: string; // 开场白内容
    selfEmanFlag?: boolean; // 是否是自己的e人
    emanDifficult?: number; // e人难度
}
export interface Question {
    id: number;
    question: string;
}
export interface Time {
    hour: number;
    minute: number;
    second: number;
    nano: number;
}
export interface ScriptVO {
    backdrop: string;
    createTime: string;
    deleted: boolean;
    goal: string;
    id: string;
    location: string;
    name: string;
    product: string;
    time: string;
    timeLimit: number;
    trust: number;
    updateTime: string;
    randomFlag: number; // 0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
    randomNum: number;
    type: number; // 脚本类型 1-技巧 2-问答
    question: Question[];
    applicableObject: string;
    applicable: boolean; // 是否适用e人
    visitObject: string; // 拜访对象
    department: string;
    ppt?: any;
    /**
     * 幻灯片评分方式 1-演练要求+语义识别 2-关键词+语义识别 3-关键词+关键词匹配
     */
    pptScoringType: 1 | 2 | 3;
    lockFlag: boolean;
    disableTextInputFlag: boolean;
    aiHelpFlag: boolean;
    timeLimitType?: number; // 1限制整体时长 2限制单题时长
}

export interface SceneVO {
    id: string;
    scene: string;
}

export interface QaItem {
    qaId: string;
    answer: string;
    question: string;
    chatQAId?: string;
    chatHistoryId?: string;
    ossConfig?: CosConfig;
}

export interface PPTItem {
    id: string;
    orderNum: number;
    imageUrl: string;
    requirement: string;
    imageUrlback?: string;
    answer?: string;
    audioUrl?: string;
    done: boolean;
}

export interface ChatVO {
    chatReportId: string;
    createTime: string;
    deleted: boolean;
    done: boolean;
    emanId: string;
    eman: EmanVO;
    qaList: QaItem[];
    pptList: PPTItem[];
    id: string;
    scene: SceneVO;
    sceneId: string;
    script: ScriptVO;
    scriptId: string;
    updateTime: string;
    userId: string;
    type: number;
    introductionType: number; // 开场白，0-关闭，1-立刻开场，2-延迟开场
    introductionDelay: number; // 开场白延迟时间（秒）
    introduction: string; // 开场白内容
}

export enum RoleEnum {
    USER = 'user',
    ASSISTANT = 'assistant'
}

export interface HistoryVO {
    role: RoleEnum;
    content: string;
    reasoningContent?: string;
    avatar?: string;
    id?: string;
    createTime?: string;
    qaId?: string;
    audioFlag?: boolean;
    audioUrl?: string;
    upload?: boolean;
}

export enum ChatActionSheetType {
    /** 返回时确认结束对话 */
    FINISH = 1,
    /** 对话已关闭 */
    OFF = 2,
    /** 对话中断 */
    INTERRUPT = 3,
    // 问答完成
    COMPLETE = 4
}

export interface ChatHistoryVO {
    show3dFlag: string;
    id: string;
    createTime: string;
    type?: any;
    chatReportId: string;
    name: string;
    avatar: string;
    score: null;
    status: number;
    done: boolean;
    emanType: number; //  1-情景演练 2-企业智脑
    lastChatText: string;
    zipFileUrl: string;
    videoUrl: string;
}

export interface ReportVo {
    voiceRateDimension?: any;
    id: string;
    contentScore?: any;
    /**
     * 内容完整性分数
     */
    integrityScore?: number;
    /**
     * 流畅度分数
     */
    voiceRateScore?: number;
    createTime: string;
    updateTime: string;
    score: number;
    sumup: string;
    chatId: string;
    dimension: string;
    chatStartTime: string;
    chatEndTime: string;
    status: number;
    emanName: string;
    scene: string;
    scriptDetailVO: ScriptVO;
    dimensionArray: any[];
}
export interface certificationVo {
    id?: any;
    createTime?: string;
    updateTime?: string;
    name: string;
    department: string;
    title: string;
    organization: string;
    status?: number;
    file: string[];
}

export interface ChatParamVo {
    emanId: number;
    sceneId: number;
    scriptDetail: ScriptVO & { question: Question };
}
export interface UnlockCondition {
    scriptId: string;
    scriptName: string;
    needNum: number;
    currentNum: number;
    score: number;
    permissionFlag: boolean;
}
export interface ScriptUnlockConditionVo {
    name: string;
    unlockCondition: UnlockCondition[];
}

export interface PPTSummaryVo {
    answer?: string;
    audioUrl?: string;
}
