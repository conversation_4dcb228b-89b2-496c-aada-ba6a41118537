// 主题色
@primaryColor: var(--primary-color, #1989fa);

// 强调/正文标题
@textColor-1: #323233;
// 次强调/正文标题
@textColor-2: #646566;
// 次要信息
@textColor-3: #969799;
// 置灰信息
@textColor-4: #c1c6c6;
// 线条/深
@textColor-5: #e0e0e0;
// 线条/输入框描边
@textColor-6: #ededed;
// 线条/浅
@textColor-7: #f0f0f0;
// 填充/深
@textColor-8: #f7f7f7;
// 填充/浅
@textColor-9: #fafafa;

// 辅助文案
@fontSize-1: 22px;
// 次要文案
@fontSize-2: 24px;
// 正文-常规
@fontSize-3: 28px;
// 标题-小/按钮
@fontSize-4: 32px;
// 标题-中
@fontSize-5: 34px;
// 标题-大
@fontSize-6: 36px;

// 字重
@fontWeight-1: 500;
@fontWeight-2: 400;
