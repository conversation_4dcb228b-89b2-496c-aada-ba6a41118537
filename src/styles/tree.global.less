
.lhhui-tree {
    &-node {
      &-content {
        display: flex;
        align-items: center;
        margin-bottom: 20px;
      }
      &-expand {
        width: 40px;
        height: 40px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 4px;
        // &:hover {
        //   background-color: #f0f0f0;
        // }
        &-icon {
          width: 16px;
          height: 26px;
          color: #333;
          transition: transform 0.3s;
          transform: rotate(0deg);
          &-show {
            transform: rotate(90deg);
          }
        }
        &-placeholder {
          width: 24px;
          height: 24px;
        }
      }
      &-title {
        flex:1;
        // margin-left: 20px;
        font-size: 32px;
        padding: 6px 12px;
        border-radius: 4px;
        cursor: pointer;
        color: #272C47;
        // &:hover {
        //   background-color: #f0f0f0;
        // }
        // &-selected {
        //   background-color: rgba(158, 205, 244, 0.3);
        //   &:hover {
        //     background-color: rgba(158, 205, 244, 0.4);
        //   }
        // }
        // &-disabled {
        //   color: rgba(0, 0, 0, 0.28);
        //   cursor: not-allowed;
        //   // &:hover {
        //   //   background-color: transparent;
        //   // }
        // }
      }
      &-checkbox {
        width: 38px;
        height: 38px;
      }
      &-children {
        padding-left: 48px;
        overflow-y: hidden;
        transition: max-height 0.3s ease;
      }
    }
  }
  