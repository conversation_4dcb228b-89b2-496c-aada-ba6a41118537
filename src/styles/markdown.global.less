//.h5-h1,
//.h5-h2,
//.h5-h3,
//.h5-h4,
//.h5-h5,
//.h5-h6 {
//    margin: 0.8em 0 0.5em;
//    font-weight: bold;
//    line-height: 1em;
//}
//.h5-h1 {
//    font-size: 2em;
//}
//.h5-h2 {
//    font-size: 1.5em;
//}
//.h5-h3 {
//    font-size: 1.2em;
//}
//.h5-h4 {
//    font-size: 1.1em;
//}
//.h5-h5 {
//    font-size: 1em;
//}
//.h5-h6 {
//    font-size: 0.9em;
//}
//
//.h5-blockquote {
//    margin: 0;
//    padding-left: 1em;
//    color: #666;
//    border-left: 4px #eee solid;
//}
//.h5-hr {
//    display: block;
//    height: 2px;
//    margin: 10px 0;
//    padding: 0;
//    border: 0;
//    border-top: 1px solid #aaa;
//    border-bottom: 1px solid #eee;
//}
//.h5-pre,
//.h5-code,
//.h5-kbd,
//.h5-samp {
//    color: #000;
//    font-size: 0.98em;
//}
//.h5-pre {
//    white-space: pre;
//    white-space: pre-wrap;
//    word-wrap: break-word;
//}
//
//.h5-b,
//.h5-strong {
//    font-weight: bold;
//}
//.h5-ul,
//.h5-ol {
//    margin: 1em 0;
//    padding: 0 0 0 1.5em;
//}
//.h5-ol {
//    list-style-type: decimal;
//}
//.h5-li {
//    display: list-item;
//}
//.h5-li .h5-p:last-child {
//    margin: 0;
//}
//.h5-dd {
//    margin: 0 0 0 2em;
//}
//
//.h5-table {
//    border-collapse: collapse;
//    border-spacing: 0;
//}
//.h5-td {
//    vertical-align: top;
//}
//.h5-img {
//    max-width: 100%;
//    height: 224px;
//    margin: 8px 0;
//}
//.doting {
//    opacity: 0;
//    animation: loading-dot 1.5s infinite;
//}
//
//@keyframes loading-dot {
//    0% {
//        opacity: 0;
//    }
//
//    50% {
//        opacity: 1;
//    }
//
//    100% {
//        opacity: 0;
//    }
//}
//
//.img-block {
//    width: 420px;
//    height: 224px;
//    margin: 8px 0;
//    background-color: #ccc;
//}
