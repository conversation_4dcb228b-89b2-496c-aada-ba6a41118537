import { isEmpty, setConfig, setMessage, Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
// import { getWxCode, wxlogin } from '@/services/user';
// import { loginSuccess } from '@/utils/login';
import { Toast } from '@antmjs/vantui';
import Taro from '@tarojs/taro';
const Toast_ = Toast.createOnlyToast();

const tipModal = (content: string) => {
    Toast_.show(content);
};

const requestInstance = {
    request: Taro.request
};

setConfig({
    requestType: 'taro',
    headers: () => {
        // 请求头配置，可以配置 token等
        const tenantId = Storage.get(StorageEnvKey.TENANT_ID);
        const headers: any = {
            biz: config.biz
        };

        if (tenantId) {
            headers.tenantId = tenantId;
        }

        const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);
        if (isEnterprise === 0) {
            headers.appid = Taro.getAccountInfoSync().miniProgram.appId;
        }

        const token = Storage.get(StorageEnvKey.TOKEN);

        if (!isEmpty(token)) {
            headers.Authorization = token;
        }
        return headers;
    },
    errorCode: {
        // 401: async (res, retryRequest) => {
        //     // const loginRes = await login();
        //     // const { data } = loginRes.data;
        //     const isEnterprise = Storage.get(StorageKey.IS_ENTERPRISE);
        //     if (isEnterprise === 1) {
        //         // 药企
        //         Taro.navigateTo({
        //             url: '/pages/login/relogin/index'
        //         });
        //     } else {
        //         const resToken = await getWxCode();
        //         const loginRes = await wxlogin(resToken.code);
        //         if (loginRes.data.code === 200) {
        //             const { data } = loginRes.data;
        //             loginSuccess(data);
        //             retryRequest();
        //         }
        //     }
        // }
    },
    resKeys: {
        code: 'statusCode',
        data: 'data',
        message: (data) => {
            const { fieldMsgs } = data;
            if (fieldMsgs && Array.isArray(fieldMsgs)) {
                return fieldMsgs.map((item) => item.message);
            }
            if (typeof fieldMsgs === 'string') {
                return fieldMsgs;
            }
            return data.message;
        }
    },
    baseURL: () => {
        if (Taro.getEnv() === Taro.ENV_TYPE.WEB) {
            return '';
        }
        return config.server;
    },
    requestInstance
});

setMessage({
    loading: () => {
        Taro.showLoading({
            mask: true,
            title: '加载中...'
        });
    },
    error: (content) => {
        if (Array.isArray(content)) {
            const text = [...new Set(content)].join('；');
            tipModal(text);
        } else {
            tipModal(content);
        }
    },
    destroy: () => {
        Taro.hideLoading();
    }
});
