import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import type { Response, TtsVoiceQuery, WeappSettingParam } from '@/types/common';
import type { EmanAvatarVo, SceneVo, VoiceVo } from '@/types/eman';
import request from './requestTaro';

import Taro from '@tarojs/taro';

export function getServerTime() {
    return request({
        url: `/common/server-time`
    }) as Response<string>;
}
// 上传图片接口
export function upload(url: string) {
    const token = Storage.get(StorageEnvKey.TOKEN);
    return new Promise<string>((resolve, reject) => {
        console.log('上传图片', url);
        Taro.uploadFile({
            url: `${config.server}/upload`,
            filePath: url,
            name: 'file',
            header: {
                'content-type': 'application/json',
                Authorization: token
            },

            success(res) {
                console.log('上传成功', res);
                const { data } = res;
                resolve(data);
            },
            fail(res) {
                console.log('上传失败', res);
                reject(res);
            }
        });
    });
}

/** 随机昵称 */
export function getRandomName() {
    return request({
        url: `/common/person-name`
    }) as Response<string>;
}
/** 判断页面展示 */
export function tenantsettings() {
    return request({
        url: `/common/tenant-settings`
    }) as Response<WeappSettingParam>;
}
/** 性格列表 */
export function getNatureList() {
    return request({
        url: `/common/person-nature`
    }) as Response<string[]>;
}

/** 男声音 */
export function getManVoice() {
    return request({
        url: '/common/men-voice'
    }) as Response<VoiceVo[]>;
}
/** 女声音 */
export function getWoManVoice() {
    return request({
        url: '/common/women-voice'
    }) as Response<VoiceVo[]>;
}
/** 头像背景 */
export function getAvatar(url: string) {
    return request({
        url
    }) as Response<EmanAvatarVo[]>;
}

export function getRandomScene(occupation: string, title: string) {
    return request({
        url: `/scene/random?occupation=${occupation}&title=${title}`
    }) as Response<SceneVo>;
}

export function ttsVoice(data: TtsVoiceQuery) {
    return request({
        url: '/common/tts-voice',
        method: 'GET',
        data,
        showError: false,
        timeout: 12000
    }) as Response<string>;
}

// 上传日志
export function uploadLog(log: string) {
    return request({
        url: '/common/miniapp-error-log',
        method: 'POST',
        data: {
            log
        }
    }) as Response<boolean>;
}
