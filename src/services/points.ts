import type { PagingResponse, QueryPagination, Response } from '@/types/common';
import type { MonthSignInVo, PointMissionVo, PointRecordVo, RankVo, UserPointStaticsVo } from '@/types/points';
import request from './requestTaro';

// 获取积分统计
export function getPointsStat() {
    return request({
        url: '/point/userPointStatics'
    }) as Response<UserPointStaticsVo>;
}

export function getPointsList(query?: QueryPagination) {
    return request({
        url: '/point/recordList',
        data: query
    }) as PagingResponse<PointRecordVo>;
}

// 获取排行榜
export function getRankList(period: string) {
    return request({
        url: '/point/userPointRankList',
        data: {
            period
        }
    }) as Response<RankVo[]>;
}

export function getPointMission() {
    return request({
        url: '/point/pointMission'
    }) as Response<PointMissionVo[]>;
}

export function daySignIn() {
    return request({
        url: '/signIn',
        method: 'POST'
    }) as Response<boolean>;
}

// 每月签到明细
export function monthSignIn() {
    return request({
        url: '/userMonthSignIn',
        method: 'POST'
    }) as Response<MonthSignInVo[]>;
}
