import type { PagingResponse, Response } from '@/types/common';
import type {
    UserDataAnalyseOV,
    UserDataAnalyseParams,
    UserDataDetailAnalyseVo,
    UserDataItem,
    UserDataParams,
    UserDetailChatItem,
    UserDetailChatParams,
    UserPointItem,
    UserPointParams
} from '@/types/kanban';
import type { PointRecordVo } from '@/types/points';
import qs from 'qs';
import request from './requestTaro';
// 获取学员数据统计
export const getUserDataAnalyse = (param: UserDataAnalyseParams) => {
    // console.log('123', ids);
    const url = `/kanban/userDataAnalyse?${qs.stringify(param)}`;
    // // 把param拼接到url上
    // url += `?${Object.keys(param)
    //     .map((key: any) => `${key}=${param[key]}`)
    //     .join('&')}`;
    // if (ids && ids.length > 0) {
    //     // id用&deptIds[0]=1&deptIds[1]=2拼接

    //     const queryString = ids.map((id, index) => `${encodeURIComponent(`deptIds[${index}]`)}=${id}`).join('&');
    //     url += `&${queryString}`;
    // }
    return request({
        url,
        method: 'GET'
    }) as Response<UserDataAnalyseOV>;
};

export const getUserDataList = (param: UserDataParams) => {
    const url = `/kanban/getUserChatStatics?${qs.stringify(param)}`;
    return request({
        url,
        method: 'GET'
    }) as PagingResponse<UserDataItem>;
};

export const userPointList = (param: UserPointParams) => {
    return request({
        url: `/point/userPointList?${qs.stringify(param)}`,
        method: 'GET'
    }) as PagingResponse<UserPointItem>;
};

// 学员详情统计
export const getUserDataDetailAnalyse = (param: UserDataAnalyseParams) => {
    return request({
        url: '/kanban/userDataDetailAnalyse',
        method: 'GET',
        data: param
    }) as Response<UserDataDetailAnalyseVo>;
};

// 学员对话记录
export const getUserChatList = (param: UserDetailChatParams) => {
    return request({
        url: '/kanban/userChatRecord',
        method: 'GET',
        data: param
    }) as PagingResponse<UserDetailChatItem>;
};

// 积分记录
export const getUserPointRecord = (param: UserDetailChatParams) => {
    return request({
        url: '/point/recordList',
        method: 'GET',
        data: param
    }) as PagingResponse<PointRecordVo>;
};
// 查询部门
export function getDepartment() {
    return request({
        url: '/dept/treeSelected',
        method: 'POST'
    }) as Response<any>;
}
