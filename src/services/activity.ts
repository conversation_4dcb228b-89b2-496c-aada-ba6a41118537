import type { LevelStageDetailVO, LevelStageItemVO } from '@/types/activity';
import type { PagingResponse, QueryPagination, Response } from '@/types/common';
import request from './requestTaro';

export function getCheckpointList(query?: QueryPagination) {
    return request({
        url: '/event-level/page',
        data: query
    }) as PagingResponse<LevelStageItemVO>;
}
export function getCheckpointDetail(id: string) {
    return request({
        url: `/event-level/${id}`
    }) as Response<LevelStageDetailVO>;
}
