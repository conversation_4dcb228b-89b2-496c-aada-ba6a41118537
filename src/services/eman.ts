import type { PagingResponse, QueryPagination, Response } from '@/types/common';
import type { EManDetailVo, EManFormVo, EManVo } from '@/types/eman';
import request from './requestTaro';

export function getMyEMan(query?: QueryPagination) {
    return request({
        url: `/eman/my/page`,
        method: 'GET',
        data: query
    }) as PagingResponse<EManVo>;
}
export function getMyEManDetail(id: string) {
    return request({
        url: `/eman/my/${id}`,
        method: 'GET'
    }) as Response<EManDetailVo>;
}

export function createEMan(data: EManFormVo) {
    return request({
        url: '/eman',
        method: 'POST',
        data
    }) as Response<string>;
}
export function updateEMan(data: { id: any } & EManFormVo) {
    return request({
        url: `/eman/${data.id}`,
        method: 'POST',
        data
    }) as Response<string>;
}
// 上架
export function upEMan(id: string | number) {
    return request({
        url: `/eman/${id}`,
        method: 'PUT'
    }) as Response<string>;
}
// E人审核失败原因
export function getAuditFailReason(id: string | number) {
    return request({
        url: `/eman/auditFailReason/${id}`,
        method: 'GET'
    }) as Response<string>;
}
export function downEMan(id: string | number) {
    return request({
        url: `/eman/${id}`,
        method: 'DELETE'
    }) as Response<string>;
}

export function eManStatus(id: string) {
    return request({
        url: `/eman/${id}/status`,
        method: 'GET'
    }) as Response<boolean>;
}
