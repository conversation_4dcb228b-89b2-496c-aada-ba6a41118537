import type { certificationVo } from '@/types/chat';
import type { Response } from '@/types/common';
import request from './requestTaro';

export function getProfessionalTitle() {
    return request({
        url: '/common/professional-title'
    }) as Response<{ title: string; subTitle: string[] }[]>;
}

export function getDepartment() {
    return request({
        url: '/common/department'
    }) as Response<{ name: string; subName: string[] }[]>;
}

export function getCertificationById(id: string) {
    return request({
        url: `/professional_certification/${id}`
    }) as Response<certificationVo>;
}

export function putCertification(data: certificationVo) {
    return request({
        url: `/professional_certification/${data.id}`,
        method: 'PUT',
        data
    }) as Response<any>;
}

export function postCertification(data: certificationVo) {
    return request({
        url: `/professional_certification`,
        method: 'POST',
        data
    }) as Response<any>;
}

export function postCertificationVerify(id: string) {
    return request({
        url: `/professional_certification/verify/${id}`,
        method: 'POST'
    }) as Response<any>;
}

export function getAuditFailReason(id: string) {
    return request({
        url: `/professional_certification/auditFailReason/${id}`
    }) as Response<any>;
}

export function deleteCertificationVerify(id: string) {
    return request({
        url: `/professional_certification/revert/${id}`,
        method: 'DELETE'
    }) as Response<any>;
}
