import { Storage } from '@/common';
import { StorageEnvKey } from '@/constants/storage';
import type {
    ChatHistoryVO,
    ChatParamVo,
    ChatVO,
    EmanVO,
    HistoryVO,
    PPTSummaryVo,
    ReportVo,
    ScriptUnlockConditionVo,
    ScriptVO
} from '@/types/chat';
import type { PagingResponse, QueryPagination, Response } from '@/types/common';
import request from './requestTaro';

export function getPageEman(query: QueryPagination) {
    return request({
        url: '/eman/page',
        data: query
    }) as PagingResponse<EmanVO>;
}
export function getRecentEman() {
    return request({
        url: '/eman/recentChatEman'
    }) as Response<
        (EmanVO & {
            scene: {
                id: string;
                scene: string;
            };
        })[]
    >;
}
export function getEnam(id: string) {
    return request({
        url: `/eman/${id}`
    }) as Response<EmanVO>;
}

export function getAllScript(emanId?: string, type?: any, productName?: any) {
    const data: any = {};
    if (type !== undefined && type !== null) {
        data.type = type;
    }
    if (productName) {
        data.product = productName;
    }
    if (emanId) {
        data.emanId = emanId;
    }
    return request({
        url: '/script/all',
        data
    }) as Response<ScriptVO[]>;
}

export function getScript(id: string) {
    return request({
        url: `/script/${id}`
    }) as Response<ScriptVO>;
}

/** 场景聊天 */
export function createChat(data: { emanId: string; scriptId?: string; sceneId: string }, showError = true) {
    return request({
        url: '/chat',
        method: 'POST',
        data,
        showError
    }) as Response<ChatVO>;
}

/** 场景聊天ppt */
export function createChatppt(data: { emanId: string; scriptId?: string; sceneId: string }, showError = true) {
    return request({
        url: '/chat/ppt',
        method: 'POST',
        data,
        showError
    }) as Response<ChatVO>;
}
// 创建自定义脚本聊天
export function createChatCustom(data: ScriptVO & { emanId: string; sceneId: string }) {
    return request({
        url: '/chat/createCustom',
        method: 'POST',
        data
    }) as Response<{ id: string; chatReportId: string }>;
}

// 查询e人最近聊天使用的脚本
export function getLastChatScript(id: string) {
    return request({
        url: `/eman/lastChatScript/${id}`
    }) as Response<ScriptVO & { type: number; scriptId: string }>;
}

// 会话结束
export function doneChat(id: string, shareFlag: boolean) {
    return request({
        url: `/chat/${id}/done?shareFlag=${shareFlag}`,
        method: 'PUT'
    }) as Response<{}>;
}

// 会话结束并生成报告
export function doneGenerateReport(id: string, shareFlag: boolean) {
    return request({
        url: `/chat/${id}/doneGenerateReport?shareFlag=${shareFlag}`,
        method: 'PUT'
    }) as Response<{}>;
}
// 获取轮播话术
export function getresScript() {
    return request({
        url: `/common/reference-script`,
        method: 'GET'
    }) as Response<any>;
}

// 生成对话结束报告
export function generateReport(chatId: string, shareFlag: boolean) {
    return request({
        url: `/chat/report/${chatId}?shareFlag=${shareFlag}`,
        method: 'POST'
    }) as Response<{}>;
}

export function getChat(id: string) {
    return request({
        url: `/chat/${id}`
    }) as Response<ChatVO>;
}

/** 获取对话聊天记录 */
export function getAllChatHistory(id: string) {
    return request({
        url: `/chat/${id}/history/all`
    }) as Response<HistoryVO[]>;
}

/** 获取对话聊天记录（分页） */
export function getChatHistoryPage(id: string, query?: QueryPagination & { lastId: string }) {
    return request({
        url: `/chat/${id}/history/page`,
        data: query
    }) as PagingResponse<HistoryVO>;
}

/** 获取用户历史对话（分页） */
export function getChatList(query?: QueryPagination) {
    return request({
        url: '/chat/page',
        data: query
    }) as PagingResponse<ChatHistoryVO>;
}

/** 获取用户产品列表（分页） */
export function scriptProductList(query?: QueryPagination) {
    return request({
        url: '/script/productList',
        data: query
    }) as PagingResponse<ChatHistoryVO>;
}
/** 获取背包脚本分页 */
export function getScriptList(query?: QueryPagination) {
    return request({
        url: '/script/scriptBag',
        data: query
    }) as PagingResponse<ScriptVO>;
}

export function getReport(id: string) {
    return request({
        url: `/chat/report/${id}`
    }) as Response<ReportVo>;
}

export function getReportLogin(id: string) {
    return request({
        url: `/chat/report/${id}/login`
    }) as Response<ReportVo>;
}

export function getReportShare(id: string) {
    return request({
        url: `/chat/report/share/${id}`
    }) as Response<ReportVo>;
}
export function getReportCheckLogin(id: string) {
    return request({
        url: `/chat/report/checkLogin/${id}`
    }) as Response<boolean>;
}

export function qaCompletions(chatid: string, data: { qaId: string | number; answer: string; audioFileName?: string }) {
    return request({
        url: `/chat/${chatid}/qaCompletions`,
        method: 'POST',
        data
    }) as Response<{ chatQAId: string; chatHistoryId: string }>;
}
export function qaAnswerRewrite(data: any) {
    return request({
        url: `/chat/qaAnswerRewrite`,
        method: 'POST',
        data,
        showError: false
    }) as Response<string>;
}

export function qaSave(chatid: string, qaId: string | number) {
    return request({
        url: `/chat/${chatid}/qaSaveQuestion/${qaId}`,
        method: 'POST'
    }) as Response<boolean>;
}

export function getChatParam(chatid: string) {
    return request({
        url: `/chat/${chatid}/chatParam`,
        method: 'GET'
    }) as Response<ChatParamVo>;
}

export function clearContext(chatid: string) {
    return request({
        url: `/chat/${chatid}/clear-context`,
        method: 'POST'
    }) as Response<boolean>;
}

export function scriptUnlockCondition(id: string) {
    return request({
        url: `/script/unlock-condition/${id}`,
        method: 'GET'
    }) as Response<ScriptUnlockConditionVo[]>;
}

export function getSuitableEman(scriptId: string) {
    return request({
        url: '/eman/suitableEman',
        data: { scriptId }
    }) as Response<EmanVO[]>;
}

// 保存开场白
export function saveIntroduction(chatId: string) {
    return request({
        url: `/chat/${chatId}/saveIntroduction`,
        method: 'POST'
    }) as Response<boolean>;
}

// 获取ai文本
export function getChatResponse(chatId: string) {
    return request({
        url: `/chat/getResponse`,
        method: 'GET',
        // responseType: 'arraybuffer',
        data: { id: chatId }
    }) as Response<string[]>;
}

/**
 * 获取ppt识别记录
 * @param chatId
 */
export function getPPTSummaryDetail(chatId: string) {
    return request({
        url: `/chat/${chatId}/pptSummaryDetail`,
        method: 'GET'
    }) as Response<PPTSummaryVo>;
}

export function savePPTSummary(chatId: string, pptId: string, answer: string) {
    return request({
        url: `/chat/${chatId}/pptSummarySave/${pptId}`,
        method: 'POST',
        data: { answer }
    }) as Response<boolean>;
}

/**
 * 幻灯片每页演练结束操作
 * @param chatId 对话id
 * @param pptId pptId
 * @returns Promise
 */
export function pptPageDone(pptId: string) {
    return request({
        url: `/chat/${pptId}/pptPageDone`,
        method: 'PUT'
    });
}
