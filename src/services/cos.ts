import type { Response } from '@/types/common';
import Taro from '@tarojs/taro';
// import COS from 'cos-wx-sdk-v5';
import request from './requestTaro';

export type CosConfig = {
    audioFileName: string;
    // starttime: string;
    // expiredtime: string;
    path: string;
    // sessiontoken: string;
    // tmpsecretid: string;
    // tmpsecretkey: string;
    // region: string;
    // bucket: string;
    filePath?: string;
    uploadUrl: string;
    mimeType: string;
};

// export function cosUpload(params: CosConfig, onProgress?: (progressData: any) => void) {
//     console.log('cosupload', params);
//     const cos = new COS({
//         SecretId: params.tmpsecretid, // sts服务下发的临时 secretId
//         SecretKey: params.tmpsecretkey, // sts服务下发的临时 secretKey
//         SecurityToken: params.sessiontoken, // sts服务下发的临时 SessionToken
//         StartTime: Number(params.starttime), // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
//         ExpiredTime: Number(params.expiredtime) // 临时密钥过期时间
//     });

//     return cos.uploadFile({
//         Bucket: params.bucket /* 填写自己的 bucket，必须字段 */,
//         Region: params.region /* 存储桶所在地域，必须字段 */,
//         Key: params.path /* 存储在桶里的对象键（例如:1.jpg，a/b/test.txt，图片.jpg）支持中文，必须字段 */,
//         FilePath: params.filePath,
//         SliceSize:
//             1024 * 1024 * 10 /* 触发分块上传的阈值，超过5MB使用分块上传，小于5MB使用简单上传。可自行设置，非必须 */,
//         onProgress(progressData: any) {
//             console.log('progress', JSON.stringify(progressData));
//             onProgress && onProgress(progressData);
//         }
//     });
// }

// export function cosDownload(params: CosConfig) {
//     const cos = new COS({
//         SecretId: params.tmpsecretid, // sts服务下发的临时 secretId
//         SecretKey: params.tmpsecretkey, // sts服务下发的临时 secretKey
//         SecurityToken: params.sessiontoken, // sts服务下发的临时 SessionToken
//         StartTime: Number(params.starttime), // 建议传入服务端时间，可避免客户端时间不准导致的签名错误
//         ExpiredTime: Number(params.expiredtime) // 临时密钥过期时间
//     });

//     return cos.getObject({
//         Bucket: params.bucket /* 填写自己的 bucket，必须字段 */,
//         Region: params.region /* 存储桶所在地域，必须字段 */,
//         Key: params.path
//     });
// }

export function audioUploadComplete(key: string) {
    return request({
        url: `/chat/audioUploadComplete`,
        method: 'POST',
        data: {
            key
        }
    }) as Response<boolean>;
}

export function cosUpload(config: CosConfig, path: string) {
    return new Promise((resolve, reject) => {
        let data;
        const fs = Taro.getFileSystemManager();
        try {
            data = fs.readFileSync(path);
            console.log('readFileSync', data);
        } catch (error) {
            console.log('readFile error', error);
            data = null;
        }
        if (data) {
            console.log('cosUpload', config, path);
            Taro.request({
                url: config.uploadUrl,
                method: 'PUT',
                header: {
                    'Content-Type': config.mimeType
                },
                data,
                success(res) {
                    resolve(res);
                },
                fail(err) {
                    Taro.showToast({
                        title: err.errMsg,
                        icon: 'error'
                    });
                    reject(err);
                }
            });
        } else {
            Taro.showToast({
                title: '文件不存在',
                icon: 'error'
            });
            reject('文件不存在');
        }
    });
}
