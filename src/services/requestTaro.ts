import { isEmpty, Storage, useModel } from '@/common';
import config from '@/config';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import frontLogout from '@/utils/fontLogout';
import Taro from '@tarojs/taro';

const request = ({
    url,
    method = 'GET',
    data,
    showError = true,
    reLogin = true,
    headers,
    responseType = 'text',
    timeout = 10000
}: {
    url: string;
    method?: keyof Taro.request.Method | undefined;
    data?: any;
    showError?: boolean;
    reLogin?: boolean;
    headers?: any;
    responseType?: 'text' | 'arraybuffer';
    timeout?: number;
}) => {
    console.log(showError, reLogin);
    return new Promise((resolve, reject) => {
        const header: any = {
            biz: config.biz
        };
        const tenantId = Storage.get(StorageEnvKey.TENANT_ID);
        const homePath = Storage.get(StorageEnvKey.HOME_PATH);
        if (tenantId) {
            header.tenantId = tenantId;
        }
        const isWework = Storage.get(StorageEnvKey.IS_WEWORK);
        const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);

        if (isEnterprise === 0) {
            header.appid = Taro.getAccountInfoSync().miniProgram.appId;
        }

        const token = Storage.get(StorageEnvKey.TOKEN);

        if (!isEmpty(token)) {
            header.Authorization = token;
        }
        if (headers) {
            Object.assign(header, headers);
        }
        Taro.request({
            url: config.server + url,
            method,
            data,
            header,
            responseType,
            timeout,
            success: async (res) => {
                console.log(url, data, header, res);
                const { data: resData } = res;
                if (resData.code === 200) {
                    resolve(res);
                } else if (resData.code === 401 || resData.code === 20004) {
                    if (showError) {
                        Taro.showToast({
                            icon: 'none',
                            title: '登录信息失效，请重新登录',
                            mask: true,
                            duration: 1000
                        });
                    }
                    frontLogout();
                    if (reLogin) {
                        setTimeout(
                            async () => {
                                if (isWework === '1') {
                                    Taro.reLaunch({ url: HomePath.MIDDLE });
                                } else {
                                    if (isEnterprise === 1) {
                                        // 药企
                                        Taro.redirectTo({
                                            url: '/pages/login/index/index'
                                        });
                                    } else {
                                        /* const { code } = await getWxCode();
                                        const loginRes = await wxlogin(code);
                                        const { data: loginData } = loginRes.data;
    
                                        if (loginRes.data.code === 200) {
                                            loginSuccess(loginData); // 存token和用户信息
                                            const { userInfo } = loginData;
    
                                            const phoneData = userInfo.phoneNumber || '';
                                            if (!phoneData) {
                                                Taro.redirectTo({ url: '/pages/login/index/index' });
                                            } else {
                                                // 有手机号则保存信息
                                                Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息
                                                Taro.reLaunch({ url: '/pages/home/<USER>' });
                                            }
                                        } else {
                                            Taro.reLaunch({ url: '/pages/home/<USER>' });
                                        } */
                                        Taro.reLaunch({ url: HomePath.MIDDLE });
                                    }
                                }
                            },
                            showError ? 1000 : 0
                        );
                    }

                    reject(res);
                } else if (resData.code === 20007) {
                    Taro.showModal({
                        title: '',
                        content: '您的账号不支持微信端登录，请切换到企业微信操作',
                        showCancel: false,
                        confirmText: '好的',
                        confirmColor: '#4F66FF'
                    });
                } else {
                    if (showError) {
                        Taro.showToast({
                            icon: 'none',
                            title: resData.message
                        });
                    }

                    reject(res);
                }
            },
            fail: (error) => {
                console.log(url, data, header, error);
                if (showError) {
                    if (error.errMsg.includes('timeout') || error.errMsg.includes('time out')) {
                        Taro.showToast({
                            icon: 'none',
                            title: '网络不给力，\r\n请检查网络设置'
                        });
                    } else {
                        Taro.showToast({
                            icon: 'none',
                            title: '网络错误'
                        });
                    }
                }
                reject(error);
            }
        });
    });
};

export default request;
