import type { Response, TenantList, UserInfo } from '@/types/common';
import Taro from '@tarojs/taro';
import request from './requestTaro';

/**
 * 手机号码登录
 *
 * @export
 * @param {string} code 微信code
 * @return {*}
 */
export function wxlogin(code: string) {
    const url = `/wxLogin?code=${code}`;
    return request({
        url,
        method: 'POST'
    }) as Response<{
        token: string;
        userInfo: UserInfo;
    }>;
}

export function updateUser(data: { avatar: string; name: string }) {
    return request({
        url: '/user/update',
        method: 'POST',
        data
    }) as Response<boolean>;
}

export function getWxCode() {
    return new Promise<{ code: string; errMsg: string }>((resolve, reject) => {
        Taro.login({
            success: (res) => {
                if (res.code) {
                    resolve(res);
                } else {
                    reject();
                }
            },
            fail: (err) => {
                console.log('wx login err', err);
                reject(err);
            }
        });
    });
}

// 企业微信获取code
export function getWxWorkCode() {
    return new Promise<{ code: string; errMsg: string }>((resolve, reject) => {
        wx.qy.login({
            success(res) {
                console.log('qy login', res);
                if (res.code) {
                    resolve(res);
                } else {
                    reject();
                }
            },
            fail(err) {
                console.log('qy login err', err);
                reject(err);
            }
        });
    });
}

export function weworkLogin(code: string) {
    const url = `/qyLogin?code=${code}`;
    return request({
        url,
        method: 'POST'
    }) as Response<{
        token: string;
        userInfo: UserInfo;
    }>;
}

export function bindPhone(code: string, phonenumber: string) {
    return request({
        url: `/bindPhone?code=${code}&phonenumber=${phonenumber}`,
        method: 'POST'
    }) as Response<string>;
}
export function logout() {
    return request({
        url: `/logout`,
        method: 'POST',
        reLogin: false,
        showError: false
    }) as Response<string>;
}

export function getEnterpriseFlag() {
    return request({
        url: `/getEnterpriseFlag`,
        method: 'GET'
    }) as Response<boolean>;
}

export function getTenantList(phoneCode: string, phonenumber: string) {
    return request({
        url: `/tenantList?phoneCode=${phoneCode}&phonenumber=${phonenumber}`,
        method: 'GET',
        reLogin: false
    }) as Response<TenantList>;
}
export function getNewTenantList(phoneCode: string) {
    return request({
        url: `/tenantList?phoneCode=${phoneCode}`,
        method: 'GET',
        reLogin: false
    }) as Response<TenantList>;
}

// 获取手机号下所属药企租户（已登录）
export function getTenantListLogin() {
    return request({
        url: '/tenantListLogin',
        method: 'GET'
    }) as Response<TenantList>;
}

export function loginStr(loginStr: string, jsCode: string) {
    return request({
        url: `/loginByLoginStr?loginStr=${loginStr}&jsCode=${jsCode}`,
        method: 'POST'
    }) as Response<{
        token: string;
        userInfo: UserInfo;
    }>;
}

export function getUserInfo() {
    return request({
        url: `/user/info`,
        method: 'GET',
        reLogin: false,
        showError: false
    }) as Response<UserInfo>;
}

export function getUserInfoSetting() {
    return request({
        url: `/user/info`,
        method: 'GET'
    }) as Response<UserInfo>;
}

export function sendVerifyCode(mobile: string) {
    return request({
        url: `/common/send-verify-code`,
        method: 'POST',
        data: {
            mobile
        }
    }) as Response<boolean>;
}

// 获取数据看板是否开启
export function getDataBoardOpen() {
    return request({
        url: `/getKanbanFlag`,
        method: 'POST'
    }) as Response<boolean>;
}
/* 获取分享设置 */
export function getShareSetting() {
    return request({
        url: '/getShareFlag',
        method: 'POST'
    }) as Response<boolean>;
}

// 注册
export function register(params: {
    phoneNumber: string;
    phoneCode: string;
    company: string;
    post: string;
    name: string;
    jsCode: string;
}) {
    return request({
        url: `/register`,
        method: 'POST',
        data: params
    }) as Response<{
        token: string;
        userInfo: UserInfo;
    }>;
}
