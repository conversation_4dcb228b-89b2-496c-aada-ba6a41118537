import { AppIdConsts } from '@/constants/appid';
import Taro from '@tarojs/taro';
type CONFIG = {
    env: 'develop' | 'trial' | 'release';
    biz: number;
    cdnPrefix: string;
    tmplScriptId: string;
    server: string;
    QCloudAIVoice: {
        appId: number;
        secretId: string;
        secretKey: string;
        hotword_id: string;
        customization_id: string;
    };
    shuzirenServer: string;
    webviewDebug: boolean;
    debug: boolean;
};
const cdnPrefix = 'https://assets.ecaisys.com/yubei/miniapp/';
const baseConfig = {
    biz: 2,
    cdnPrefix,
    tmplScriptId: 'x-Dg2LBNtYwN6P6Uyo0TVD3-Q5TmkbV5LrtUQSCyE6g'
};

// 测试的
const testConfig = {
    QCloudAIVoice: {
        appId: 1324569743,
        secretId: 'AKIDaI6Oj6Sp8Mg1Hbq6eBgAWtN9V2hQZo9e',
        secretKey: 'qcRWdjjxKoJh7TnIwY1XzNj2JgBmXRSP',
        hotword_id: '17994de945c311ef8484525400aec391',
        customization_id: 'd75bcfcb45d811ef8484525400aec391'
    },
    server: 'https://ybyuanyi.ecaiabc.com/api/v1',
    // server: 'https://yicai.ecaiabc.com/api/v1',
    // server: 'http://************:7002/api/v1',
    shuzirenServer: 'https://yb.ecaiabc.com/shuziren',
    // shuzirenServer: 'http://localhost:8080',
    webviewDebug: true,
    debug: true
};
// 正式的
const releaseConfig = {
    QCloudAIVoice: {
        appId: 1324767360,
        secretId: 'AKID3W6f6KL8h3B50rTtCJSN4XourcCZjjZC',
        secretKey: 'XqvmPo13whFYcSPXlupWzEI7i5GDwRil',
        hotword_id: '591d5ad1451611ef8484525400aec391',
        customization_id: 'd75bcfcb45d811ef8484525400aec391'
    },
    shuzirenServer: 'https://www.hiyubei.com/shuziren',
    server: 'https://www.hiyubei.com/prod-api/api/v1',
    webviewDebug: false,
    debug: true
};
// 请求配置，自己根据项目情况，自定义配置

const envConfigs = {
    develop: {
        env: 'develop',
        ...baseConfig,
        ...testConfig
    },
    trial: {
        env: 'trial',
        ...baseConfig,
        ...testConfig,
        shuzirenServer: 'https://yb.ecaiabc.com/shuziren'
    },
    release: {
        env: 'release',
        ...baseConfig,
        ...releaseConfig
    }
};
const baseConfigKY = {
    biz: 2,
    tmplScriptId: 'h8Pn9_81KeNBmS70aXTGTmI3sOh8Sfhol6XU36-TbV4',
    cdnPrefix
};

const QCloudAIVoiceKY = {
    appId: 1300745974,
    secretId: 'AKIDW9lLD6ykaH64FQSNR8oIdots8UpHtd4e',
    secretKey: 'b4MqGrvKkROMlL67MZktBUzN151yjPDt',
    hotword_id: 'f52051bdbd0b11ef8484525400aec391',
    customization_id: ''
};
const testConfigKY = {
    QCloudAIVoice: QCloudAIVoiceKY,
    // server: 'https://qingnang.kanion.com/dev/wechat-api/api/v1',
    server: 'http://*************:8082/api/v1',
    shuzirenServer: '',
    webviewDebug: true,
    debug: true
};
const releaseConfigKY = {
    QCloudAIVoice: QCloudAIVoiceKY,
    server: 'https://qingnang.kanion.com/api/api/v1',
    shuzirenServer: '',
    webviewDebug: false,
    debug: false
};
const envConfigsKY = {
    develop: {
        env: 'develop',
        ...baseConfigKY,
        ...testConfigKY
    },
    trial: {
        env: 'trial',
        ...baseConfigKY,
        ...testConfigKY
    },
    release: {
        env: 'release',
        ...baseConfigKY,
        ...releaseConfigKY
    }
};

// 自己根据环境切换
// const APP_ENV = process.env.NODE_ENV as keyof typeof envConfigs;
// const APP_MODE = process.env.TARO_APP_MODE;
// console.log('APP_MODE', APP_MODE);

const { envVersion, appId } = Taro.getAccountInfoSync().miniProgram;
console.log('miniProgram', envVersion, appId);
let config: CONFIG;
if (appId === AppIdConsts.qnq) {
    // 康源
    config = envConfigsKY[envVersion as keyof typeof envConfigsKY];
} else {
    config = envConfigs[envVersion as keyof typeof envConfigs];
}

console.log('config', config);
export default config;
