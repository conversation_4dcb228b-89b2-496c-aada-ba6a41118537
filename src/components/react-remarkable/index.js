import { ruleImageUrl } from '@/utils/regexRules';
import { createSelectorQuery } from '@tarojs/taro';
import React from 'react';
import { Remarkable as Markdown } from 'remarkable';
import { RichText } from "@tarojs/components";

class Remarkable extends React.Component {
    render() {
        const Container = this.props.container;
        const { onImgClick } = this.props;
        const handleClick = (e) => {
            console.log('md click', e, e.target.id);
            e.stopPropagation();

            onImgClick && onImgClick(this.imgList && this.imgList[0], this.imgList)
        };

        return <Container onClick={handleClick}>{this.content()}</Container>;
    }

    UNSAFE_componentWillUpdate(nextProps, nextState) {
        if (nextProps.options !== this.props.options) {
            this.md = new Markdown(nextProps.options);
        }
    }
    extractImageUrls(html) {
        const regex = /<img[^>]*src="([^"]*)"/g;
        const matches = [];
        let match;
        while ((match = regex.exec(html)) !== null) {
            // console.log('ruleImageUrl', match)
            matches.push(match[1]);
        }
        return matches;
    }
    content() {
        if (this.props.source) {
            let cont = this.renderMarkdown(this.props.source);
            // 正则找到所有img标签并增加mode="aspectFill"

            this.imgList = this.extractImageUrls(cont);
            const newCont = cont.replace(/<img/g, `<img style="width:100%;height: auto;"`).replace(/<(\/)?pre[^>]*>/gi,'');
            cont = newCont;

            return <RichText nodes={cont} />;
        } else {
            return React.Children.map(this.props.children, (child) => {
                let cont = this.renderMarkdown(child);
                if (typeof child === 'string') {
                    this.imgList = this.extractImageUrls(cont);

                    // const newCont = cont.replace(/<img([^>]*)>/g, '<img$1 mode="aspectFill">');
                    const newCont = cont.replace(/<img/g, `<img style="width:100%;height: auto;"`).replace(/<(\/)?pre[^>]*>/gi,'');
                    cont = newCont;
                    return <RichText nodes={cont} />;
                } else {
                    return child;
                }
            });
        }
    }

    renderMarkdown(source) {
        // console.log('renderMarkdown', source);

        if (!this.md) {
            this.md = new Markdown(this.props.options);
        }

        return this.md.render(source);
    }
}

Remarkable.defaultProps = {
    container: 'div',
    options: {}
};

export default Remarkable;
