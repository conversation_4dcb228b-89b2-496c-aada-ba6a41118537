import type { SocketTask } from '@tarojs/taro';
import Taro, { pxTransform } from '@tarojs/taro';

import { Storage } from '@/common';
import MarkDownComponent from '@/components/react-remarkable';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { Icon } from '@antmjs/vantui';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { useThrottleFn, useUnmount } from 'ahooks';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import styles from './index.less';

export enum HelpStatus {
    Loading = 'Loading',
    Generating = 'Generating',
    Error = 'Error',
    Completed = 'Completed'
}

const Index: React.FC<{
    show: boolean;
    styleType: 'text' | 'voice';
    keyHeight: number;
    onClose: () => void;
    chatId?: string;
    currentPPTIndexId?: string;
    canGenerate: boolean; // 是否可以生成
}> = (props) => {
    const HelpRobot = `${config.cdnPrefix}aihelp/help_robot.png`;
    const HelpError = `${config.cdnPrefix}aihelp/help_error.png`;
    const IconRetry = `${config.cdnPrefix}aihelp/icon_retry.svg`;
    const IconLoading = `${config.cdnPrefix}aihelp/loading.svg`;

    const { show, styleType, onClose, keyHeight, chatId, canGenerate = true, currentPPTIndexId } = props;
    // console.log(currentPPTIndexId, chatId, 'chatIdchatId');
    const token = Storage.get(StorageEnvKey.TOKEN);
    const [text, setText] = useState<string>('');
    const [scrollTop, setScrollTop] = useState(0);

    const [status, setStatus] = useState<HelpStatus>(HelpStatus.Loading);
    const socketTask = useRef<SocketTask>();

    const getScrollHeight = (): Promise<number> => {
        return new Promise((resolve) => {
            const query = Taro.createSelectorQuery();
            query
                .select('#helpContentScroll')
                .boundingClientRect((rect) => {
                    resolve(rect.height);
                })
                .exec();
        });
    };

    const { run: handleScroll } = useThrottleFn(
        useCallback(() => {
            console.log('handleScroll');
            const query = Taro.createSelectorQuery();
            query
                .select('#helpContent')
                .boundingClientRect(async (rect) => {
                    if (!rect) {
                        return;
                    }
                    const scrollContentHeight = await getScrollHeight();
                    const top = rect.height - scrollContentHeight;
                    console.log(top, 'top');
                    setScrollTop(top);
                })
                .exec();
        }, [styleType]),
        {
            wait: 100,
            leading: true,
            trailing: false
        }
    );

    const initSocket = useCallback(async () => {
        if (socketTask.current && socketTask.current.readyState === 1) {
            return;
        }
        const url = `${config.server.replace('http', 'ws')}/ws/aiHelp/${chatId}?token=${token}`;
        console.log(url, 'urlurlurl');
        socketTask.current = await Taro.connectSocket({
            url,
            success: () => {
                console.log('WebSocket connected');
            },
            fail: (err) => {
                console.error('WebSocket connection failed', err);
                setStatus(HelpStatus.Error);
            }
        });

        socketTask.current.onOpen((e) => {
            console.log('WebSocket opened', e);

            generateText();
        });

        socketTask.current.onMessage((res) => {
            // console.log('Received message:', res);
            setStatus(HelpStatus.Generating);
            const { data } = res;
            if (data === '0\r\n' || data.indexOf('0\r\n') !== -1) {
                console.log('complete');
                setStatus(HelpStatus.Completed);

                setText((text) => text + data.replace('0\r\n', ''));
            } else {
                setText((text) => text + data);
            }
            handleScroll();
        });

        socketTask.current.onError((err) => {
            console.error('WebSocket error:', err);
            setText('');
            setStatus(HelpStatus.Error);
            socketTask.current?.close({});
        });

        socketTask.current.onClose((e) => {
            console.log('WebSocket closed', e);
        });
    }, [chatId]);
    function generateText() {
        setText('');
        setStatus(HelpStatus.Loading);
        if (socketTask.current && socketTask.current.readyState === 1) {
            socketTask.current?.send({
                data: JSON.stringify({
                    action: 'HELP',
                    pptId: currentPPTIndexId
                })
            });
        } else {
            initSocket();
        }
    }

    const stopGenerate = () => {
        socketTask.current?.send({
            data: JSON.stringify({ action: 'STOP' })
        });
    };
    const close = () => {
        if (socketTask.current && socketTask.current.readyState === 1) {
            stopGenerate();
        }
        onClose();
    };

    const positonStyle = useMemo(() => {
        if (styleType === 'text') {
            return {
                bottom: `${100}px`,
                transform: `translateY(-${keyHeight}px)`
            };
        } else if (styleType === 'voice') {
            return {
                top: `${100}px`
            };
        }
    }, [styleType, keyHeight]);

    useEffect(() => {
        if (show) {
            generateText();
        } else {
            setScrollTop(0);
            setText('');
            setStatus(HelpStatus.Loading);
        }
    }, [show]);

    useUnmount(() => {
        if (socketTask.current && socketTask.current.readyState === 1) {
            stopGenerate();
        }
        socketTask.current?.close({});
        socketTask.current = null;
    });

    return (
        <View className={styles.aishu}>
            <View
                className={classNames(styles.aiHelpWrapper, styles[styleType], {
                    [styles.visible]: show
                })}
            >
                <View className={classNames(styles.ai_content_box, styles[styleType])} style={positonStyle}>
                    <View className={styles.aiHelpBox}>
                        <Image src={HelpRobot} mode='aspectFit' className={styles.help_robot_img} />
                        <Icon
                            name='cross'
                            className={styles.close}
                            size={pxTransform(40)}
                            color='#272C47'
                            onClick={close}
                        />

                        <View className={styles.aiHelpBoxtiyle}>AI求助:</View>

                        {status === HelpStatus.Loading && (
                            <View className={styles.loading_box}>
                                <Image src={IconLoading} className={styles.icon_loading} />
                                <Text>AI正在思考中，请稍等...</Text>
                            </View>
                        )}

                        <View className={classNames(styles.aiHelpContent, styles[styleType])}>
                            <ScrollView
                                scrollY
                                id='helpContentScroll'
                                scrollTop={scrollTop}
                                scrollWithAnimation
                                className={classNames(styles.aiHelpBoxTextStart, styles[styleType])}
                            >
                                <View id='helpContent'>
                                    <MarkDownComponent source={text} />
                                </View>
                            </ScrollView>

                            {status === HelpStatus.Completed && canGenerate && (
                                <View className={styles.renederate_box}>
                                    <View className={styles.aiHelpBoxTextStartButtle} onClick={generateText}>
                                        <Image src={IconRetry} className={styles.icon_retry} />
                                        <Text>换一换</Text>
                                    </View>
                                </View>
                            )}
                        </View>

                        {status === HelpStatus.Error && (
                            <View className={styles.help_error}>
                                <Image src={HelpError} className={styles.help_error_icon} />
                                <View className={styles.help_error_text}>生成失败</View>
                                <View className={styles.help_retry} onClick={generateText}>
                                    重试
                                </View>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </View>
    );
};

export default Index;
