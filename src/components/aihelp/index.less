.aishu{
  .aiHelpWrapper {
    display: none;
    position: absolute;
    left: 0;
    touch-action: none;
    right: 0;
    &.text {
      background: rgba(0, 0, 0, 0.3);
      top: 0;
      bottom: 0;
      z-index: 1;
    }
    &.voice {
      top: 0;
      z-index: 101;
    }
  
  }
  
  .visible {
    display: block;
  }
  .ai_content_box {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
  }
  .help_robot_img {
    display: block;
    width: 190px;
    height: 190px;
    position: absolute;
    top: -95px;
    left: 50%;
    margin-left: -95px;
  }
  .aiHelpBox {
    margin: 95px 40px 0;
    border-radius: 24px;
    position: relative;
    padding-top: 72px;
    padding-bottom: 32px;
    background: linear-gradient(180deg, #D6EBFF 0%, #FFFFFF 43%);
  
    .close {
      position: absolute;
      right: 32px;
      top: 32px;
    }
  
    .aiHelpBoxtiyle {
      font-size: 32px;
      margin-bottom: 32px;
      margin-left: 32px;
      color: #272C47;
      font-weight: bold;
    }
    .aiHelpContent {
        &.voice {
          max-height: 420px;
        }
      display: flex;
      flex-direction: column;
    }
    .aiHelpBoxTextStart {
      padding: 0 32px;
      box-sizing: border-box;
      overflow-y: auto;
      color: #000000;
      font-size: 28px;
      &.text {
        max-height: 180px;
      }
      &.voice {
        max-height: 320px;
      }
    }
  
  }
  
  
  
  .loading_box {
    text-align: center;
    font-size: 28px;
    padding: 14px;
    color: #3D3D3D;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 32px 48px;
  
    .icon_loading {
      width: 32px;
      height: 32px;
      margin-right: 14px;
      animation: rotate 3s linear infinite;
    }
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }
  
  
  .renederate_box {
    display: flex;
    justify-content: flex-end;
  
    .aiHelpBoxTextStartButtle {
      width: 170px;
      height:  64px;
      border-radius: 64px;
      background: rgba(79, 102, 255, 0.1);
      border: 1px solid #4F66FF;
      color: #4F66FF;
      font-size: 26px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 32px;
      margin-right: 32px;
    }
  
    .icon_retry {
      width: 32px;
      height: 32px;
      margin-right: 12px;
    }
  }
  
  
  .help_error {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 18px;
    padding-bottom: 40px;
  
    &_text {
      font-size: 24px;
      text-align: center;
      color: #EE5858;
      margin-top: -40px;
    }
    &_icon {
      width: 234px;
      height: 234px;
    }
    .help_retry {
      width: 160px;
      height: 72px;
      border-radius: 72px;
      line-height: 72px;
      text-align: center;
      color: #FFFFFF;
      font-size: 26px;
      background: linear-gradient(270deg, #6742FF 0%, #3D83FF 99%);
    }
  }
}
.ai{
  .aiHelpWrapper {
    display: none;
    position: absolute;
    left: 0;
    touch-action: none;
    right: 0;
    &.text {
      background: rgba(0, 0, 0, 0.3);
      top: 0;
      bottom: 0;
      z-index: 1;
    }
    &.voice {
      top: -65px;
      z-index: 101;
    }
  
  }
  
  .visible {
    display: block;
  }
  .ai_content_box {
    overflow: hidden;
    position: absolute;
    left: 0;
    right: 0;
  }
  .help_robot_img {
    display: block;
    width: 95px;
    height:  95px;
    position: absolute;
    top: -37px;
    left: 50%;
    margin-left: -45px;
  }
  .aiHelpBox {
    margin: 33px 20px 0;
    border-radius: 24px;
    position: relative;
    padding-top:23px;
    padding-bottom: 12px;
    background: linear-gradient(180deg, #D6EBFF 0%, #FFFFFF 43%);
  
    .close {
      position: absolute;
      right: 16px;
      top: 16px;
    }
  
    .aiHelpBoxtiyle {
      font-size: 16px;
      margin-bottom: 12px;
      margin-left: 32px;
      color: #272C47;
      font-weight: bold;
    }
    .aiHelpContent {
      &.voice {
        max-height: 130px;
      }
    display: flex;
    flex-direction: column;
  }
  .aiHelpBoxTextStart {
    padding: 0 32px;
    box-sizing: border-box;
    overflow-y: auto;
    color: #000000;
    font-size: 14px;
    // &.text {
    //   max-height: 160px;
    // }
    // &.voice {
    //   max-height: 80px;
    // }
  }
  
  }
  
  
  
  .loading_box {
    text-align: center;
    font-size: 16px;
    padding: 7px;
    color: #3D3D3D;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 16px 28px;
  
    .icon_loading {
      width: 16px;
      height: 16px;
      margin-right: 14px;
      animation: rotate 3s linear infinite;
    }
    @keyframes rotate {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }
  
  
  .renederate_box {
    display: flex;
    justify-content: flex-end;
  
    .aiHelpBoxTextStartButtle {
      width: 72px;
      height:  22px;
      border-radius: 32px;
      background: rgba(79, 102, 255, 0.1);
      border: 1px solid #4F66FF;
      color: #4F66FF;
      font-size: 13px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-top: 11px;
      margin-right: 16px;
    }
  
    .icon_retry {
      width: 14px;
      height: 14px;
      margin-right: 6px;
    }
  }
  
  
  .help_error {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 18px;
    padding-bottom: 15px;
  
    &_text {
      font-size: 12px;
      text-align: center;
      color: #EE5858;
      margin-top: -40px;
    }
    &_icon {
      width: 50px;
      height: 50px;
    }
    .help_retry {
      width: 80px;
      height: 36px;
      border-radius: 36px;
      line-height: 36px;
      text-align: center;
      color: #FFFFFF;
      font-size: 13px;
      background: linear-gradient(270deg, #6742FF 0%, #3D83FF 99%);
    }
  }
}
