import MarkDownComponent from '@/components/react-remarkable';
import { useEffect, useState } from 'react';
function Typewriter({ text, typingSpeed }: { text: string; typingSpeed: number }) {
    // console.log('Typewriter-text', text);

    const [displayText, setDisplayText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);

    useEffect(() => {
        const interval = setInterval(() => {
            if (currentIndex < text.length) {
                setDisplayText((prevText) => prevText + text[currentIndex]);
                setCurrentIndex((prevIndex) => prevIndex + 1);
            } else {
                clearInterval(interval);
            }
        }, typingSpeed);

        return () => {
            clearInterval(interval);
        };
    }, [text, typingSpeed, currentIndex]);

    return <MarkDownComponent>{displayText}</MarkDownComponent>;
}

export default Typewriter;
