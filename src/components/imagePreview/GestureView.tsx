import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import CSSMatrix from '@thednp/dommatrix';
import { useMount } from 'ahooks';
import { useCallback, useEffect, useState } from 'react'; // 假设你已经将 dommatrix 模块转换为 ES6 模块

// 定义组件的 Props 类型
interface GestureViewProps {
    width?: string;
    height?: string;
    translateX?: boolean;
    translateXValue?: number;
    translateY?: boolean;
    translateYValue?: number;
    scale?: boolean;
    initialScaleValue?: number;
    scaleMin?: number;
    scaleMax?: number;
    rotate?: boolean;
    rotateValue?: number;
    children?: React.ReactNode;
}

const GestureView: React.FC<GestureViewProps> = (props) => {
    const {
        width = '300rpx',
        height = '200rpx',
        translateX = true,
        translateXValue = 0,
        translateY = true,
        translateYValue = 0,
        scale = true,
        initialScaleValue = 1.0,
        scaleMin = 0.1,
        scaleMax = 2.0,
        rotate = true,
        rotateValue = 0,
        children
    } = props;

    // 定义状态类型
    const [scaleValue, setScaleValue] = useState(initialScaleValue);
    const [startScale, setStartScale] = useState<number>(-1);
    const [startDistance, setStartDistance] = useState<number>(-1);
    const [transform, setTransform] = useState<string>('');
    const [transformMatrix, setTransformMatrix] = useState<CSSMatrix | null>(null);
    const [startTransformMatrix, setStartTransformMatrix] = useState<CSSMatrix | null>(null);
    const [startRotate, setStartRotate] = useState<number>(-1);
    const [startAngle, setStartAngle] = useState<number>(0);
    const [startOrigin, setStartOrigin] = useState<{ x: number; y: number }>({ x: 0, y: 0 });
    const [startTouches, setStartTouches] = useState<any[]>([]);

    // 计算两点之间的距离
    const getTouchDistance = (touches: any): number => {
        return Math.hypot(touches[0].clientX - touches[1].clientX, touches[0].clientY - touches[1].clientY);
    };

    // 计算两点之间的角度
    const getTouchAngle = (touches: any): number => {
        const x = touches[0].clientX - touches[1].clientX;
        const y = touches[0].clientY - touches[1].clientY;
        return (Math.atan2(y, x) * 180) / Math.PI;
    };

    // 计算两点的中点
    const midpoint = (touches: any): { x: number; y: number } => {
        const [t1, t2] = touches;
        return {
            x: (t1.clientX + t2.clientX) / 2,
            y: (t1.clientY + t2.clientY) / 2
        };
    };

    // 触摸开始事件
    const touchStart = useCallback(
        (e: any) => {
            const { touches } = e;
            setStartTouches(touches);
            if (touches.length === 2) {
                let startOrigin = midpoint(touches);
                Taro.createSelectorQuery()
                    .select('#gesture-ref')
                    .boundingClientRect((rect: any) => {
                        startOrigin = {
                            x: startOrigin.x - rect.left,
                            y: startOrigin.y - rect.top
                        };
                        setStartOrigin(startOrigin);
                        setStartDistance(getTouchDistance(touches));
                        setStartAngle(getTouchAngle(touches));
                    })
                    .exec();
            }
            setStartScale(scaleValue);
            setStartTransformMatrix(transformMatrix);
        },
        [rotateValue, scaleValue, transformMatrix]
    );

    // 触摸移动事件
    const touchMove = useCallback(
        (e: any) => {
            const { touches } = e;
            if (touches.length === 2 && startTouches.length === 2) {
                let curScale = startScale,
                    delta_scale = 1.0;
                if (scale) {
                    curScale = startScale * (getTouchDistance(touches) / startDistance);
                    if (curScale < scaleMin) curScale = scaleMin;
                    if (curScale > scaleMax) curScale = scaleMax;
                    delta_scale = curScale / startScale;
                }

                let delta_rotate = 0.0;
                if (rotate) {
                    delta_rotate = getTouchAngle(touches) - startAngle;
                }

                const mp_init = midpoint(startTouches);
                const mp_cur = midpoint(touches);
                const translateXDelta = translateX ? mp_cur.x - mp_init.x : 0;
                const translateYDelta = translateY ? mp_cur.y - mp_init.y : 0;

                const newTransformMatrix = new CSSMatrix()
                    .translate(startOrigin.x, startOrigin.y)
                    .translate(translateXDelta, translateYDelta)
                    .rotate(delta_rotate)
                    .scale(delta_scale)
                    .translate(-startOrigin.x, -startOrigin.y)
                    .multiply(startTransformMatrix);

                setScaleValue(curScale);
                // setRotateValue(curRotate);
                setTransformMatrix(newTransformMatrix);
            } else if (startTouches.length !== 2) {
                const newTransformMatrix = new CSSMatrix()
                    .translate(
                        translateX ? touches[0].clientX - startTouches[0].clientX : 0,
                        translateY ? touches[0].clientY - startTouches[0].clientY : 0
                    )
                    .multiply(startTransformMatrix);
                setTransformMatrix(newTransformMatrix);
            }
        },
        [
            rotate,
            scale,
            scaleMax,
            scaleMin,
            startAngle,
            startDistance,
            startOrigin.x,
            startOrigin.y,
            startRotate,
            startScale,
            startTouches,
            startTransformMatrix,
            translateX,
            translateY
        ]
    );

    // 触摸结束事件
    const touchEnd = (e: any) => {
        // 处理触摸结束逻辑
    };

    // 组件初始化
    useMount(() => {
        const initialTransformMatrix = new CSSMatrix()
            .scale(initialScaleValue)
            .rotate(rotateValue)
            .translate(translateXValue, translateYValue);
        setTransformMatrix(initialTransformMatrix);
        setStartTransformMatrix(initialTransformMatrix);
    });

    // 监听 transformMatrix 变化
    useEffect(() => {
        if (transformMatrix) {
            setTransform(transformMatrix.toString());
        }
    }, [transformMatrix]);

    return (
        <View
            className='gesture-wrapper'
            onTouchStart={touchStart}
            onTouchMove={touchMove}
            onTouchEnd={touchEnd}
            style={{ width, height, overflow: 'hidden' }}
        >
            <View id='gesture-ref' style={{ width: 0, height: 0 }} />
            <View className='content-container' style={{ transform, transformOrigin: '0 0' }}>
                {children}
            </View>
        </View>
    );
};

export default GestureView;
