import { Overlay } from '@antmjs/vantui';
import { Image, MovableArea, MovableView, View } from '@tarojs/components';
import styles from './index.less';
// import GestureView from "@/components/imagePreview/GestureView";

interface Props {
    show: boolean;
    onClose: () => void;
    current: string;
    urls: string[];
}

const Index: React.FC<Props> = (props) => {
    const { show, onClose, current } = props;
    const minScale = 1,
        maxScale = 2.5;

    return (
        <Overlay show={show} onClick={onClose}>
            <View className={styles.preview_container}>
                <MovableArea className={styles.moveable_area} scale-area>
                    <MovableView
                        className={styles.moveable_view}
                        direction='horizontal'
                        scale
                        scaleMin={minScale}
                        scaleMax={maxScale}
                    >
                        <Image className={styles.preview_img} mode='aspectFit' src={current} />
                    </MovableView>
                </MovableArea>
                {/*<GestureView width='100vw' height='100vh' translateY={false} scaleMin={minScale} scaleMax={maxScale} rotate={false}>
                    <Image className={styles.preview_img} mode='aspectFit' src={current} />
                </GestureView>*/}
            </View>
        </Overlay>
    );
};

export default Index;
