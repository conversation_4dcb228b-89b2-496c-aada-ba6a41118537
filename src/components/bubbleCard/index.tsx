// components/BubbleCard/index.tsx
import { Button } from '@hygeia/ui';
import { Text, View } from '@tarojs/components';
import type Taro from '@tarojs/taro';
import styles from './index.less';
interface Props {
    position?: 'top' | 'bottom' | 'left' | 'right'; // 气泡位置
    content?: string | React.ReactNode; // 内容
    triangleColor?: string; // 三角颜色
    backgroundColor?: string; // 背景颜色
    maxWidth?: number | string; // 最大宽度
    visible: boolean; // 显示状态
    onClose?: any; // 关闭回调
    voiceRateDimensions?: any;
    customStyle?: React.CSSProperties; // 自定义样式
}

const BubbleCard: Taro.FC<Props> = ({
    position = 'top',
    // content,
    triangleColor = '#fff',
    backgroundColor = '#fff',
    maxWidth = 300,
    visible = false,
    onClose,
    customStyle,
    voiceRateDimensions
}) => {
    if (!visible) return null;

    // 计算三角形位置样式
    const getTriangleStyle = () => {
        const baseStyle = {
            borderColor: triangleColor
            // [position]: '-16rpx'
        } as React.CSSProperties;

        switch (position) {
            case 'top':
                return { ...baseStyle, left: '50%', transform: 'translateX(-50%)' };
            case 'bottom':
                return { ...baseStyle, left: '50%', transform: 'translateX(-50%)' };
            case 'left':
                return { ...baseStyle, top: '50%', transform: 'translateY(-50%)' };
            case 'right':
                return { ...baseStyle, top: '50%', transform: 'translateY(-50%)' };
        }
    };
    return (
        <View
            //   className={`bubble-card ${position}`}
            className={`${styles.bubble_card} ${styles.bubble_card_position}`}
            style={{
                maxWidth,
                backgroundColor,
                ...customStyle
            }}
            onClick={(e) => e.stopPropagation()}
        >
            {/* 气泡三角 */}
            {/* <View className={`${styles.triangle} ${styles.triangle_top}`} style={getTriangleStyle()} /> */}

            {/* 内容区域 */}
            <View className={styles.content}>
                <View className={styles.texts_core}>综合得分</View>
                <View className={styles.text}>当前综合得分为： </View>
                <View className={styles.text}>{`内容完整性得分*${
                    100 - voiceRateDimensions?.voiceWeight
                }%+声音流畅度得分*${voiceRateDimensions?.voiceWeight}%`}</View>
                {/* <View onClick={onClose} className={styles.text}>我知道了</View> */}
                <View className={styles.bottom}>
                    <Button
                        // style={{
                        //     '--padding-md': pxTransform(28),
                        //     '--button-normal-height': pxTransform(96)
                        // }}
                        round
                        block
                        onClick={onClose}
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        我知道了
                    </Button>
                </View>
                {/* {typeof content === 'string' ? <Text className={styles.text}>{content}</Text> : content} */}
            </View>
        </View>
    );
};

export default BubbleCard;
