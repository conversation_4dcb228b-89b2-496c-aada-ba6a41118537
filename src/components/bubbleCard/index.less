// components/BubbleCard/index.scss
.bubble_card {
    position: absolute;
    padding: 20rpx;
    width: 608px;
    border-radius: 12rpx;
    box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.12);
    z-index: 1000;
    &_top {
      margin-bottom: 16rpx;
    }
  
    
   
    .content {
      position: relative;
      z-index: 1;
  .texts_core{
font-size: 32px;
font-weight: 800;
margin-bottom: 24px;
text-align: center;
color: #272C47;
  }
      .text {text-align: center;
        font-size: 28rpx;
        margin-bottom: 12px;
        color: #67686F;
        line-height: 1.5;
      }
    }
  
    .triangle {
        position: absolute;
        width: 0;
        height: 0;
        z-index: 1001; // 确保在内容层上方
  
      &_top {
        border-width: 0 16rpx 16rpx 16rpx;
        border-color: transparent transparent var(--triangle-color, #fff) transparent;
        bottom: -16rpx;
        left: 50%;
        transform: translateX(-50%);
    top: -14px !important;
    left: 59% !important;
    border-top-color: #eee0 !important;
    border-left-color: #eee0 !important;
    border-right-color: #eee0 !important;
    // border-bottom-color: rgba(221, 11, 11, 0.667) !important;
    border-bottom-color:linear-gradient(#000, #fff) !important;
    border-style: solid !important;
    transform: translateX(-50%);
      }
  
     
    }
  }
  .bottom {
    bottom: 0;
    box-sizing: border-box;
    padding: 21px 36px 48px 36px;
    
}