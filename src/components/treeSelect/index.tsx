import { Checkbox } from '@antmjs/vantui';
import { Image, View } from '@tarojs/components';
import { createSelectorQuery } from '@tarojs/taro';
import { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import IconArrow from './arrow.png';
import IconArrowDisabled from './arrow_disabled.png';
import IconCheck from './check.png';
import type { CheckTree, CheckTreeItem, TreeInstance, TreeNode, TreeProps } from './types';
import IconUnCheckDisabled from './ucheck_disabled.png';
import IconUnCheck from './uncheck.png';

const classPrefix = `lhhui-tree`;

const classBem = (className: string, obj?: { [key in string]?: boolean }) => {
    let str = className;
    if (obj) {
        Object.keys(obj).forEach((key) => {
            str += ` ${obj[key] ? `${className}-${key}` : ''}`;
        });
    }
    return str;
};

// hooks - start
function usePropsState<S>(value: S) {
    const [state, setState] = useState<S>(value);
    const isMounted = useRef(false);

    useEffect(() => {
        if (isMounted.current) {
            setState(value);
        } else {
            isMounted.current = true;
        }
    }, [value]);

    return [state, setState] as const;
}

// utils - start
/** 获取父节点的所有 key */
export const getParentKeys = (key: string, checkTree?: CheckTree, list: string[] = []): string[] | undefined => {
    if (!checkTree?.[key]) return void 0;
    if (!checkTree[key].parentKey) return list;
    list.push(checkTree[key].parentKey!);
    return getParentKeys(checkTree[key].parentKey!, checkTree, list);
};

/** 获取当前 check 的所有 key */
export const getCheckKeys = (tree?: CheckTree) => {
    if (!tree) return [];
    return Object.keys(tree).reduce((pre, key) => {
        if (tree[key].checked) {
            pre.push(key);
        }
        return pre;
    }, [] as string[]);
};

/** 获取是否有子节点 check 了 */
export const getIsSomeChildCheck = (checkItem: CheckTreeItem, checkTree: CheckTree): boolean => {
    const isSomeCheck = checkItem.childKeys?.some(
        (cKey) => checkTree![cKey].checked || getIsSomeChildCheck(checkTree![cKey], checkTree)
    );
    return Boolean(isSomeCheck);
};
// utils - end

type TitleNodeInfo = Record<string, { height: number }>;

/** 标题的最小高度 */
const TITLE_MIN_HEIGHT = 50;
/** 每个标题的下边距 */
const TITLE_MB = 6;

const Tree = forwardRef<TreeInstance, TreeProps>((props, ref) => {
    const {
        fieldNames = { title: 'label', key: 'id', children: 'children', disabled: 'disabled' },
        checkable,
        treeData,
        checkedKeys,
        defaultExpandAll,
        multiple,
        singleSelected,
        // selectable = true,
        treeCheckStrictly = true,
        selectedKeys: propsSelectedKeys,
        onCheck,
        onSelect,
        ...ret
    } = props;

    /** 首层节点的key值 */
    const [firstNodeKeys, setFirstNodeKeys] = useState<string[]>([]);
    /** 用于渲染和交互的树形结构 */
    const [checkTree, setCheckTree] = useState<CheckTree>();
    const [selectedKeys] = usePropsState<string[] | undefined>(propsSelectedKeys);
    /** 标题节点的高度等信息 */
    const [titleNodeInfo, setTitleNodeInfo] = useState<TitleNodeInfo>({});
    const isTreeRender = useRef(false);

    /** 从 treeData 中找到对应的数据 */
    const getTreeDataItem = (key: string, treeData?: TreeNode[]): TreeNode | undefined => {
        if (!treeData) return void 0;
        const item = treeData.find((t: any) => t[fieldNames.key] === key);
        if (item) return item;
        for (const t of treeData) {
            const newItem = getTreeDataItem(key, t[fieldNames.children]);
            if (newItem) return newItem;
        }
        return void 0;
    };
    /** 单选节点 */
    const onSingleCheck = (key: string, curChecked: boolean, cTree = checkTree!) => {
        Object.keys(cTree).forEach((k) => {
            cTree[k].checked = false;
        });
        if (!curChecked) return;
        // 找到最底层的子节点并选中
        (function findChildToCheck(curKey: string, childKeys?: string[]) {
            if (childKeys?.length) {
                findChildToCheck(childKeys[0], cTree[childKeys[0]].childKeys);
            } else {
                cTree[curKey].checked = true;
            }
        })(key, cTree[key].childKeys);
    };

    /** 处理父子节点的选中状态 */
    const onCheckChildAndParent = (key: string, curChecked: boolean, cTree = checkTree!) => {
        const checkItem = cTree[key];

        // 全选/不选所有子节点
        (function checkAllChild(childKeys?: string[]) {
            childKeys?.forEach((childKey) => {
                cTree[childKey].checked = curChecked;
                checkAllChild(cTree[childKey].childKeys);
            });
        })(checkItem.childKeys);

        // 处理父节点的选中状态
        (function checkAllParent(parentKey?: string) {
            if (!parentKey) return;
            if (!curChecked) {
                // 取消所有父节点的选中
                cTree[parentKey].checked = false;
                checkAllParent(cTree[parentKey].parentKey);
            } else {
                // 将所有子节点被全选的父节点也选中
                const isSiblingCheck = !!cTree[parentKey].childKeys?.every((childKey) => cTree[childKey].checked);
                if (isSiblingCheck && !cTree[parentKey][fieldNames.disabled]) {
                    // 判断兄弟节点是否也全被选中
                    cTree[parentKey].checked = true;
                    checkAllParent(cTree[parentKey].parentKey);
                }
            }
        })(checkItem.parentKey);

        // 同层单选时，使兄弟节点取消选中
        if (singleSelected && curChecked) {
            const keys = cTree[key].parentKey ? cTree[cTree[key].parentKey!].childKeys : firstNodeKeys;
            keys?.forEach((siblingKey) => {
                if (siblingKey !== key) {
                    cTree[siblingKey].checked = false;
                }
            });
        }
    };

    // 初始化选择树形结构
    useEffect(() => {
        if (!treeData?.length) return;
        const generateCheckTree = (list: TreeNode[], parentKey?: string) => {
            return list?.reduce((pre: any, cur: any) => {
                const curChecked = Boolean(checkedKeys?.includes(cur[fieldNames.key]));
                pre[cur[fieldNames.key]] = {
                    show: !!defaultExpandAll,
                    checked: curChecked,
                    parentKey
                };
                if (cur.checkable) pre[cur[fieldNames.key]].checkable = true;
                if (cur.disableCheckbox) pre[cur[fieldNames.key]].disableCheckbox = true;
                if (cur[fieldNames.disabled]) pre[cur[fieldNames.key]][fieldNames.disabled] = true;
                if (cur[fieldNames.children]?.length) {
                    pre[cur[fieldNames.key]].childKeys = cur.children.map((c) => c[fieldNames.key]);
                    const treeChild = generateCheckTree(cur.children, cur[fieldNames.key]);
                    pre = { ...pre, ...treeChild };
                }
                return pre;
            }, {} as CheckTree);
        };
        const state = generateCheckTree(treeData);
        if (!singleSelected && treeCheckStrictly) {
            checkedKeys?.forEach((key) => onCheckChildAndParent(key, true, state));
        } else {
            if (checkedKeys?.[0]) onSingleCheck(checkedKeys[0], true, state);
        }
        // onCheck?.(getCheckKeys(state));
        setFirstNodeKeys(treeData.map((item) => item.key));
        setCheckTree(state);
        isTreeRender.current = true;
    }, [treeData]);

    // 等待树形结构渲染完毕，获取 title 的高度
    useEffect(() => {
        if (!checkTree || !isTreeRender.current) return;
        const info: TitleNodeInfo = {};
        for (const key in checkTree) {
            if (Object.hasOwn(checkTree, key)) {
                createSelectorQuery()
                    .select(`.${classPrefix}-node-title-${key}`)
                    .boundingClientRect((res) => {
                        console.log(res, 'dd');
                    })
                    .exec((res) => {
                        if (res[0]) {
                            console.log('exex', res);
                            info[key] = { height: Math.max(res[0].height, TITLE_MIN_HEIGHT) + TITLE_MB };
                        }
                    });
            }
        }
        setTitleNodeInfo(info);
        isTreeRender.current = false;
    }, [checkTree]);

    useEffect(() => {
        console.log(checkedKeys, checkTree, !singleSelected, treeCheckStrictly, 'checkedKeys');
        checkedKeys?.forEach((key) => {
            try {
                const checkItem = checkTree![key];
                if (checkItem && !checkItem[fieldNames.disabled]) {
                    checkItem.checked = true;
                }
            } catch (error) {}
        });
        if (!checkTree) return;
        if (!singleSelected && treeCheckStrictly) {
            checkedKeys?.forEach((key) => onCheckChildAndParent(key, true));
        } else {
            if (checkedKeys?.[0]) onSingleCheck(checkedKeys[0], true);
        }
        if (checkedKeys?.length) {
            setCheckTree({ ...checkTree });
        }
        console.log(checkTree, 'dn');
    }, [checkedKeys]);

    useImperativeHandle(ref, () => ({
        getCheckTree: () => JSON.parse(JSON.stringify(checkTree)),
        getParentKeys(key) {
            return getParentKeys(key, checkTree);
        },
        getSiblingKeys(key) {
            if (!checkTree?.[key]) return void 0;
            if (!checkTree[key]?.parentKey) {
                return firstNodeKeys;
            }
            return checkTree[checkTree[key].parentKey!].childKeys;
        },
        getChildKeys(key) {
            return checkTree?.[key].childKeys;
        },
        getCheckKeys: () => getCheckKeys(checkTree),
        getTreeDataItem
        // setCheckedKeys: (keys: string[]) => {
        //     console.log('setCheckedKeys', keys);
        //     if (!checkTree) return;
        //     if (!singleSelected && treeCheckStrictly) {
        //         keys?.forEach((key) => onCheckChildAndParent(key, true));
        //     } else {
        //         if (keys?.[0]) onSingleCheck(keys[0], true);
        //     }
        //     if (keys?.length) {
        //         setCheckTree({ ...checkTree });
        //     }
        // }
    }));

    /** 点击选中节点 */
    const onNodeCheck = (key: string) => {
        const checkItem = checkTree![key];
        const curChecked = !checkItem.checked;
        checkItem.checked = curChecked;

        if (singleSelected) {
            onSingleCheck(key, curChecked);
        } else if (treeCheckStrictly) {
            onCheckChildAndParent(key, curChecked);
        }
        setCheckTree({ ...checkTree });

        const keys = getCheckKeys(checkTree!);

        onCheck?.(
            keys,
            keys.map((k) => getTreeDataItem(k, treeData)),
            {
                key,
                // 这步判断主要是单选时，选择父节点时只会选中其子节点
                checked: keys.includes(key) ? curChecked : false,
                parentKeys: getParentKeys(key, checkTree!),
                treeDataItem: getTreeDataItem(key, treeData)
            }
        );
    };

    /** 点击标题选中 */
    // const onTitleClick = (key: string) => {
    //     if (!selectable) return;
    //     let keys = selectedKeys ?? [];
    //     const index = keys.indexOf(key);
    //     if (index === -1) {
    //         if (multiple) keys.push(key);
    //         else keys = [key];
    //     } else {
    //         keys.splice(index, 1);
    //     }
    //     setSelectedKeys([...keys]);
    //     onSelect?.([...keys], {
    //         key,
    //         selected: index === -1,
    //         parentKeys: getParentKeys(key, checkTree!),
    //         treeDataItem: getTreeDataItem(key, treeData)
    //     });
    // };

    const getTreeChildHeight = (list: TreeNode[]) => {
        return (
            list?.reduce((pre: any, cur: any) => {
                pre += titleNodeInfo[cur[fieldNames.key]]?.height ?? TITLE_MIN_HEIGHT + TITLE_MB;
                if (checkTree![cur[fieldNames.key]].show && cur[fieldNames.children]?.length) {
                    pre += getTreeChildHeight(cur[fieldNames.children]);
                }
                return pre;
            }, 0) ?? 0
        );
    };

    const renderTreeList = (list?: TreeNode[]) => {
        if (!checkTree) return null;
        return list?.map((item: any) => {
            const checkItem = checkTree![item[fieldNames.key]];
            return (
                <View key={item[fieldNames.key]} className={`${classPrefix}-node`}>
                    <View className={`${classPrefix}-node-content`}>
                        {item[fieldNames.children]?.length ? (
                            <View
                                onClick={() => {
                                    checkItem.show = !checkItem.show;
                                    setCheckTree({ ...checkTree });
                                }}
                            >
                                <View className={`${classPrefix}-node-expand`}>
                                    <Image
                                        className={classBem(`${classPrefix}-node-expand-icon`, {
                                            show: checkItem.show
                                        })}
                                        src={
                                            item[fieldNames.disabled] || item.disableCheckbox
                                                ? IconArrowDisabled
                                                : IconArrow
                                        }
                                    />
                                </View>
                            </View>
                        ) : (
                            <span className={`${classPrefix}-node-expand-placeholder`} />
                        )}

                        <View
                            className={classBem(`${classPrefix}-node-title`, {
                                selected: selectedKeys?.includes(item[fieldNames.key]),
                                disabled: item[fieldNames.disabled],
                                [item[fieldNames.key]]: true
                            })}
                            onClick={() => {
                                checkItem.show = !checkItem.show;
                                setCheckTree({ ...checkTree });
                            }}
                        >
                            {item[fieldNames.title]}
                        </View>
                        {checkable && item.checkable !== false && (
                            <Checkbox
                                value={checkItem.checked}
                                disabled={item[fieldNames.disabled] || item.disableCheckbox}
                                renderIcon={
                                    <Image
                                        className={`${classPrefix}-node-checkbox`}
                                        src={
                                            item[fieldNames.disabled] || item.disableCheckbox
                                                ? IconUnCheckDisabled
                                                : checkItem.checked
                                                ? IconCheck
                                                : IconUnCheck
                                        }
                                    />
                                }
                                // indeterminate={getIsSomeChildCheck(checkItem, checkTree)}
                                onChange={() => {
                                    console.log('change');
                                    if (item[fieldNames.disabled] || item.disableCheckbox) return;
                                    onNodeCheck(item[fieldNames.key]);
                                }}
                            />
                        )}
                    </View>
                    <View
                        className={`${classPrefix}-node-children`}
                        // height: fit-content; 无法触发过渡效果，需要准确的值
                        // 也可通过 maxHeight 设置一个很大的值来解决，但值过大又会使过度效果难看，所以这里需要获取一个准确的高度
                        style={{ maxHeight: checkItem.show ? `${getTreeChildHeight(item.children!)}px` : 0 }}
                    >
                        {renderTreeList(item[fieldNames.children])}
                    </View>
                </View>
            );
        });
    };

    return (
        <View className={`${classPrefix} ${ret.className ?? ''}`} style={ret.style}>
            {renderTreeList(treeData)}
        </View>
    );
});
export default Tree;
