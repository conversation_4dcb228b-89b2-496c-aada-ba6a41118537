import config from '@/config';
import { encodeWAV } from '@/utils/audioUtils';
import { Image, Slider } from '@antmjs/vantui';
import { Block, View } from '@tarojs/components';
import Taro, { pxTransform, type InnerAudioContext } from '@tarojs/taro';
import { useMount, useThrottleFn, useUnmount } from 'ahooks';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

export interface AudioPlayerProps {
    url: string;
    id: string;
}
enum PLAY_STATUS {
    PLAY = 'play',
    PAUSE = 'pause',
    STOP = 'stop'
}
const Index: React.FC<AudioPlayerProps> = (props) => {
    const { url, id } = props;

    const audioContext = useRef<InnerAudioContext>();
    const [playStatus, setPlayStatus] = useState<PLAY_STATUS>(PLAY_STATUS.STOP);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [loading, setLoading] = useState(0);
    const [showAudio, setShowAudio] = useState(false);
    const pathRef = useRef<string>();

    // 格式化时间显示
    function formatTime(time: any) {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    }

    useMount(() => {
        Taro.setInnerAudioOption({
            obeyMuteSwitch: false // 解决IOS无法播放音频
        });
        audioContext.current = Taro.createInnerAudioContext();
    });

    useEffect(() => {
        console.log(url, id, 'audio');
        if (!url || !id) return;
        // 创建音频实例
        const audio = audioContext.current;
        if (!audio) return;
        audio.onPlay(() => {
            console.log('play');
            setLoading((val) => {
                if (val === 1) {
                    return 2;
                } else {
                    return val;
                }
            });
            setPlayStatus(PLAY_STATUS.PLAY);
        });
        audio.onPause(() => {
            console.log('pause');
            setPlayStatus(PLAY_STATUS.PAUSE);
        });
        audio.onTimeUpdate(() => {
            console.log(audio.currentTime, 'duration');
            setDuration(audio.duration);
            setCurrentTime(audio.currentTime);
        });
        audio.onEnded(() => {
            console.log('end');
            setCurrentTime(audio.duration);
            setPlayStatus(PLAY_STATUS.STOP);
        });
        audio.onError((err) => {
            console.log(err, 'err audio');
            setLoading(0);
            setPlayStatus(PLAY_STATUS.STOP);
        });
        audio.onCanplay(() => {
            console.log('音频可以播放', audio.duration);
            audio.play();
            audio.pause();
            setTimeout(() => {
                setDuration(audio.duration);
            }, 500);
        });
        // 判断播放音频地址文件名后缀是否是pcm
        if (url.endsWith('.wav')) {
            audio.src = url;
            setShowAudio(true);

            // Taro.request({
            //     url,
            //     method: 'GET',
            //     timeout: 600000,
            //     success: (downloadRes) => {
            //         if (downloadRes.statusCode !== 200 && !downloadRes.data) return;
            //         setDuration(downloadRes.header['Content-Length'] / (16000 * 2));
            //     },
            //     complete: () => {}
            // });
        } else {
            Taro.downloadFile({
                url,
                timeout: 600000,
                success: (downloadRes) => {
                    console.log(downloadRes, 'downlowd audio');
                    if (downloadRes.statusCode !== 200) return;
                    const fileSystemManager = Taro.getFileSystemManager();
                    fileSystemManager.readFile({
                        filePath: downloadRes.tempFilePath,
                        success: (fileRes) => {
                            console.log(fileRes);
                            setDuration(fileRes.data.byteLength / (16000 * 2));
                            const wavBuffer = encodeWAV(fileRes.data, 1, 16000);
                            // 通过数据长度计算音频时长

                            const path = `${Taro.env.USER_DATA_PATH}/${id}.wav`;

                            fileSystemManager.writeFile({
                                data: wavBuffer,
                                filePath: path,
                                success: () => {
                                    pathRef.current = path;
                                    setShowAudio(true);
                                    audioContext.current.src = path;
                                }
                            });
                        }
                    });
                },
                fail: (err) => {
                    console.log(err, 'err download audio');
                }
            });
        }
    }, [url, id]);
    const togglePlayPause = useCallback(() => {
        if (playStatus === PLAY_STATUS.PLAY) {
            audioContext.current?.pause();
        } else {
            console.log(audioContext, 'audioaudioaudioContext');
            if (currentTime === duration) {
                audioContext.current?.seek(0);
                setCurrentTime(0);
            }
            setLoading((val) => {
                if (val === 0) {
                    return 1;
                } else {
                    return val;
                }
            });
            audioContext.current?.play();
        }
    }, [playStatus, currentTime, duration]);

    const { run: onDrag } = useThrottleFn(
        useCallback(
            (e) => {
                console.log('drag', e);
                const newTime = e.detail.value;
                if (playStatus === PLAY_STATUS.PLAY) {
                    audioContext.current?.pause();
                }
                setCurrentTime(newTime);
            },
            [playStatus]
        ),
        {
            wait: 100
        }
    );

    const onSliderChange = useCallback(
        (e) => {
            console.log(e, 'eeeeee');
            audioContext.current?.pause();
            const newTime = e.detail;
            audioContext.current?.seek(newTime);
            setCurrentTime(newTime);
            // if (playStatus === PLAY_STATUS.PAUSE) {
            //     setLoading((val) => {
            //         if (val === 2) {
            //             return 0;
            //         } else {
            //             return val;
            //         }
            //     });
            //     audioContext.current?.play();
            // }
            audioContext.current?.play();
        },
        [playStatus]
    );

    useUnmount(() => {
        audioContext.current?.destroy();
        const fileSystemManager = Taro.getFileSystemManager();
        if (pathRef.current) {
            fileSystemManager.access({
                path: pathRef.current,
                success: (fileRes) => {
                    console.log(fileRes);
                    fileSystemManager.unlink({
                        filePath: pathRef.current,
                        success: (res) => {
                            console.log(res);
                        }
                    });
                }
            });
        }
    });

    return (
        <View className={styles.audio_index} style={{ display: showAudio ? 'flex' : 'none' }} catchMove>
            <View className={styles.audio_play}>
                <Image
                    onClick={togglePlayPause}
                    fit='cover'
                    width={pxTransform(42)}
                    height={pxTransform(42)}
                    src={
                        playStatus === PLAY_STATUS.PLAY
                            ? `${config.cdnPrefix}slide/stop_icon.png`
                            : loading === 1
                            ? `${config.cdnPrefix}eman/assets/voice_loading.svg`
                            : `${config.cdnPrefix}slide/play_icon.png`
                    }
                />
            </View>

            <View className={styles.audio_time}>{formatTime(currentTime)}</View>
            <View className={styles.audio_progress}>
                <Slider
                    value={currentTime}
                    min={0}
                    step={0.1}
                    activeColor='#4F66FF'
                    max={duration ? duration : 1}
                    barHeight='8px'
                    onDrag={onDrag}
                    onChange={onSliderChange}
                    renderButton={<View className={styles.customButton}> </View>}
                />
            </View>
            <View className={styles.audio_timeend}>{formatTime(duration)}</View>
        </View>
    );
};

export default Index;
