import { Button, Form, FormItem, Popup } from '@antmjs/vantui';
import { CoverView, Textarea, View } from '@tarojs/components';
import { createSelectorQuery, pxTransform } from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import styles from './index.less';
import './vant.global.less';
interface Props {
    show: boolean;
    keyHeight: number;
    onClose: () => void;
    onConfirm: (values: { description: string }) => void;
}

const Index: React.FC<Props> = (props) => {
    const { show, keyHeight, onClose, onConfirm } = props;
    const [length, setLength] = useState(0);
    const [popupMove, setPopupMove] = useState<string>('-50%');
    const formIt = Form.useForm();
    const ruleContent = {
        rule: (values: string, callback: (errMess: string) => void) => {
            if (values.length > 200) {
                callback(`文字太多啦${values.length}/200`);
            }
        }
    };

    const handleClose = () => {
        formIt.resetFields();
        setLength(0);
        onClose();
    };

    const onChange = (e: any) => {
        setLength(e.detail.value.length);
    };

    const handleSubmit = () => {
        formIt.validateFields(async (errorMessage, fieldValues: any) => {
            console.log(fieldValues, fieldValues);
            if (errorMessage && errorMessage.length) {
                return console.info('errorMessage', errorMessage);
            }
            onConfirm({ ...fieldValues });
            handleClose();
        });
    };

    useEffect(() => {
        console.log('keyHeight dialog', keyHeight);
        if (keyHeight === 0) {
            setPopupMove('-50%');
        } else {
            const query = createSelectorQuery();
            query
                .select('#popup')
                .boundingClientRect()
                .exec((res) => {
                    console.log('res dialog', res);
                    // 根据键盘的高度，将弹窗向上推
                    if (res[0]) {
                        const { height } = res[0];
                        if (height) {
                            setPopupMove(`-${pxTransform(keyHeight + height / 2)}`);
                        }
                    }
                });
        }
    }, [keyHeight]);

    return (
        <Popup
            className={classNames(styles.popup_feedback, 'popup_feedback')}
            id='popup'
            style={{ transform: `translate3D(-50%,${popupMove},0)` }}
            zIndex={110}
            round
            closeOnClickOverlay={false}
            show={show}
            onClose={handleClose}
        >
            <Form form={formIt}>
                <FormItem
                    name='description'
                    feedback='hidden'
                    required
                    layout='vertical'
                    label='问题描述'
                    trigger='onInput'
                    valueFormat={(e) => e.detail.value}
                    rules={ruleContent}
                >
                    <Textarea
                        className={styles.textarea}
                        maxlength={200}
                        onInput={onChange}
                        placeholder='请描述一下您遇到的问题，以便我们提供更好地服务。'
                    />
                </FormItem>
                <CoverView className={styles.limit}>{length}/200</CoverView>
            </Form>
            <View className={styles.action_btns}>
                <Button
                    onClick={handleClose}
                    style={{
                        '--padding-md': pxTransform(26),
                        '--button-normal-height': pxTransform(86),
                        color: '#777777'
                    }}
                    round
                    block
                    color='#F6F6F6'
                >
                    取消
                </Button>
                <Button
                    onClick={handleSubmit}
                    disabled={length === 0}
                    style={{
                        '--padding-md': pxTransform(26),
                        '--button-normal-height': pxTransform(86)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    确定
                </Button>
            </View>
        </Popup>
    );
};

export default Index;
