import { Dialog } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { View } from '@tarojs/components';
import { useMemo } from 'react';
import styles from './index.less';

interface IProps {
    show: boolean;
    setShow: (show: boolean) => void;
    type: 'A' | 'B';
    onClose?: () => void;
}

export default function ComprehensiveScoreDialog({ show, setShow, type }: IProps) {
    const content = useMemo(() => {
        if (type === 'A') {
            return '当前手机操作系统为鸿蒙5.0及以上，微信小程序暂不支持录音功能，请切换为3D类型E人后继续，如果没有3D E人请联系管理员创建。';
        } else if (type === 'B') {
            return '当前手机操作系统为鸿蒙5.0及以上，微信小程序暂不支持录音功能。';
        }
    }, [type]);
    return (
        <Dialog show={show} onClose={() => setShow(false)} showConfirmButton={false}>
            <View className={styles.content}>
                <View className={styles.title}>当前操作系统暂不支持</View>
                <View className={styles.text}>{content}</View>
                <View className={styles.bottom}>
                    <Button
                        round
                        block
                        onClick={() => setShow(false)}
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        我知道了
                    </Button>
                </View>
            </View>
        </Dialog>
    );
}
