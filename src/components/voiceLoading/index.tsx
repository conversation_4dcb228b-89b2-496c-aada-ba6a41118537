import { Icon } from '@antmjs/vantui';
import { Block, View } from '@tarojs/components';
import classnames from 'classnames';
import type { ReactNode } from 'react';
import { useEffect, useState } from 'react';
import styles from './index.less';
import voice from './voice.less';
export type LoadingProps = {
    type: 'barAnimate' | 'barStatic' | 'dot' | 'shortBar' | 'repeat' | 'barAnimateheng' | 'barStatics' | 'barAnimates';
};

const Index: React.FC<LoadingProps> = (props) => {
    const { type } = props;
    const [loadingView, setLoadingView] = useState<ReactNode>();
    useEffect(() => {
        if (type === 'dot') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 4 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_dot} />
                    ))}
                </Block>
            );
        } else if (type === 'barAnimate') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 20 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_bar_animate} />
                    ))}
                </Block>
            );
        } else if (type === 'barAnimateheng') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 20 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={voice.loading_bar_animate} />
                    ))}
                </Block>
            );
        } else if (type === 'barAnimates') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 15 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_barr_animates} />
                    ))}
                </Block>
            );
        } else if (type === 'barStatic') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 20 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_bar_static} />
                    ))}
                </Block>
            );
        } else if (type === 'barStatics') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 15 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_barr_statics} />
                    ))}
                </Block>
            );
        } else if (type === 'shortBar') {
            setLoadingView(
                <Block>
                    {Array.from({ length: 5 }).map((_, i) => (
                        // eslint-disable-next-line react/no-array-index-key
                        <View key={i} className={styles.loading_shortBar} />
                    ))}
                </Block>
            );
        } else if (type === 'repeat') {
            setLoadingView(
                <Block>
                    <Icon style={{ transform: 'rotate(45deg)' }} name='replay' size='56' color='#fff' />
                </Block>
            );
        }
    }, [type]);
    return <View className={classnames(styles.loading_box, styles[type])}>{loadingView}</View>;
};

export default Index;
