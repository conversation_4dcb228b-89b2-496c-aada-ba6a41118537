 

 

 

.loading-bar-mixin (@n, @i: 15) when (@i > 0) {
  &:nth-child(@{i}) {
    animation-delay: (@n - @i) * 0.3s;
  }
  .loading-bar-mixin(@n, @i - 1);
}
.loading_bar_static {
  width: 8px;
  height: 15px;
  background-color: #fff;
  border-radius: 8px;
}
.loading_bar_animate {
  .loading_bar_static();

  .loading-bar-mixin(20, 20);
  animation: bar 1.5s infinite;
  animation-play-state: running

}
 
 
 


@keyframes bar {
  0% {
    height: 7px;
  }
  50% {
    height: 20px;
  }
  100% {
    height: 7px;
  }
}
.loading_shortBar {
   width: 8px;
    height: 20px;
    background-color: #fff;
    border-radius: 8px;
    animation: shortBar 1s infinite;

  .loading-bar-mixin(0, 5);
}

 
@keyframes shortBar {
  0% {
    height: 7px;
  }
  50% {
    height: 20px;
  }
  100% {
    height: 7px;
  
  }
}