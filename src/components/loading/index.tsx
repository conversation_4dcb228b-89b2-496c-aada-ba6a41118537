import { DotLoading } from '@hygeia/ui';
import { View } from '@tarojs/components';
import React from 'react';

import styles from './index.less';

export type LoadingProps = {
    /** 加载状态 */
    loading: boolean;
    children?: React.ReactNode;
};

const Index: React.FC<LoadingProps> = (props) => {
    const { loading, children } = props;
    if (loading) {
        return (
            <View className={styles.loading}>
                <DotLoading />
            </View>
        );
    }
    return children;
};

export default Index;
