// import { useStorage } from '@/common';
import { DateTimeFormatEnum } from '@/constants/dateTime';
// import { StorageKey } from '@/constants/storage';
import type { HistoryVO } from '@/types/chat';
import { RoleEnum } from '@/types/chat';
// import type { UserInfo } from '@/types/common';
import MarkDownComponent from '@/components/react-remarkable';
import config from '@/config';
import '@/styles/markdown.global.less';
import { encodeWAV } from '@/utils/audioUtils';
import { Block, Image, View } from '@tarojs/components';
import type { InnerAudioContext } from '@tarojs/taro';
import Taro, { pxTransform, setClipboardData } from '@tarojs/taro';
import { useMount, useUnmount } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef, useState } from 'react';
import Typewriter from '../typewriter';
import VoiceLoading from '../voiceLoading';
import AudioPlayer from './audioPlayer';
import styles from './index.less';

export interface ChatHistory {
    // navBarHeight: number;
    hasCountdown?: boolean;
    historyPage?: boolean;
    history: HistoryVO[];
    assistantMessage?: string;
    scriptTime?: string;
    scene?: string;
    eman: {
        avatar: string;
        name: string;
    };
    emanClick?: () => void;
    historicalRecords?: any;
}

const Index: React.FC<ChatHistory> = (props) => {
    const {
        hasCountdown = false,
        historyPage,
        history,
        assistantMessage,
        scene,
        scriptTime,
        eman,
        emanClick,
        historicalRecords
    } = props;
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const HistoryEmpty = `${config.cdnPrefix}history_empty.png`;
    // const playVideo = () => {};
    const handleCopy = (content: string) => {
        if (content.trim()) {
            setClipboardData({
                data: content
            });
        }
    };

    const innerAudioContext = useRef<InnerAudioContext | null>();
    const currentVoice = useRef<string | null>();
    const [type, settype] = useState<any>('dot');
    const [preid, setpreid] = useState<any>(null);
    const [isplay, setisplay] = useState<any>(false);

    const initAudio = () => {
        innerAudioContext.current = Taro.createInnerAudioContext();
        innerAudioContext.current.onPlay(() => {
            console.log('开始播放');
            settype('barAnimate');
        });
        innerAudioContext.current.onEnded(() => {
            console.log('播放结束');
            settype('dot');
        });
    };
    const stopAudio = () => {
        if (innerAudioContext.current) {
            innerAudioContext.current.stop();
        }
    };
    const openAudio = () => {
        if (innerAudioContext.current) {
            innerAudioContext.current.play();
        }
    };

    const handleAudioPlay = (item: any) => {
        innerAudioContext.current.src = item.audioUrl;

        // 判断播放音频地址文件名后缀是否是pcm
        if (item.audioUrl.endsWith('.wav')) {
            if (innerAudioContext.current) {
                innerAudioContext.current.src = item.audioUrl;
                // innerAudioContext.current.play();
                setpreid((preid: any) => {
                    if (preid === item.id && innerAudioContext.current) {
                        setisplay(!isplay);
                        if (isplay) {
                            openAudio();
                            settype('barAnimate');
                        } else {
                            stopAudio();
                            settype('dot');
                        }

                        return item.id;
                    } else {
                        openAudio();
                        settype('barAnimate');
                        return item.id;
                    }
                });
            }
        } else {
            Taro.downloadFile({
                url: item.audioUrl,
                timeout: 600000,
                success: (downloadRes) => {
                    console.log(downloadRes, 'downlowd audio');
                    if (downloadRes.statusCode !== 200) return;
                    const fileSystemManager = Taro.getFileSystemManager();
                    fileSystemManager.readFile({
                        filePath: downloadRes.tempFilePath,
                        success: (fileRes) => {
                            console.log(fileRes);
                            const wavBuffer = encodeWAV(fileRes.data, 1, 16000);
                            // 通过数据长度计算音频时长

                            const path = `${Taro.env.USER_DATA_PATH}/${item.id}.wav`;

                            fileSystemManager.writeFile({
                                data: wavBuffer,
                                filePath: path,
                                success: () => {
                                    if (innerAudioContext.current) {
                                        innerAudioContext.current.src = path;
                                        // innerAudioContext.current.play();
                                        setpreid((preid: any) => {
                                            if (preid === item.id && innerAudioContext.current) {
                                                setisplay(!isplay);
                                                if (isplay) {
                                                    openAudio();
                                                    settype('barAnimate');
                                                } else {
                                                    stopAudio();
                                                    settype('dot');
                                                }

                                                return item.id;
                                            } else {
                                                openAudio();
                                                settype('barAnimate');
                                                return item.id;
                                            }
                                        });
                                    }
                                }
                            });
                        }
                    });
                },
                fail: (err) => {
                    console.log(err, 'err download audio');
                }
            });
        }
    };

    const handleItemClick = (item: any) => {
        currentVoice.current = item.id;
        stopAudio();
        if (innerAudioContext.current) {
            handleAudioPlay(item);
        } else {
            console.log('没有语音');
            initAudio();
            handleAudioPlay(item);
        }
    };
    // 格式化时间显示
    useMount(() => {
        Taro.setInnerAudioOption({
            obeyMuteSwitch: false // 解决IOS无法播放音频
        });
        initAudio();
    });

    useUnmount(() => {
        if (innerAudioContext.current) {
            innerAudioContext.current.destroy();
        }
    });
    return (
        <Block>
            <View className={styles.chat}>
                {scriptTime && dayjs(scriptTime).isSame(dayjs(), 'day') ? (
                    <View className={styles.chat_time}>{dayjs(scriptTime).format(DateTimeFormatEnum.HM)}</View>
                ) : dayjs(scriptTime).isSame(dayjs().subtract(1, 'day'), 'day') ? (
                    <View className={styles.chat_time}>昨天 {dayjs(scriptTime).format(DateTimeFormatEnum.HM)}</View>
                ) : dayjs(scriptTime).isSame(dayjs(), 'year') ? (
                    <View className={styles.chat_time}>
                        {dayjs(scriptTime).format(DateTimeFormatEnum.MONTHTIMEMIN)}
                    </View>
                ) : (
                    <View className={styles.chat_time}>{dayjs(scriptTime).format(DateTimeFormatEnum.DATETIMEMIN)}</View>
                )}
                {scene && (
                    <View className={styles.chat_scene}>
                        <View className={styles.chat_scene_text}>{scene}</View>
                    </View>
                )}
                {history.length > 0 || assistantMessage ? (
                    <View className={styles.history}>
                        {history.map((item, index) => {
                            item.isplay = false;
                            // console.log(history, 'historyhistoryhistory');
                            if (item.role === RoleEnum.ASSISTANT) {
                                return (
                                    <View key={`${item.role}_${index}`} className={styles.history_assistant}>
                                        <Image
                                            onClick={emanClick}
                                            className={styles.history_user_avatar}
                                            src={item.avatar ? item.avatar : eman.avatar}
                                        />
                                        <View
                                            className={styles.history_assistant_message}
                                            onLongPress={() => handleCopy(item.content)}
                                        >
                                            <MarkDownComponent source={item.content} />
                                        </View>
                                    </View>
                                );
                            }
                            if (item.role === RoleEnum.USER && item.content?.trim()) {
                                return (
                                    <View key={`${item.role}_${index}`} className={styles.history_user}>
                                        <View
                                            className={styles.history_user_message}
                                            onLongPress={() => handleCopy(item.content)}
                                        >
                                            <MarkDownComponent source={item.content} />
                                            {item.audioUrl && historicalRecords && (
                                                <View onClick={() => handleItemClick(item)}>
                                                    {' '}
                                                    <AudioPlayer
                                                        type={type}
                                                        currentVoice={currentVoice.current}
                                                        url={item.audioUrl}
                                                        id={item.id}
                                                        backgroundColor='#ffffff'
                                                        boxStyle={{ display: 'inline-block' }}
                                                    />{' '}
                                                </View>
                                            )}
                                            {/* <Icon name='paly' onClick={playVideo} /> */}
                                        </View>
                                        <Image
                                            className={styles.history_user_avatar}
                                            src={(item?.avatar as string) || AvatarDefault}
                                        />
                                    </View>
                                );
                            }
                        })}
                        {assistantMessage && (
                            <View className={styles.history_assistant}>
                                <Image
                                    className={styles.history_user_avatar}
                                    src={(eman.avatar as string) || AvatarDefault}
                                />
                                <View
                                    className={classNames(
                                        styles.history_assistant_message,
                                        styles.history_assistant_message_saying
                                    )}
                                >
                                    <Typewriter text={assistantMessage} typingSpeed={50} />
                                </View>
                            </View>
                        )}
                    </View>
                ) : historyPage && !history?.length ? (
                    <View className={styles.history_empty}>
                        <Image
                            className={styles.history_empty_img}
                            width={pxTransform(400)}
                            height={pxTransform(205)}
                            src={HistoryEmpty}
                        />
                        <View className={styles.history_empty_txt}>双方都保持了沉默，无对话记录</View>
                    </View>
                ) : null}
            </View>
        </Block>
    );
};

export default Index;
