@import '@/styles/index.less';

page {
    background-color: #fff;
}

.chat {
    .wrap-box(24px 32px);

    margin-top: 70px; // 系统顶部高度
    
    // 真机上，iphone14可以取到安全高度，魅族18没有取到安全高度（兼容，但是返回0）
    // 赋值200px的话，iphone偏少一点点，魅族正好?
    // 赋值140px + env(safe-area-inset-top)，iphone正好，魅族偏少
    // 赋值200px + env(safe-area-inset-top)，iphone偏多，魅族正好
    padding-top: 200px; // 微信顶部高度+'内容由ai生成'tips栏
    // padding-top: calc(200px + constant(safe-area-inset-top));
    // padding-top: calc(200px + env(safe-area-inset-top)); 
    
    // padding-bottom: 100px; // 估的
    // margin-bottom: 20px; // 发送栏距离底部高度
    // margin-bottom: calc(20px + constant(safe-area-inset-bottom)); 
    // margin-bottom: calc(20px + env(safe-area-inset-bottom)); 

    &_time {
        .font(24px,#9597a0,400);

        margin-bottom: 24px;
        text-align: center;
    }
    &_scene {
        .wrap-box(18px 24px);
        .font(24px,#9597a0,400);

        text-align: center;
        background-color: #f5f6f8;
        border-radius: 16px;
        &_text {
            .multi-ellipsis(2);
        }
    }
}

.history {
    padding-top: 40px;


    &_assistant {
        display: flex;
        margin-bottom: 32px;

        &_avatar {
            margin-top: 5px;
        }

        &_message {
            .wrap-box(24px 32px);
            .font(30px,#272c47,400);

            max-width: 480px;
            margin-left: 16px;
            background-color: #f5f6f8;
            border-radius: 12px 24px 24px 24px;
            
            // &_saying {
            //     .wrap-box(24px 30px);
            // }
        }
    }
    &_user {
        display: flex;
        justify-content: flex-end;
        margin-bottom: 32px;

        &_avatar {
            margin-top: 5px;
            border-radius: 14px;
            width: 80px;
            height: 80px;
        }

        &_message {
            .wrap-box(24px 32px);
            .font(30px,#FFFFFF,400);

            max-width: 480px;
            margin-right: 16px;
            background-color: #4f66ff;
            border-radius: 24px 12px 24px 24px;
        }
    }
    &_empty {
        margin-top: 250px;
        text-align: center;

        &_img {
            margin: auto;
        }

        &_txt {
            margin-top: 24px;
            color: #9597a0;
            text-align: center;
            font-size: 28px;
            line-height: 40px;
        }
    }
}
