import VoiceLoading from '@/components/voiceLoading';
import config from '@/config';
import { encodeWAV } from '@/utils/audioUtils';
import { Image, Slider } from '@antmjs/vantui';
import { Block, View } from '@tarojs/components';
import Taro, { pxTransform, type InnerAudioContext } from '@tarojs/taro';
import { useMount, useThrottleFn, useUnmount } from 'ahooks';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

export interface AudioPlayerProps {
    url: string;
    id: string;
    currentVoice: any;
    type: any;
}
enum PLAY_STATUS {
    PLAY = 'play',
    PAUSE = 'pause',
    STOP = 'stop'
}
const Index: React.FC<AudioPlayerProps> = (props) => {
    const { url, id, currentVoice, type } = props;
    const audioContext = useRef<InnerAudioContext>();
    const [playStatus, setPlayStatus] = useState<PLAY_STATUS>(PLAY_STATUS.STOP);
    const [currentTime, setCurrentTime] = useState(0);
    const [duration, setDuration] = useState(0);
    const [loading, setLoading] = useState(0);
    const [showAudio, setShowAudio] = useState(false);
    const pathRef = useRef<string>();
    console.log(currentVoice, id, type, 'currentVoicecurrentVoicecurrentVoice');
    // 格式化时间显示
    function formatTime(time: any) {
        const minutes = Math.floor(time / 60);
        const seconds = Math.floor(time % 60);
        return `${minutes}:${seconds < 10 ? '0' : ''}${seconds}`;
    }

    useMount(() => {
        Taro.setInnerAudioOption({
            obeyMuteSwitch: false // 解决IOS无法播放音频
        });
        audioContext.current = Taro.createInnerAudioContext();
    });

    useEffect(() => {
        console.log(url, id, 'audio');
        if (!url || !id) return;
        // 创建音频实例
        const audio = audioContext.current;
        if (!audio) return;
        audio.onPlay(() => {
            console.log('play');
            setLoading((val) => {
                if (val === 1) {
                    return 2;
                } else {
                    return val;
                }
            });
            setPlayStatus(PLAY_STATUS.PLAY);
        });
        audio.onPause(() => {
            console.log('pause');
            setPlayStatus(PLAY_STATUS.PAUSE);
        });
        audio.onTimeUpdate(() => {
            console.log(audio.currentTime, 'duration');
            setDuration(audio.duration);
            setCurrentTime(audio.currentTime);
        });
        audio.onEnded(() => {
            console.log('end');
            setCurrentTime(audio.duration);
            setPlayStatus(PLAY_STATUS.STOP);
        });
        audio.onError((err) => {
            console.log(err, 'err audio');
            setLoading(0);
            setPlayStatus(PLAY_STATUS.STOP);
        });
        // 判断播放音频地址文件名后缀是否是pcm
        if (url.endsWith('.wav')) {
            audio.src = url;
            setShowAudio(true);
            Taro.request({
                url,
                method: 'GET',
                timeout: 600000,
                success: (downloadRes) => {
                    if (downloadRes.statusCode !== 200 && !downloadRes.data) return;
                    setDuration(downloadRes.header['Content-Length'] / (16000 * 2));
                },
                complete: () => {}
            });
        } else {
            Taro.downloadFile({
                url,
                timeout: 600000,
                success: (downloadRes) => {
                    console.log(downloadRes, 'downlowd audio');
                    if (downloadRes.statusCode !== 200) return;
                    const fileSystemManager = Taro.getFileSystemManager();
                    fileSystemManager.readFile({
                        filePath: downloadRes.tempFilePath,
                        success: (fileRes) => {
                            console.log(fileRes);
                            setDuration(fileRes.data.byteLength / (16000 * 2));
                            const wavBuffer = encodeWAV(fileRes.data, 1, 16000);
                            // 通过数据长度计算音频时长

                            const path = `${Taro.env.USER_DATA_PATH}/${id}.wav`;

                            fileSystemManager.writeFile({
                                data: wavBuffer,
                                filePath: path,
                                success: () => {
                                    pathRef.current = path;
                                    setShowAudio(true);
                                    audioContext.current.src = path;
                                }
                            });
                        }
                    });
                },
                fail: (err) => {
                    console.log(err, 'err download audio');
                }
            });
        }
    }, [url, id]);

    useUnmount(() => {
        audioContext.current?.destroy();
        const fileSystemManager = Taro.getFileSystemManager();
        if (pathRef.current) {
            fileSystemManager.access({
                path: pathRef.current,
                success: (fileRes) => {
                    console.log(fileRes);
                    fileSystemManager.unlink({
                        filePath: pathRef.current,
                        success: (res) => {
                            console.log(res);
                        }
                    });
                }
            });
        }
    });

    return (
        <View className={styles.audio_index} style={{ display: showAudio ? 'flex' : 'none' }} catchMove>
            <View className={styles.audio_play}>
                {currentVoice === id ? (
                    type == 'dot' ? (
                        <Image
                            className={styles.audio_play_img}
                            // onClick={togglePlayPause}
                            fit='cover'
                            width={pxTransform(40)}
                            height={pxTransform(40)}
                            src={`${config.cdnPrefix}historicalRecords/playVoice.png`}
                        />
                    ) : (
                        <Image
                            className={styles.audio_play_img}
                            // onClick={togglePlayPause}
                            fit='cover'
                            width={pxTransform(40)}
                            height={pxTransform(40)}
                            src={`${config.cdnPrefix}historicalRecords/Frame.png`}
                        />
                    )
                ) : (
                    <Image
                        className={styles.audio_play_img}
                        // onClick={togglePlayPause}
                        fit='cover'
                        width={pxTransform(40)}
                        height={pxTransform(40)}
                        src={`${config.cdnPrefix}historicalRecords/playVoice.png`}
                    />
                )}
                {/* <Image
                    // onClick={togglePlayPause}
                    fit='cover'
                    width={pxTransform(200)}
                    height={pxTransform(40)}
                    src={`${config.cdnPrefix}historicalRecords/soundWave.png`}
                /> */}
                {currentVoice === id ? (
                    type == 'dot' ? (
                        <VoiceLoading type='barStatics' />
                    ) : (
                        <VoiceLoading type='barAnimates' />
                    )
                ) : (
                    <VoiceLoading type='barStatics' />
                )}
            </View>
            <View className={styles.audio_timeend}>{formatTime(duration)}</View>
        </View>
    );
};

export default Index;
