// import { upload } from '@/services/common';
import { ConfigProvider } from '@hygeia/ui';
import { withNativeProps } from '@pigjs/react-utils';
import { View } from '@tarojs/components';
import React from 'react';
import Loading from '../loading';

import type { NativeProps } from '@pigjs/react-utils/es/withNativeProps';

type PageProps = NativeProps & {
    children?: React.ReactNode;
    loading?: boolean;
};

const configProviderProps = {
    themeVars: {
        primaryColor: '#1989FA'
    },
    // uploader: {
    //     afterRead: async (file: any) => {
    //         return await upload(file.url);
    //     }
    // },
    list: {
        reqConfig: {
            pageSizeKey: 'pageSize',
            pageKey: 'pageNo'
        }
    }
};

const App: React.FC<PageProps> = (props) => {
    const { loading = false } = props;

    return (
        <ConfigProvider {...configProviderProps}>
            {/* 加一个空的form，使得 @hygeia/ui 的 Form 组件能正常渲染 */}
            <form />
            {withNativeProps(
                props,
                <View>
                    <Loading loading={loading}>{props.children}</Loading>
                </View>
            )}
        </ConfigProvider>
    );
};

export default App;

/** 函数式使用公共页面 */
export function withCommonPage(children: React.ReactNode, props?: Omit<PageProps, 'children'>) {
    return <App {...props}>{children}</App>;
}
