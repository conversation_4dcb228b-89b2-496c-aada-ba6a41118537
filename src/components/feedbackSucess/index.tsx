import config from '@/config';
import { Button, Popup } from '@antmjs/vantui';
import { Image, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import styles from './index.less';
interface Props {
    show: boolean;
    onClose: () => void;
}

const Index: React.FC<Props> = (props) => {
    const { show, onClose } = props;
    const FeedbackSuccessImage = `${config.cdnPrefix}chat/feedback_success.svg`;
    return (
        <Popup className={styles.popup_feedback} zIndex={110} show={show} round onClose={onClose}>
            <Image className={styles.popup_feedback_img} mode='aspectFit' src={FeedbackSuccessImage} />
            <View className={styles.popup_feedback_tips}>
                <View>感谢您的反馈</View>
                <View>我们会尽快排查解决问题</View>
            </View>
            <View className={styles.action_btns}>
                <Button
                    onClick={onClose}
                    style={{
                        '--padding-md': pxTransform(26),
                        '--button-normal-height': pxTransform(86)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    关闭
                </Button>
            </View>
        </Popup>
    );
};

export default Index;
