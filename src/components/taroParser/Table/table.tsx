import { View } from '@tarojs/components';
import { Component } from 'react';
import Decode from '../Decode/decode';
import type { BaseProps } from '../types/BaseProps';
import config from '../utils/config';
import { styleToObj } from '../utils/dom';

export default class Table extends Component<BaseProps, {}> {
    options = {
        addGlobalClass: true
    };

    buildTr(trList) {
        return trList
            .filter((o) => o.tag)
            .map((trs) => {
                let child;
                if (trs.child) {
                    child = trs.child.filter((o) => o.tag);
                } else {
                    child = [];
                }
                const children = child.map((item) => {
                    let style;

                    if (item.attr && item.attr.style) {
                        style = styleToObj(item.attr.style);
                        if (item.attr && item.attr.width) {
                            style.width = item.attr.width;
                        }
                    }
                    return (
                        <View key={item.index} className={config.classPrefix + item.attr.class} style={style}>
                            {item.child && <Decode nodes={item} />}
                        </View>
                    );
                });
                return (
                    <View key={trs.index} className={config.classPrefix + trs.attr.class}>
                        {children}
                    </View>
                );
            });
    }

    render() {
        const { data } = this.props;
        let child;
        let style;
        if (data) {
            style = styleToObj(data.attr && data.attr.style ? data.attr.style : '');
            if (data.attr && data.attr.width) {
                style.width = data.attr.width;
            }

            child = (data.child || [])
                .filter((o) => o.tag)
                .map((item) => {
                    const c = this.buildTr(item.child);
                    return (
                        <View key={item.index} className={config.classPrefix + item.attr.class}>
                            {c}
                        </View>
                    );
                });
        }
        return (
            data &&
            data.tag === 'table' && (
                <View className={`${config.classPrefix}h2w__tableParent`}>
                    <View className={config.classPrefix + data.attr.class} style={style}>
                        {child}
                    </View>
                </View>
            )
        );
    }
}
