/* 正文样式 */
.h2w-light {
  color: #333;
  // background-color: white;
}

/**  标题  **/
.h2w-light .h2w__h1,
.h2w-light .h2w__h2 {
  border-color: #eee;
}


/**  表格  **/
.h2w-light .h2w__thead .h2w__tr {
  background-color: #f6f8fa;
}

.h2w-light .h2w__table .h2w__tr:nth-child(2n) {
  background-color: #fbfcfd;
}

.h2w-light .h2w__th,
.h2w-light .h2w__td {
  border-color: #dfe2e5;
}


/**  代码块  **/
.h2w-light .h2w__pre {
  background-color: #f6f8fa;
  border-color: #eaedf0;
}

.h2w-light .h2w__code {
  background-color: #f6f8fa;
  border-color: #eaedf0;
}


/**  块元素  **/
.h2w-light .h2w__blockquote {
  border-left-color: #dfe2e5;
}

/**  内连元素  **/
.h2w-light .h2w__a {
  color: #1aad16;
  border-color: #b9d9b8;
}

.h2w-light .h2w__hr {
  background-color: #eee;
}

.h2w-light .h2w__mark {
  background: yellow;
  color: black;
}

.h2w-light .h2w__lineNum {
  color: #ccc;
}

/*

github.com style (c) Vasily Polovnyov <<EMAIL>>

*/
.h2w-light {
  .hljs {
    display:block;
    overflow-x:auto;
    padding:.5em;
    background:white;
    color:black
  }
  .hljs-comment,
  .hljs-quote,
  .hljs-variable {
    color:#008000
  }
  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-built_in,
  .hljs-name,
  .hljs-tag {
    color:#00f
  }
  .hljs-string,
  .hljs-title,
  .hljs-section,
  .hljs-attribute,
  .hljs-literal,
  .hljs-template-tag,
  .hljs-template-variable,
  .hljs-type,
  .hljs-addition {
    color:#a31515
  }
  .hljs-deletion,
  .hljs-selector-attr,
  .hljs-selector-pseudo,
  .hljs-meta {
    color:#2b91af
  }
  .hljs-doctag {
    color:#808080
  }
  .hljs-attr {
    color:#f00
  }
  .hljs-symbol,
  .hljs-bullet,
  .hljs-link {
    color:#00b0e8
  }
  .hljs-emphasis {
    font-style:italic
  }
  .hljs-strong {
    font-weight:bold
  }
}


