/* 正文样式 */
.h2w-dark {
  color:white;
  // background-color:#000;
}

/**  标题  **/
.h2w-dark .h2w__h1,
.h2w-dark .h2w__h2 {
    border-color:#3d3d3d;
}


/**  表格  **/
.h2w-dark .h2w__thead .h2w__tr {
    background-color:#1f1f1f;
}
.h2w-dark .h2w__table .h2w__tr:nth-child(2n){
    background-color:#090909;
}
.h2w-dark .h2w__th,
.h2w-dark .h2w__td {
    border-color:#333;
}


/**  代码块  **/
.h2w-dark .h2w__pre,
.h2w-dark .h2w__pre .h2w__code {
    background-color:#1b1b1b;
    border-color:#262626;
}

.h2w-dark .h2w__code {
    background-color:#272822;
    border-color:#1b1c18;
}


/**  块元素  **/
.h2w-dark .h2w__blockquote {
    border-left-color:#10230f;
}

/**  内连元素  **/
.h2w-dark .h2w__a {
    color:#1aad16; border-color:#4d804b;
}

.h2w-dark .h2w__hr {
    background-color:#242424;
}

.h2w-dark .h2w__mark {
    background:yellow;
    color:black;
}

.h2w-dark .h2w__todoCheckbox .wx-checkbox-input {
    background:#2e2e2e; border-color:#494949;
}
.h2w-dark .h2w__todoCheckbox .wx-checkbox-input.wx-checkbox-input-checked {
    background:green; border-color:#4d804b;
}
.h2w-dark .h2w__todoCheckbox .wx-checkbox-input.wx-checkbox-input-checked::before {
    color:white;
}
.h2w-dark .h2w__lineNum {
    color:#494949;
}

/*
Monokai style - ported by Luigi Maselli - http://grigio.org
*/
.h2w-dark{
  .hljs {
    display: block;
    overflow-x: auto;
    padding: 0.5em;
    background: #272822; color: #ddd;
  }

  .hljs-tag,
  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-literal,
  .hljs-strong,
  .hljs-name {
    color: #f92672;
  }

  .hljs-code {
    color: #66d9ef;
  }

  .hljs-class .hljs-title {
    color: white;
  }

  .hljs-attribute,
  .hljs-symbol,
  .hljs-regexp,
  .hljs-link {
    color: #bf79db;
  }

  .hljs-string,
  .hljs-bullet,
  .hljs-subst,
  .hljs-title,
  .hljs-section,
  .hljs-emphasis,
  .hljs-type,
  .hljs-built_in,
  .hljs-builtin-name,
  .hljs-selector-attr,
  .hljs-selector-pseudo,
  .hljs-addition,
  .hljs-variable,
  .hljs-template-tag,
  .hljs-template-variable {
    color: #a6e22e;
  }

  .hljs-comment,
  .hljs-quote,
  .hljs-deletion,
  .hljs-meta {
    color: #75715e;
  }

  .hljs-keyword,
  .hljs-selector-tag,
  .hljs-literal,
  .hljs-doctag,
  .hljs-title,
  .hljs-section,
  .hljs-type,
  .hljs-selector-id {
    font-weight: bold;
  }
}
