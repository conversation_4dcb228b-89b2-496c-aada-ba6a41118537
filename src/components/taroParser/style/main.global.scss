/* 正文样式 */
.h2w {
    font-family: 'STHeitiTC-Light', 'Microsoft YaHei Light', 'Hiragino Sans GB', -apple-system, system-ui,
        BlinkMacSystemFont, 'Segoe UI', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol';
    // line-height: 1.5;
    word-wrap: break-word;
    word-break: break-all;
}

.h2w__main {
    margin: 0 30px 0 30px;
    // padding-top: 20px;
}

/**  标题  **/
.h2w__h1,
.h2w__h2,
.h2w__h3,
.h2w__h4,
.h2w__h5,
.h2w__h6 {
    font-weight: bold;
}

/**  设置行间元素样式  **/
.h2w__span,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em,
.h2w__code,
.h2w__sub,
.h2w__sup,
.h2w__g-emoji,
.h2w__mark,
.h2w__ins {
    display: inline;
}

.h2w__h1 {
    margin-bottom: 20px;
    padding-bottom: 10px;
    font-size: 42px;
    border-bottom-width: 6px;
    border-bottom-style: double;
}

.h2w__h2 {
    margin-bottom: 18px;
    padding-bottom: 8px;
    font-size: 40px;
    border-bottom-width: 1px;
    border-bottom-style: solid;
}

.h2w__h3 {
    margin-bottom: 12px;
    padding-bottom: 6px;
    font-size: 38px;
}

.h2w__h4 {
    margin-bottom: 12px;
    padding-bottom: 4px;
    font-size: 36px;
}

.h2w__h5 {
    margin-bottom: 12px;
    padding-bottom: 2px;
    font-size: 34px;
}

.h2w__h6 {
    margin-bottom: 12px;
}

/**  表格  **/
.h2w__tableParent {
    width: 100%;
    overflow-x: auto;
}

.h2w__table {
    display: table;
    width: 100%;
    margin-bottom: 40px;
    white-space: nowrap;
    border-collapse: collapse;
    border-spacing: 0;
}

.h2w__table .h2w__tr:nth-child(2n) {
    background-color: red;
}

.h2w__colgroup {
    display: table-column-group;
}

.h2w__col {
    display: table-column;
}

.h2w__thead {
    display: table-header-group;
}

.h2w__tbody {
    display: table-row-group;
}

.h2w__tfoot {
    display: table-footer-group;
}

.h2w__tr {
    display: table-row;
}

.h2w__th,
.h2w__td {
    display: table-cell;
    padding: 8px 16px;
    font-size: 28px;
    border-style: solid;
    border-width: 1px;
}

.h2w__th {
    font-weight: bold;
}

/**  代码块  **/
.h2w__pre {
    margin-bottom: 40px;

    /* white-space:nowrap; */
    padding: 20px;
    overflow-x: auto;
    font-size: 28px;
    white-space: nowrap;
    word-break: normal;
    tab-size: 4;
    border-style: solid;
    border-width: 1px;
}

.h2w__pre .h2w__code {
    padding: 0;
    font-size: 100%;
    border: 0;
}

.h2w__pre,
.h2w__code {
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace, 'STHeitiTC-Light',
        'Microsoft YaHei Light', -apple-system, system-ui, BlinkMacSystemFont;
}

.h2w__code {
    margin: 0 4px;
    padding: 4px 8px;
    overflow-x: auto;
    font-size: 80%;
    border-style: solid;
    border-width: 1px;
    border-radius: 8px;
}

.h2w__pre .h2w__span,
.h2w__pre .h2w__a,
.h2w__pre .h2w__span,
.h2w__pre .h2w__b,
.h2w__pre .h2w__strong,
.h2w__pre .h2w__i,
.h2w__pre .h2w__em {
    display: inline;
}

.h2w__pre {
    display: block;
    white-space: pre;
}

.h2w__pre .h2w__code {
    display: block;
    min-width: 100%;
    font-size: 80%;
    white-space: pre;
}

/**  列表  **/
.h2w__ul,
.h2w__ol {
    margin-top: 24px;
    margin-bottom: 24px;
    padding-left: 1rem;
}

.h2w__ul .h2w__ol,
.h2w__ol .h2w__ul {
    margin-bottom: 0;
}

.h2w__li {
    display: list-item;
}

/**  todo  **/
.h2w__todogroup {
    margin-bottom: 40px;
}

.h2w__todolist {
    display: block;
}

.h2w__todogroup .h2w__todogroup {
    padding-left: 1.6rem;
}

/**  一级ol样式  **/
.h2w__ol {
    list-style-type: decimal;
}

/**  二级ol样式  **/
.h2w__ul .h2w__ol,
.h2w__ol .h2w__ol {
    list-style-type: lower-roman;
}

/**  三级ol样式  **/
.h2w__ul .h2w__ul .h2w__ol,
.h2w__ul .h2w__ol .h2w__ol,
.h2w__ol .h2w__ul .h2w__ol,
.h2w__ol .h2w__ol .h2w__ol {
    list-style-type: lower-alpha;
}

/**  一级ul样式  **/
.h2w__ul {
    list-style-type: disc;
}

/**  二级ul样式  **/
.h2w__ul .h2w__ul,
.h2w__ol .h2w__ul {
    list-style-type: circle;
}

/**  三级样式  **/
.h2w__ol .h2w__ol .h2w__ul,
.h2w__ol .h2w__ul .h2w__ul,
.h2w__ul .h2w__ol .h2w__ul,
.h2w__ul .h2w__ul .h2w__ul {
    list-style-type: square;
}

/**  块元素  **/
.h2w__p {
    margin: 20px 0 20px 0;

    /* word-wrap:break-word;
  word-break:break-all; */

    /* text-align:justify; */
}

.h2w__div {
    /* text-align:justify; */
}

.h2w__blockquote {
    padding: 0 20px;
    border-left-width: 8px;
    border-left-style: solid;
}

/**  内连元素  **/
.h2w__a,
.h2w__span,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em {
    display: inline;
}

.h2w__b,
.h2w__strong {
    font-weight: bold;
}

.h2w__i,
.h2w__em {
    font-style: italic;
}

/**  文本删除线  **/
.h2w__s,
.h2w__strike,
.h2w__del {
    text-decoration: line-through;
}

/**  文本下划线  **/
.h2w__ins,
.h2w__u {
    text-decoration: underline;
}

/**  链接  **/
.h2w__a {
    color: #0366d6;
    line-height: 1.2em;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    cursor: pointer;
}

.h2w__hr {
    height: 8px;
    margin: 40px 0;
}

/**  荧光标记  **/
.h2w__mark {
    border-radius: 4px;
}

/**  上标、下标  **/
.h2w__sup,
.h2w__sub {
    position: relative;
    font-size: 75%;
}

.h2w__sup {
    top: -0.5em;
}

.h2w__sub {
    bottom: -0.25em;
}

/**  emoji表情  **/
.h2w__g-emoji {
    margin: 0 0.1em;
    font-family: 'Apple Color Emoji', 'Segoe UI', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

/**  内置元素  **/
image,
video {
    max-width: 100%;
}

video {
    width: 100%;
    margin: 10px auto;
}

image {
    vertical-align: middle;
}

video {
    font-size: 0;
}

.h2w__latex--line {
    margin: 4px 8px;
    vertical-align: middle;
}

.h2w__latex--block {
    display: block;
    margin: 1em auto;
}

.h2w__yuml {
    display: block;
}

.h2w__yumlBox {
    width: 100%;
    overflow-x: auto;
}

.h2w__yumlView {
    margin: 0 auto;
    padding-bottom: 40px;
}

/**  代码行号  **/
.h2w__lineNum {
    float: left;
    margin: 0 1em 0 0;
    padding: 0;
    text-align: right;
}

.h2w__lineNumLine {
    list-style: none;
}

.h2w__img {
    max-width: 100%;
    height: 224px;
    margin: 8px 0;
}

@import 'theme/light';
@import 'theme/dark';
