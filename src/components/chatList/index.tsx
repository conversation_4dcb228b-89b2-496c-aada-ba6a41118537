import config from '@/config';

import { getChatList } from '@/services/chat';
import type { ChatHistoryVO } from '@/types/chat';
import { Image, Popup } from '@antmjs/vantui';
import { ScrollView, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import { useDebounceFn, useThrottleFn } from 'ahooks';
import { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import ChartItem from './chartItem';
import styles from './index.less';

const HistoryEmpty = `${config.cdnPrefix}history_empty.png`;

export interface ChatListProps {
    show: boolean;
    onCloseFn: () => void;
    refBase: any;
    onClick: (item: ChatHistoryVO) => void;
}

const Index: React.FC<ChatListProps> = (props) => {
    const { show, onCloseFn, onClick } = props;
    const hasMore = useRef(true);
    const [more, setMore] = useState(true);
    const pageNo = useRef(1);
    const touchStart = useRef<Touch>();
    const [loading, setLoading] = useState(false);

    // 请求数据，并设置到state中
    const [list, setList] = useState<ChatHistoryVO[]>([]);

    // 分页处理
    const requestData = useCallback(async () => {
        if (!hasMore.current) {
            setLoading(false);
            return;
        }
        try {
            const listRes = await getChatList({ pageNo: pageNo.current, pageSize: 20 });
            const { data } = listRes.data;

            if (hasMore) {
                pageNo.current += 1;
            }
            setList((oldList) => {
                const list = [...oldList, ...data.records];
                if (list.length >= Number(data.total)) {
                    pageNo.current = 1;
                    hasMore.current = false;
                    setMore(false);
                }
                return list;
            });
        } catch (error) {
            console.log(error);
        } finally {
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        if (props.show) {
            pageNo.current = 1;
            setList([]);
            hasMore.current = true;
            setMore(true);
            setLoading(true);
            requestData();
        }
    }, [props.show]);

    const { run: goDetail } = useThrottleFn((item: ChatHistoryVO) => onClick(item), {
        wait: 1000,
        leading: true,
        trailing: false
    });
    // 监听滚动事件
    const { run: onScroll } = useDebounceFn(
        () => {
            setLoading(true);
            requestData();
        },
        {
            wait: 500
        }
    );

    const initQuery = () => {
        pageNo.current = 1;
        setList(() => {
            return [];
        });
        hasMore.current = true;
        setMore(true);
        setLoading(true);
        requestData();
    };

    useImperativeHandle(props.refBase, () => ({
        // 暴露方法出去
        onInitQuery: initQuery //
    }));
    const onCloseSunFn = (paramShow: boolean) => {
        // 隐藏后重置加载标志
        // if (paramShow) {
        //     if (!hasMore.current) {
        //         setList(() => {
        //             return [];
        //         });
        //         hasMore.current = true;
        //     }
        // }
        onCloseFn();
    };

    const onTouchStart = (e: any) => {
        const [ts] = e.changedTouches;
        touchStart.current = ts;
    };
    const onTouchEnd = (e: any) => {
        const [te] = e.changedTouches;
        // 判断是否是向左滑
        if (te.clientX - touchStart.current!.clientX < -80 && te.clientY - touchStart.current!.clientY < 50) {
            onCloseSunFn(props.show);
        }
    };

    return (
        <Popup
            position='left'
            show={show}
            safeAreaInsetTop
            style={{
                transitionDuration: '200ms',
                WebkitTransitionDuration: '200ms',
                overflowY: 'hidden'
            }}
            safeAreaInsetBottom
            onClose={() => onCloseSunFn(props.show)}
        >
            <View className={styles.history}>
                <View className={styles.title}>历史对话</View>
                <View className={styles.list} onTouchStart={onTouchStart} onTouchEnd={onTouchEnd}>
                    <ScrollView
                        className={styles.paginationList}
                        onScrollToLower={onScroll}
                        scrollY
                        enhanced
                        lowerThreshold={100}
                    >
                        {list && list.length > 0
                            ? list?.map((item) => <ChartItem key={item.id} {...item} onClick={() => goDetail(item)} />)
                            : !loading && (
                                  <View className={styles.history_empty}>
                                      <Image
                                          className={styles.history_empty_img}
                                          width={pxTransform(400)}
                                          height={pxTransform(205)}
                                          src={HistoryEmpty}
                                      />
                                      <View className={styles.history_empty_txt}>暂无对话记录</View>
                                  </View>
                              )}

                        {loading ? (
                            <View className={styles.list_tip}>加载中...</View>
                        ) : list.length > 0 ? (
                            more ? (
                                <View className={styles.list_tip}>上拉加载</View>
                            ) : (
                                <View className={styles.list_tip}>没有更多了</View>
                            )
                        ) : null}
                    </ScrollView>
                </View>
            </View>
        </Popup>
    );
};

export default Index;
