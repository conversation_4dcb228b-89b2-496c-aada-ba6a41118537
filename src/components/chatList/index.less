@import '@/styles/index.less';
.history {
  height: 100vh;
  width: 78vw;
  display: flex;
  flex-direction: column;
  padding-top: 94px;
  box-sizing: border-box;
}

.title {
  font-size: 32px;
  font-weight: 600;
  margin-top: 21px;
  margin-left: 32px;
  margin-bottom: 30px;
}

.list {
  height:calc(100vh - 100px);
  overflow-y: hidden;
  background-color: #fff;

}

.item {
  height: 142px;
  border-radius: 32px;
  background: #F5F6F8;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  margin-left: 32px;
  margin-right: 32px;
  padding-right: 35px;
  box-sizing: border-box;
  &-avatar {
    margin-left: 25px;
  }
  &-content {
    margin-left: 20px;
    flex: 1;
    width: 0;
  }
  &-name {
    font-size: 32px;
    font-weight: 600;
    .ellipsis();
  }
  &-time {
    font-size: 24px;
    font-weight: 400;
    color: #9597a0;
    margin-top: 8px;
    .ellipsis();
  }
  &-score {
    display: flex;
    align-items: center;
    font-size: 28px;
    font-weight: 400;
    &-num {
      font-size: 40px;
      font-weight: 600;
    }
    &-unit {
      font-size: 20px;
      font-weight: 400;
      margin-left: 4px;
    }
    &-none {
      color: #61626a;
   
    }
    &-fail {
      color: #ff3734;
 
    }
    &-afoot{
      color: #3C83FF;

    }
  }
  &-icon {
    margin-left: 14px;
  }
}
.paginationList{
  height:calc(93vh - 100px);
}
.history {
  &_empty {
    text-align: center;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -40%);
    &_img {
        margin: auto;
    }

    &_txt {
        margin-top: 24px;
        color: #9597a0;
        text-align: center;
        font-size: 28px;
        line-height: 40px;
    }
}
}

.list_tip {
  text-align: center;
  font-size: 28px;
  color: #9597a0;
  margin-top: 20px;
}