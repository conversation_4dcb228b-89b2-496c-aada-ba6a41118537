import config from '@/config';
import type { ChatHistoryVO } from '@/types/chat';
import { Icon, Image } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import dayjs from 'dayjs';
import styles from './index.less';
import PPTiconNew from './PPT_icon_New.png';
interface Props extends ChatHistoryVO {
    onClick: (id: number | string) => void;
}

const Index: React.FC<Props> = (props: any) => {
    const { createTime, id, emanType, lastChatText, type } = props;
    return (
        <View className={styles.item} onClick={() => props.onClick(id)}>
            {type == 3 ? (
                <Image
                    className={styles['item-avatar']}
                    width={pxTransform(92)}
                    height={pxTransform(92)}
                    src={PPTiconNew}
                />
            ) : (
                <Image
                    className={styles['item-avatar']}
                    width={pxTransform(92)}
                    radius={pxTransform(16)}
                    height={pxTransform(92)}
                    src={props.avatar}
                />
            )}

            <View className={styles['item-content']}>
                <View className={styles['item-name']}>{type == 3 ? props.scriptName : props.name}</View>
                <View className={styles['item-time']}>
                    {(emanType === 1 || type == 3) &&
                        (dayjs(createTime).isSame(dayjs(), 'year')
                            ? dayjs(createTime).format('MM月DD日 HH:mm')
                            : dayjs(createTime).format('YYYY年MM月DD日 HH:mm'))}
                    {emanType === 2 && lastChatText}
                </View>
            </View>
            <View className={styles['item-score']} style={{ display: emanType === 2 ? 'none' : 'block' }}>
                {props.done ? (
                    <View>
                        {props.status === 1 && <Text className={styles['item-score-none']}>无报告</Text>}
                        {props.status === 2 && props.name && (
                            <View>
                                <Text className={styles['item-score-num']}>{props.score}</Text>
                                <Text className={styles['item-score-unit']}>分</Text>
                            </View>
                        )}
                        {type == 3 && props.status === 2 && (
                            <View>
                                <Text className={styles['item-score-num']}>{props.score}</Text>
                                <Text className={styles['item-score-unit']}>分</Text>
                            </View>
                        )}
                        {props.status === 3 && <Text className={styles['item-score-fail']}>生成失败</Text>}
                        {props.status === 4 && <Text className={styles['item-score-fail']}>生成中</Text>}
                    </View>
                ) : (
                    <Text className={styles['item-score-afoot']}>进行中</Text>
                )}
            </View>
            <Icon
                className={styles['item-icon']}
                style={{ display: emanType === 2 ? 'none' : 'block' }}
                name='arrow'
                size={pxTransform(24)}
                color='#9597A0'
            />
        </View>
    );
};

export default Index;
