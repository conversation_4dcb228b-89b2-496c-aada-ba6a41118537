import config from '@/config';
import { ScriptType } from '@/constants/scriptType';
import type { ScriptVO } from '@/types/chat';
import { ActionSheet, Image } from '@antmjs/vantui';
import { Block, Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import classnames from 'classnames';
import React, { useEffect, useState } from 'react';
import styles from './index.less';

const scriptImg = `${config.cdnPrefix}sceneExercise/script.png`;

export type LoadingProps = {
    showScript: boolean;
    currentScript: ScriptVO | undefined;
    emanName?: string;
    showScriptClose: () => void;
};

const Index: React.FC<LoadingProps> = (props) => {
    const { showScript, currentScript, showScriptClose, emanName } = props;
    const [randomText, setRandomText] = useState<string>('');

    // action-sheet按住下滑收起
    let startY = 0;
    const moveThreshold = 30; // 滑动的阈值，超过这个值则认为开始滑动
    const handleTouchStart = (e: TouchEvent) => {
        startY = e.touches[0].clientY;
    };
    const handleTouchMove = (e: TouchEvent) => {
        const moveY = e.touches[0].clientY - startY;
        if (moveY > moveThreshold) {
            showScriptClose();
        }
    };
    function handleRandomText(randomFlag: number, randomNum: number, question: any[]) {
        let text = '';
        //  0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
        switch (randomFlag) {
            case 0:
                text = '按顺序提问所有问题';
                break;
            case 1:
                text = '随机提问所有题目';
                break;
            case 2:
                text = `随机提问${randomNum}题`;
                break;
            case 3:
                // 向上取整randomNum
                text = `随机提问${question.length}题`;
                break;
            default:
                text = '';
        }
        setRandomText(text);
    }
    useEffect(() => {
        if (currentScript) {
            handleRandomText(currentScript.randomFlag, currentScript.randomNum, currentScript.question);
        }
    }, [currentScript]);

    return (
        <ActionSheet
            show={showScript}
            zIndex={110}
            onClose={() => showScriptClose()}
            closeOnClickOverlay
            safeAreaInsetBottom
            // @ts-ignore
            style={{ '--action-sheet-header-height': pxTransform(124) }}
        >
            {currentScript && (
                <View className={styles.container}>
                    <View
                        className={styles.container_bar}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                    />
                    {/* 此处要求按住后上下拖拽吗？ */}
                    {currentScript.name && (
                        <View className={styles.container_name}>
                            <Image width={pxTransform(32)} height={pxTransform(32)} src={scriptImg} />
                            <Text>{currentScript.name}</Text>
                        </View>
                    )}

                    {currentScript.type === ScriptType.QUESTION && (
                        <View className={styles.container_content}>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>产品</Text>
                                <Text className={styles.container_item_content}>{currentScript.product}</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>提问方式</Text>
                                <Text className={styles.container_item_content}>
                                    {/* {currentScript.randomFlag ? '随机提问所有题目' : '按顺序提问所有问题'} */}
                                    {randomText}
                                </Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>时限</Text>
                                <Text className={styles.container_item_content}>{currentScript.timeLimit}分钟</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>试题</Text>
                                <View className={styles.container_item_content}>
                                    {currentScript
                                        ? currentScript.question.map((item, index) => (
                                              <View key={item.id}>
                                                  {index + 1}.{item.question}
                                              </View>
                                          ))
                                        : null}
                                </View>
                            </View>
                        </View>
                    )}
                    {currentScript.type === ScriptType.SKILL && (
                        <View className={styles.container_content}>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>拜访对象</Text>
                                <Text className={styles.container_item_content}>
                                    {currentScript?.visitObject}
                                    {currentScript?.visitObject && currentScript?.department && '-'}
                                    {currentScript?.department &&
                                        currentScript?.department
                                            .split(',')
                                            .map((item) => item.split('-')[item.split('-').length - 1])
                                            .join('、')}
                                </Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>背景</Text>
                                <Text className={styles.container_item_content}>
                                    {emanName &&
                                        (currentScript.backdrop
                                            ? currentScript.backdrop.replaceAll('【拜访对象】', emanName)
                                            : '')}
                                </Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>目标</Text>
                                <Text className={styles.container_item_content}>
                                    {emanName && currentScript.goal
                                        ? currentScript.goal.replaceAll('【拜访对象】', emanName)
                                        : ''}
                                </Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>产品</Text>
                                <Text className={styles.container_item_content}>{currentScript.product}</Text>
                            </View>
                            {/* <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>时间</Text>
                                <Text className={styles.container_item_content}>{currentScript.time}</Text>
                            </View> */}
                            {currentScript.location && (
                                <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                    <Text className={styles.container_item_title}>地点</Text>
                                    <Text className={styles.container_item_content}>{currentScript.location}</Text>
                                </View>
                            )}
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>时限</Text>
                                <Text className={styles.container_item_content}>{currentScript.timeLimit}分钟</Text>
                            </View>
                        </View>
                    )}
                </View>
            )}
        </ActionSheet>
    );
};

export default Index;
