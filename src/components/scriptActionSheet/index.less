@import '@/styles/index.less';

.container {
    margin-top: 42px;
    padding-bottom: 20px;
    box-sizing: border-box;
    position: relative;
    max-height: 60vh;
   display: flex;
   flex-direction: column;
    &_bar{
        position: absolute;
        width: 100%;
        height: 50px;
        &::before{
            content: '';
            position: absolute;
            top:20px;
            left: 50%;
            transform: translateX(-50%);
            // content: '';
            width:128px;
            height: 12px;
            flex-shrink: 0;
            border-radius: 174px;
            background: #D9D9D9;
        }
    }
    &_content {
        flex: 1;
        height: 0;
        overflow-y: auto
    }
    &_name{
        .font(28px,#272C47,600);
        .flex-center;

        padding: 50px 0 20px;

        image{
            width: 32px;
            height: 32px;
        }
        text{
            margin-left: 6px;
        }
    }
    &_item {
        .wrap-box(16px 32px 16px 32px);

        // margin-bottom: 24px;
        min-height: 108px;
        border-radius: 16px;
        &_title {
            .font(32px,#272C47,600);
        }
        &_content {
            .font(28px,#67686F,400);
        }
    }
    &_horizontal {
        display: flex;
        align-items: center;
        justify-self: flex-start;

        .container_item_content {
            margin-left: 48px;
            line-height: 40px;
        }
    }
    &_vertical {
        display: flex;
        justify-content: center;
        flex-direction: column;

        .container_item_content {
            margin-top: 10px;
            line-height: 40px;
        }
    }
}