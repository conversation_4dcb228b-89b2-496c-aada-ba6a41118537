
import { Image, View } from '@tarojs/components';
import styles from './index.less';
import config from "@/config";
export type StarProps = {
    value?: number;
    isGray?: boolean;
};

// props默认值
const Index: React.FC<StarProps> = (props) => {
    const { value = 0, isGray = false } = props;
    const StarOff = `${config.cdnPrefix}star_off.svg`;
    const StarOffGray = `${config.cdnPrefix}star_off_gray.svg`;
    const StarOn = `${config.cdnPrefix}star_on.svg`;
    return (
        <View className={styles.star_box}>
            {[...Array(value ? value : 0)].map(() => (
                <Image src={StarOn} mode='aspectFit' className={styles.star_item} />
            ))}
            {[...Array(3 - (value ? value : 0))].map(() => (
                <Image src={isGray ? StarOffGray : StarOff} mode='aspectFit' className={styles.star_item} />
            ))}
        </View>
    );
};

export default Index;
