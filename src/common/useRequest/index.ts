import { Storage } from "@/common";
import { getWxCode, getWxWorkCode, weworkLogin, wxlogin } from '@/services/user';
import { useRequest as useRequestA } from 'ahooks';
import { useShow } from '../useShow';

import type { Response } from '@/types/common';
import { loginSuccess } from '@/utils/login';
// import Taro from '@tarojs/taro';

import { StorageEnvKey } from '@/constants/storage';
import frontLogout from '@/utils/fontLogout';
import Taro from '@tarojs/taro';
import type { Options as AOptions, Plugin } from 'ahooks/es/useRequest/src/types';
import { useRef } from 'react';
import { HomePath } from "@/constants/homePath";

type Service<TData, TParams extends any[]> = (...args: TParams) => Response<TData>;

interface Options<TData, TParams extends any[]> extends AOptions<TData, TParams> {
    /** DidShow 页面显示的时候是否刷新 默认 true */
    showRefresh?: boolean;
}

export function useRequest<TData, TParams extends any[] = any[]>(
    service: Service<TData, TParams>,
    options?: Options<TData, TParams>,
    plugins?: Plugin<TData, TParams>[]
) {
    const { showRefresh, ...resetOptions } = options || {};
    const loadingDelayRef = useRef(0);

    const _options = {
        // refreshShow show 刷新的时候，默认 延迟200毫秒，避免闪烁
        loadingDelay: showRefresh ? loadingDelayRef.current : undefined,
        ...resetOptions,
        manual: true
    };

    const resetService = async (...args: any) => {
        const { data } = await service(...args);

        if (data.code === 401 || data.code === 20004) {
            Taro.showToast({
                icon: 'none',
                title: '登录信息失效，请重新登录',
                mask: true
            });
            const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);
            const isWework = Storage.get(StorageEnvKey.IS_WEWORK);
            const homePath = Storage.get(StorageEnvKey.HOME_PATH);
            frontLogout();
            setTimeout(async () => {
                console.log('isWework', isWework);
                if (isWework === '1') {
                    // 企业微信小程序
                    /*const { code } = await getWxWorkCode();
                    console.log('wxwork code', code);
                    const loginRes = await weworkLogin(code);
                    const { data: loginData } = loginRes.data;

                    if (loginRes.data.code === 200) {
                        loginSuccess(loginData); // 存token和用户信息
                        const { userInfo } = loginData;
                        Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息

                    }*/
                    Taro.reLaunch({ url: homePath ? homePath: HomePath.DIALOG });
                } else {
                    if (isEnterprise === 1) {
                        // 药企
                        Taro.redirectTo({
                            url: '/pages/login/index/index'
                        });
                    } else {
                        /*const { code } = await getWxCode();
                        const loginRes = await wxlogin(code);
                        const { data: loginData } = loginRes.data;

                        if (loginRes.data.code === 200) {
                            loginSuccess(loginData); // 存token和用户信息
                            const { userInfo } = loginData;

                            const phoneData = userInfo.phoneNumber || '';
                            if (!phoneData) {
                                Taro.redirectTo({ url: '/pages/login/index/index' });
                            } else {
                                // 有手机号则保存信息
                                Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息
                                Taro.reLaunch({ url: homePath ? homePath: HomePath.DIALOG });
                            }
                        } else {
                            Taro.reLaunch({ url: homePath ? homePath: HomePath.DIALOG });
                        }*/
                        Taro.reLaunch({ url: HomePath.MIDDLE });
                    }
                }
            }, 1500);
        } else if (data.code === 200) {
            return data.data;
        } else {
            Taro.showToast({
                icon: 'none',
                title: data.message
            });

            return null;
        }
    };

    // @ts-ignore
    const response = useRequestA(resetService, _options, plugins);

    useShow(() => {
        if (!resetOptions?.manual) {
            // @ts-ignore
            response.run();
            loadingDelayRef.current = 200;
        }
    }, !showRefresh);

    return response;
}
