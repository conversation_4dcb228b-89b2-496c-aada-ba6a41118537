/** 第三的方法或者工具从这里统一导出 收敛依赖 */

import { Events } from '@tarojs/taro';

/** 全局 发布者订阅者模式 */
export const events = new Events();

// utils
export * from '@hera/practical-utils';
export { useEvent, useMemo, useMount, usePropsValue, useSetState, useUnmount, useUpdate } from '@hera/react-utils';
/** request */
export { default as request, setConfig, setMessage } from '@hera/request';
export * from '@hera/type-utils';
export * from './logger';
export * from './storage';
// useModel
export * from './useModel';
export { useRequest } from './useRequest';
export * from './useShow';
