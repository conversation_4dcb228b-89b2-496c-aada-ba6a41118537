import React from 'react';

export class Logger {
    name: string;
    constructor(name: string) {
        this.name = name;
    }

    public error(content: string, stack?: any) {
        const message = `${this.name}: ${content}`;
        console.error(message, stack);
    }

    public log(content: string, ...params: any[]) {
        console.log(`${this.name}: ${content}. ${params.join('.')}`);
    }

    public warn(content: string, ...params: any[]) {
        console.warn(`${this.name}: ${content}. ${params.join('.')}`);
    }

    public debug(content: string, ...params: any[]) {
        console.debug(`${this.name}: ${content}. ${params.join('.')}`);
    }

    public info(content: string, ...params: any[]) {
        console.info(`${this.name}: ${content}. ${params.join('.')}`);
    }
}

export function useLogger(name: string) {
    const loggerRef = React.useRef(new Logger(name));

    return loggerRef.current;
}
