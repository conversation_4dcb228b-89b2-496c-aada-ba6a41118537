import { useEvent } from '@hera/react-utils';
import { isFunction, isNil } from '@hera/type-utils';
import Taro from '@tarojs/taro';
import { useState } from 'react';
import { Logger } from '../logger';

import type { StorageEnvKey } from '@/constants/storage';

export class Storage {
    static logger = new Logger('Storage');

    /**
     * 写入
     * @param key
     * @param value
     * @param validTime 有效时间，单位 ms
     */
    static set(key: keyof typeof StorageEnvKey | string, value: any, validTime?: number) {
        if (isNil(value)) {
            this.del(key);
            return;
        }
        Taro.setStorageSync(
            key,
            JSON.stringify({
                value,
                validTime,
                writeTime: Date.now()
            })
        );
    }
    static get(key: keyof typeof StorageEnvKey | string) {
        const result = Taro.getStorageSync(key);
        if (isNil(result) || result === '') {
            return null;
        }
        try {
            const { value, validTime, writeTime } = JSON.parse(result);
            if (validTime) {
                if (Date.now() - writeTime > validTime) {
                    // 已过期
                    this.del(key);
                    return null;
                }
            }
            return value;
        } catch (err) {
            this.logger.error(`获取 ${key} 失败`, err);
            // 清除掉不合法数据，防止持续报错
            this.del(key);
            return null;
        }
    }
    static del(key: keyof typeof StorageEnvKey | string) {
        Taro.removeStorageSync(key);
    }

    static clear() {
        Taro.clearStorageSync();
    }
}

/**
 * 修改 Storage 状态
 *
 * @param data 需要修改的值
 * @param validTime 有效时间，单位 ms
 */
type setStateAction<T> = (data: React.SetStateAction<T | null>, validTime?: number) => void;

/**
 * 本地缓存和state同步 hook
 * 值为 null 或者 undefined 的时候会自动删除key
 *
 * @param key 键值
 * @param emptyValue 为空时的默认值 默认 null
 */
export function useStorage<T = any>(key: keyof typeof StorageEnvKey | string) {
    const [state, setState] = useState<T | null>(() => {
        return Storage.get(key);
    });

    const _setState = useEvent((data: React.SetStateAction<T | null>, validTime?: number) => {
        setState((prevState) => {
            const value = isFunction(data) ? data(prevState) : data;
            const resetValue = isNil(value) ? null : value;
            Storage.set(key, resetValue, validTime);
            return resetValue;
        });
    });

    return [state, _setState] as [T | null, setStateAction<T>];
}
