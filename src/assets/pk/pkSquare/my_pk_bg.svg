<svg width="359" height="186" viewBox="0 0 359 186" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_510_5894)">
<g clip-path="url(#clip0_510_5894)">
<path d="M213 6H335C343.837 6 351 13.1634 351 22V33H213V6Z" fill="url(#paint0_linear_510_5894)"/>
<path d="M213 6H335C343.837 6 351 13.1634 351 22V33H213V6Z" fill="white" fill-opacity="0.8" style="mix-blend-mode:hard-light"/>
<path d="M8 22C8 13.1634 15.1634 6 24 6H179.5H214.83C219.608 6 224.135 8.13512 227.175 11.8214L239.013 26.1786C242.052 29.8649 246.58 32 251.358 32H265.25H286.688H308.125H329.562H351V160C351 168.837 343.837 176 335 176H24C15.1635 176 8 168.837 8 160V22Z" fill="white"/>
<g filter="url(#filter1_f_510_5894)">
<path d="M103.2 -69.0327C130.805 -69.0327 153.184 -91.4111 153.184 -119.016C153.184 -146.622 130.805 -169 103.2 -169C75.5948 -169 53.2163 -146.622 53.2163 -119.016C53.2163 -91.4111 75.5948 -69.0327 103.2 -69.0327Z" fill="#ADC7FF" fill-opacity="0.6"/>
</g>
<g filter="url(#filter2_f_510_5894)">
<path d="M32.0735 255.147C81.2674 255.147 121.147 215.267 121.147 166.073C121.147 116.88 81.2674 77 32.0735 77C-17.1204 77 -57 116.88 -57 166.073C-57 215.267 -17.1204 255.147 32.0735 255.147Z" fill="#BFA8FF" fill-opacity="0.6"/>
</g>
<g filter="url(#filter3_f_510_5894)">
<path d="M342.616 138.233C384.378 138.233 418.233 104.378 418.233 62.6163C418.233 20.8546 384.378 -13 342.616 -13C300.855 -13 267 20.8546 267 62.6163C267 104.378 300.855 138.233 342.616 138.233Z" fill="#FFD796" fill-opacity="0.6"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_510_5894" x="0" y="0" width="359" height="186" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_510_5894"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_510_5894" result="shape"/>
</filter>
<filter id="filter1_f_510_5894" x="-126.784" y="-349" width="459.967" height="459.968" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="90" result="effect1_foregroundBlur_510_5894"/>
</filter>
<filter id="filter2_f_510_5894" x="-237" y="-103" width="538.147" height="538.146" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="90" result="effect1_foregroundBlur_510_5894"/>
</filter>
<filter id="filter3_f_510_5894" x="87" y="-193" width="511.233" height="511.232" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="90" result="effect1_foregroundBlur_510_5894"/>
</filter>
<linearGradient id="paint0_linear_510_5894" x1="215.966" y1="11.6182" x2="332.396" y2="69.6623" gradientUnits="userSpaceOnUse">
<stop stop-color="#31BCFF"/>
<stop offset="0.249572" stop-color="#9676FF"/>
<stop offset="0.411552" stop-color="#BE64FE"/>
<stop offset="0.517542" stop-color="#E157CB"/>
<stop offset="0.620535" stop-color="#EF5794"/>
<stop offset="0.697281" stop-color="#FD683F"/>
<stop offset="0.852337" stop-color="#FE7C2B"/>
<stop offset="1" stop-color="#FFA10B"/>
</linearGradient>
<clipPath id="clip0_510_5894">
<rect x="8" y="6" width="343" height="170" rx="16" fill="white"/>
</clipPath>
</defs>
</svg>
