import { dataflowProvider, Storage } from '@/common';
import Layout from '@/layouts';
// import '@/services/request';
import Taro, { useDidShow, useLaunch, useUnload } from '@tarojs/taro';
import './app.less';
import frontLogout from './utils/fontLogout';

import tenantSettingUtil from '@/utils/tenantSettingUtils';
import { AppIdConsts } from './constants/appid';
import { StorageEnvKey } from './constants/storage';
import { uploadLog } from './services/common';
import { getUserInfo } from './services/user';
import { createFilePath } from './utils/fileUtils';

const App = (props: any) => {
    /** 小程序小程序版本更新（小程序版本/小程序配置版本） */
    // 对话tts文件目录

    const checkForUpdate = () => {
        const updateManager: any = Taro.getUpdateManager();
        updateManager.onCheckForUpdate(async (res: any) => {
            console.log('onCheckForUpdate', res);
        });

        updateManager.onUpdateReady(() => {
            Taro.showModal({
                title: '更新提示',
                content: '新版本已经准备好，立即重启应用？',
                confirmText: '我知道了',
                showCancel: false
            }).then((mRes: any): void => {
                if (mRes.confirm) {
                    updateManager.applyUpdate();
                }
            });
        });

        updateManager.onUpdateFailed(() => {
            Taro.showModal({
                title: '更新失败',
                content: '请删除小程序后重新打开',
                confirmText: '我知道了',
                showCancel: false
            }).then((): void => {});
        });
    };

    useDidShow(async () => {
        Taro.nextTick(() => {
            if (Taro.getEnv() !== Taro.ENV_TYPE.WEB) {
                checkForUpdate();
            }
        });
        const res = await Taro.getSystemInfoSync();
        if (res.environment && res.environment === 'wxwork') {
            console.log('getSystemInfoSync', res);
        } else {
            try {
                await getUserInfo();
            } catch (error: any) {
                console.log('error', error);
                if (error.data) {
                    if (error.data.code === 20003) {
                        frontLogout();
                        Taro.showModal({
                            title: '无登录权限',
                            content: '如有疑问，请联系管理员。',
                            showCancel: false,
                            confirmText: '好的',
                            confirmColor: '#4F66FF'
                        });
                    }
                }
            }
        }
        const token = Storage.get(StorageEnvKey.TOKEN);
        if (token) {
            tenantSettingUtil();
        }
    });

    useLaunch(async () => {
        Taro.eventCenter.on('uploadLogs', (data: string) => {
            // console.log('uploadLogs', data);
            uploadLog(data);
        });
        try {
            const res = await Taro.getSystemInfoSync();
            console.log('getSystemInfoSync', res);
            if (res.environment && res.environment === 'wxwork') {
                // 企业微信小程序
                Storage.set(StorageEnvKey.IS_WEWORK, '1');
                console.log('申请录音权限开始');

                /* Taro.onMemoryWarning((res) => {
                    console.log('onMemoryWarning', res);
                    Taro.showToast({
                        title: '内存不足',
                        icon: 'none'
                    });
                }); */
            } else {
                Storage.set(StorageEnvKey.IS_WEWORK, '0');
            }
        } catch (error: any) {
            console.log('getSystemInfoSync error', error);
            Storage.set(StorageEnvKey.IS_WEWORK, '0');
        }
        try {
            const accountInfo = Taro.getAccountInfoSync();

            const appid = accountInfo.miniProgram.appId;

            if (appid === AppIdConsts.ybDoctor) {
                // 语贝ai
                Storage.set(StorageEnvKey.IS_ENTERPRISE, 0);
            } else {
                Storage.set(StorageEnvKey.IS_ENTERPRISE, 1);
            }
        } catch (error) {
            Storage.set(StorageEnvKey.IS_ENTERPRISE, 1);
            console.log('getEnterpriseFlag', error);
        }
        createFilePath('chat');

        console.log('app lanch');
    });

    useUnload(() => {
        Taro.eventCenter.off('uploadLogs');
    });

    // @ts-ignore
    return dataflowProvider(<Layout>{props.children}</Layout>);
};
export default App;
