@import '@/styles/index.less';
.checkscript {
  border-radius: 24px;
  background-color: #fff;
  margin: 0 32px 24px;
  overflow: hidden;

  &_head {
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 32px;
    box-sizing: border-box;
    &_ing {
      background: linear-gradient(90deg, rgba(79, 102, 255, 0.2) 0%, rgba(79, 102, 255, 0) 100%);
    }
    &_wait {
      background: linear-gradient(90deg, rgba(255, 131, 54, 0.2) 0%, rgba(255, 131, 54, 0.2) 0%, rgba(255, 128, 128, 0) 100%);
    }
    &_done {
      background: linear-gradient(90deg, rgba(153, 153, 153, 0.2) 0%, rgba(204, 204, 204, 0) 100%);
    }
  }
  &_title {
    font-size: 32px;
    font-weight: bold;
  }
  &_status {
    width: 105px;
    height: 40px;
    font-size: 24px;
    color: #fff;
    text-align: center;
    line-height: 40px;
    border-radius: 8px;
    transform: skew(-8deg);
    &_text {
      transform: skew(8deg);
    }
    &_wait {
      background-color: #FF8336;

    }
    &_ing {
      background-color: #4F66FF;
    }
    &_done {
      background-color: #CCC;
    }
  }

  &_body {
    padding: 16px 32px;
    box-sizing: border-box;
  }
  &_data {
    margin-bottom: 16px;
    display: flex;
    font-size: 24px;
  }
  &_label {
    color: #9E9E9E;
  }
  &_value {
    flex: 1;
    width: 0;
    color: #666;
    &_num {
      color: #4F66FF;
    }
  }
  &_dept_1 {
    // .ellipsis();
    .multi-ellipsis(1);
  }
  &_dept_2 {
    .multi-ellipsis(2);
    // line-height: 1.5;
    // max-height: 3em;
    // position: relative;
    // &::before {
    //   content: '';
    //   height: calc(100% - 36px);
    //   float: right;
    // }
    // &::after {
    //   content: '';
    //   width: 100%;
    //   height: 100%;
    //   position: absolute;
    //   background: #fff;
    // }
  }

  // &_more {
  //   float: right;
  //   clear: both;
  //   color: #4F66FF;
  // }
}
