import type { LevelStageItemVO } from '@/types/activity';
import { View } from '@tarojs/components';
import classNames from 'classnames';
import styles from './styles.less';
export interface CheckPointItemProps extends LevelStageItemVO {
    departmentLine: number;
    onClick?: (id: string) => void;
    onDepartmentClick?: () => void;
}
const Index: React.FC<CheckPointItemProps> = (props) => {
    const {
        id,
        name,
        levelStageNum,
        startDate,
        endDate,
        deptName,
        progress,
        departmentLine,
        onClick,
        onDepartmentClick
    } = props;

    return (
        <View className={styles.checkscript} onClick={() => onClick && onClick(id)}>
            <View
                className={classNames(styles.checkscript_head, [
                    {
                        [styles.checkscript_head_wait]: progress === 1,
                        [styles.checkscript_head_ing]: progress === 0,
                        [styles.checkscript_head_done]: progress === 2
                    }
                ])}
            >
                <View className={styles.checkscript_title}>{name}</View>
                <View
                    className={classNames(styles.checkscript_status, [
                        {
                            [styles.checkscript_status_wait]: progress === 1,
                            [styles.checkscript_status_ing]: progress === 0,
                            [styles.checkscript_status_done]: progress === 2
                        }
                    ])}
                >
                    <View className={styles.checkscript_status_text}>
                        {progress === 1 ? '待开始' : progress === 0 ? '进行中' : '已结束'}
                    </View>
                </View>
            </View>
            <View className={styles.checkscript_body}>
                <View className={styles.checkscript_data}>
                    <View className={styles.checkscript_label}>关卡总数：</View>
                    <View className={classNames(styles.checkscript_value, styles.checkscript_value_num)}>
                        {levelStageNum}个关卡
                    </View>
                </View>
                <View className={styles.checkscript_data}>
                    <View className={styles.checkscript_label}>活动时间：</View>
                    <View className={styles.checkscript_value}>
                        {startDate}至{endDate}
                    </View>
                </View>
                <View className={styles.checkscript_data} onClick={onDepartmentClick}>
                    <View className={styles.checkscript_label}>参与部门：</View>
                    <View
                        className={classNames(styles.checkscript_value, {
                            [styles.checkscript_dept_1]: departmentLine === 1,
                            [styles.checkscript_dept_2]: departmentLine === 2
                        })}
                    >
                        {/* {departmentLine === 2 && (
                            <Text className={styles.checkscript_more} onClick={onDepartmentClick}>
                                查看
                            </Text>
                        )} */}
                        {deptName.split(',').join('、')}
                    </View>
                </View>
            </View>
        </View>
    );
};

export default Index;
