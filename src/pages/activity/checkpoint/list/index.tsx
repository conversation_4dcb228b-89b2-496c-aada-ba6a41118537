import { Page } from '@/components';
import { getCheckpointList } from '@/services/activity';
import { NavBar } from '@antmjs/vantui';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import { navigateBack, navigateTo, useDidShow } from '@tarojs/taro';
import { useDebounceFn, useThrottleFn } from 'ahooks';
import CheckpointEmpty from '../../assets/checkpoint_empty.svg';

import { useRef, useState } from 'react';
import CheckpointItem from '../../components/CheckpointItem';
import styles from './index.less';
const App = () => {
    const [list, setList] = useState<any>([]);
    const page = useRef(1);
    const [loadState, setLoadState] = useState<'loading' | 'complete' | 'more'>('loading');
    const loadStateRef = useRef<'loading' | 'complete' | 'more'>();

    const goBack = () => {
        navigateBack();
    };
    const loadMore = async () => {
        if (loadStateRef.current === 'loading' || loadStateRef.current === 'complete') return;
        setLoadState('loading');
        loadStateRef.current = 'loading';
        const result = await getCheckpointList({ pageNo: page.current, pageSize: 12 });
        const { records, total } = result.data.data;
        if (records.length > 0) {
            let newData = [];
            if (page.current === 1) {
                setList(records);
                newData = records;
            } else {
                const newData = list.concat(records);
                console.log(newData);
                setList(newData);
            }
            page.current += 1;
            setLoadState('more');
            loadStateRef.current = 'more';
            if (Number(total) === newData.length) {
                setLoadState('complete');
                loadStateRef.current = 'complete';
                console.log('complete');
            }
        } else {
            if (page.current === 1) {
                setList(records);
            }
            setLoadState('complete');
            loadStateRef.current = 'complete';
        }
    };

    const { run: navigateToDetail } = useThrottleFn(
        (id) => {
            console.log('navigateToDetail', id);
            navigateTo({
                url: `/pages/activity/checkpoint/detail/index?id=${id}`
            });
        },
        {
            wait: 1000
        }
    );

    const { run: onScroll } = useDebounceFn(
        () => {
            console.log('onScroll');
            loadMore();
        },
        {
            wait: 300
        }
    );
    useDidShow(() => {
        page.current = 1;
        setLoadState('more');
        loadStateRef.current = 'more';

        loadMore();
    });

    return (
        <Page className={styles.page}>
            <NavBar className={styles.navbar} title='闯关活动' leftArrow safeAreaInsetTop onClickLeft={goBack} />
            <ScrollView className={styles.content} onScrollToLower={onScroll} scrollY lowerThreshold={50}>
                {!list || list.length === 0 ? (
                    <View className={styles.points_empty}>
                        <Image className={styles.points_empty_icon} src={CheckpointEmpty} />
                        <Text>暂无闯关活动</Text>
                    </View>
                ) : (
                    list.map((item: any) => (
                        <CheckpointItem key={item.id} {...item} departmentLine={1} onClick={navigateToDetail} />
                    ))
                )}
                {list && list.length > 0 && (
                    <View className={styles.load_status}>
                        {loadState === 'loading' && '加载中...'}
                        {loadState === 'complete' && '已加载全部记录'}
                        {loadState === 'more' && '加载更多'}
                    </View>
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
