@import '@/styles/index.less';
page {
  background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%), linear-gradient(158deg, #CFF7F4 0%, #D1EBFF 12%, #FFF 35%);

  --nav-bar-background-color: transparent;
  --nav-bar-icon-color: #333;
  --nav-bar-arrow-size: 48px;
}
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.content {
  margin-top: 20px;
  flex: 1;
  box-sizing: border-box;
  height: 0;
  margin-bottom: env(safe-area-inset-bottom);
}
.points_empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 80%;
  color: #9597a0;
  font-size: 26px;

  &_icon {
    width: 200px;
    height: 200px;
    margin-bottom: 28px;
  }
}
.load_status {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 16px;
  color: #9597a0;
  font-size: 26px;
}
