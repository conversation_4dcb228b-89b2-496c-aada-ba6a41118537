import { Page } from '@/components';
import { getCheckpointDetail } from '@/services/activity';
import type { LevelStageDetailVO, UnlockCondition } from '@/types/activity';

import { Dialog, NavBar, Toast } from '@antmjs/vantui';
import { Image, Text, View } from '@tarojs/components';
import { navigateBack, navigateTo, useDidShow, useRouter } from '@tarojs/taro';
import classNames from 'classnames';
import Dayjs from 'dayjs';
import { useState } from 'react';
import IconLock from '../../assets/lock.svg';
import IconPass from '../../assets/pass.svg';
import CheckpointItem from '../../components/CheckpointItem';
import styles from './index.less';
const Toast_ = Toast.createOnlyToast();
const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    console.log(useRouter().params);
    const { id } = useRouter().params;
    const [data, setData] = useState<LevelStageDetailVO>();
    console.log(id);
    const goBack = () => {
        navigateBack();
    };
    const goChallenge = (scriptId: string, stageId: string, dItem: any) => {
        if (dItem?.condition?.[0]?.scriptType == 3) {
            navigateTo({
                url: `/pages/practicePPT/detail/index?pptId=${scriptId}&goChallenge=challenge&from=checkpoint&stageId=${stageId}`
            });
        } else {
            navigateTo({
                url: `/pages/chat/sceneExercise/index?goChallenge=challenge&from=checkpoint&scriptId=${scriptId}&stageId=${stageId}`
            });
        }
    };

    const requestDetail = async () => {
        try {
            const res = await getCheckpointDetail(id);
            setData(res.data.data);
        } catch (error) {
            console.log(error);
        }
    };

    const handleDeptmartmentClick = () => {
        if (data?.eventLevel.deptName && data?.eventLevel.deptName.length > 30) {
            Dialog_.alert({
                title: '参与部门',
                message: data?.eventLevel.deptName.split(',').join('、'),
                confirmButtonText: '关闭',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    };

    useDidShow(() => {
        requestDetail();
    });
    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title='闯关活动'
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => goBack()}
            />
            <View className={styles.content}>
                {data && (
                    <CheckpointItem
                        {...data.eventLevel}
                        departmentLine={2}
                        onDepartmentClick={handleDeptmartmentClick}
                    />
                )}
                <View className={styles.card}>
                    {data?.eventLevelStageList.map((item, index) => {
                        const detail: {
                            scriptId: string;
                            scriptName: string;
                            condition: UnlockCondition[];
                            errorMessage: string;
                        }[] = [];
                        // 将unlockCondition根据scriptId重新分组
                        item.unlockCondition.forEach((item) => {
                            const { scriptId, scriptName } = item;
                            const scriptDetail = detail.find((item: any) => item.scriptId === scriptId);
                            if (scriptDetail) {
                                scriptDetail.condition.push(item);
                                if (!scriptDetail.errorMessage) {
                                    scriptDetail.errorMessage = item.errorMessage;
                                }
                            } else {
                                detail.push({
                                    scriptId,
                                    scriptName,
                                    errorMessage: item.errorMessage,
                                    condition: [item]
                                });
                            }
                        });
                        console.log(detail);
                        return (
                            <View className={styles.checkpoint_item} key={item.id}>
                                <View className={styles.step}>
                                    <View
                                        className={classNames(styles.step_point, {
                                            [styles.step_point_active]: item.finishFlag
                                        })}
                                    />
                                    <View
                                        className={classNames(styles.step_line, {
                                            [styles.step_line_active]: item.finishFlag
                                        })}
                                    />
                                </View>
                                <View className={styles.card_content}>
                                    <View className={styles.checkpoint_index}>关卡{index + 1}</View>
                                    {detail.map((dItem) => {
                                        const status = data.eventLevel.progress;
                                        let btn_text = '';
                                        if (status === 1) {
                                            btn_text = '未开始';
                                        } else if (status === 0) {
                                            btn_text = '去挑战';
                                        } else if (status === 2) {
                                            btn_text = '已结束';
                                        }
                                        if (item.finishFlag) {
                                            btn_text = '已通关';
                                        }
                                        return (
                                            <View key={dItem.scriptId} className={styles.checkpoint_task}>
                                                <View className={styles.checkpoint_task_content}>
                                                    <View className={styles.checkpoint_task_script}>
                                                        {dItem.scriptName}
                                                    </View>
                                                    {dItem.condition.map((cItem) => (
                                                        <View key={cItem.id} className={styles.checkpoint_task_list}>
                                                            获得{cItem.time}次{cItem.score}分及以上(
                                                            {cItem.currentTime < cItem.time
                                                                ? cItem.currentTime
                                                                : cItem.time}
                                                            /{cItem.time})
                                                        </View>
                                                    ))}

                                                    <View className={styles.checkpoint_task_list}>
                                                        {item.finishFlag &&
                                                            item.unlockTime &&
                                                            `通关时间：${item.unlockTime}`}
                                                    </View>
                                                    <View className={styles.checkpoint_task_list}>
                                                        {`开启时间：${
                                                            item.openTime
                                                                ? Dayjs(item.openTime).format('YYYY.MM.DD HH:mm')
                                                                : ''
                                                        }`}
                                                    </View>
                                                </View>
                                                <View
                                                    className={classNames(styles.checkpoint_btn, {
                                                        [styles.checkpoint_btn_disabled]: status !== 0,
                                                        [styles.checkpoint_btn_can]:
                                                            status === 0 && !item.finishFlag && !item.lockFlag
                                                    })}
                                                    onClick={() => {
                                                        console.log(dItem, item, 'dkkd');
                                                        if (
                                                            status === 0 &&
                                                            !item.lockFlag &&
                                                            !dItem.errorMessage &&
                                                            !item.finishFlag
                                                        ) {
                                                            if (item.timeLimitFlag) {
                                                                Toast_.show({
                                                                    duration: 1500,
                                                                    message: '该关卡尚未开启',
                                                                    forbidClick: true
                                                                });
                                                                return;
                                                            }
                                                            goChallenge(dItem.scriptId, item.id, dItem);
                                                        } else {
                                                            if (status === 1) {
                                                                Toast_.show({
                                                                    duration: 1500,
                                                                    message: '活动暂未开始',
                                                                    forbidClick: true
                                                                });
                                                                return;
                                                            }
                                                            if (status === 2) {
                                                                Toast_.show({
                                                                    duration: 1500,
                                                                    message: '活动已结束',
                                                                    forbidClick: true
                                                                });
                                                                return;
                                                            }
                                                            if (item.lockFlag) {
                                                                Toast_.show({
                                                                    duration: 1000,
                                                                    message: '请先完成上一关',
                                                                    forbidClick: true
                                                                });
                                                                return;
                                                            }

                                                            if (item.finishFlag) {
                                                                Toast_.show({
                                                                    duration: 1500,
                                                                    message: '已通关',
                                                                    forbidClick: true,
                                                                    onClose: () => {
                                                                        // goChallenge(dItem.scriptId, item.id);
                                                                    }
                                                                });
                                                                return;
                                                            }

                                                            if (dItem.errorMessage) {
                                                                Toast_.show({
                                                                    duration: 1000,
                                                                    message: dItem.errorMessage,
                                                                    forbidClick: true
                                                                });
                                                            }
                                                        }
                                                    }}
                                                >
                                                    {status === 0 && item.finishFlag && (
                                                        <Image src={IconPass} className={styles.checkpoint_btn_icon} />
                                                    )}
                                                    {status === 0 && item.lockFlag && (
                                                        <Image src={IconLock} className={styles.checkpoint_btn_icon} />
                                                    )}
                                                    <Text
                                                        className={classNames(styles.checkpoint_btn_text, {
                                                            [styles.checkpoint_btn_text_passed]:
                                                                status === 0 && (item.finishFlag || item.lockFlag)
                                                        })}
                                                    >
                                                        {btn_text}
                                                    </Text>
                                                </View>
                                            </View>
                                        );
                                    })}
                                </View>
                            </View>
                        );
                    })}
                </View>
            </View>
            <Toast_ />
            <Dialog_ />
        </Page>
    );
};

export default App;
