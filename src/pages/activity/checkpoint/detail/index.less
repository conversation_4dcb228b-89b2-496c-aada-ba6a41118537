@import '@/styles/index.less';
page {
  background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%), linear-gradient(158deg, #CFF7F4 0%, #D1EBFF 12%, #FFF 35%);

  --nav-bar-background-color: transparent;
  --nav-bar-icon-color: #fff;
  --nav-bar-arrow-size: 48px;
  --nav-bar-title-text-color: #fff;
  :global {
    .van-nav-bar::after {
      border-width: 0;
    }
  }

}
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-image: url('../../assets/bg.svg');
  background-repeat: no-repeat;
  background-size: 100% auto;

}

.content {
  flex: 1;
  box-sizing: border-box;
  height: 0;
  margin-top: 20px;
  overflow-y: auto;
  margin-bottom: env(safe-area-inset-bottom);
}
.card {
  background-color: #fff;
  border-radius: 24px;
  margin: 28px 32px 0;
  padding: 32px  32px 32px 22px;
  box-sizing: border-box;
}
.checkpoint_item {
  display: flex;
}
.step {
  width: 42px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  &_point {
    width: 18px;
    height: 18px;
    border-radius: 18px;
    background: #DADADA;
    &_active {
      background: linear-gradient(270deg, #6742FF 0%, #3D83FF 100%);
    }
  }
  &_line {
    width: 2px;
    background-color: #DADADA;
    flex:1;
    margin-top: 4px;
    margin-bottom: 4px;
    &_active {
      background-color: #4F66FF;
    }
  }
}

.card_content {
  flex: 1;
}
.checkpoint {
  &_index {
    font-size: 24px;
    color: #333;
    line-height: 34px;
    margin-top: -6px;
  }
  &_task {
    display: flex;
    align-items: center;
    background-color: rgba(79, 102, 255, 0.04);
    border-radius: 16px;
    padding: 24px;
    box-sizing: border-box;
    margin-top: 16px;
    margin-bottom: 30px;

    &_content {
      flex:1;
    }

    &_script {
      font-size: 28px;
      font-weight: bold;
      line-height: 40px;
    }
    &_list {
      color: #9E9E9E;
      font-size: 24px;
      line-height: 34px;
      margin-top: 12px;
    }
  }
  &_btn {
    width: 156px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 60px;
    margin-left: 34px;
    background-image: url('../../assets/btn_bg.svg');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    font-size: 24px;
    &_icon {
      width: 24px;
      height: 24px;
      margin-right: 14px;
    }
    &_disabled {
      background: #ccc;
    }
    &_text {
      color: #fff;

      &_passed{
        background: linear-gradient(270deg, #6742FF 0%, #3D83FF 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
      }
    }
    &_can {
      background: linear-gradient(270deg, #6742FF 0%, #3D83FF 100%);
    }
  }
}