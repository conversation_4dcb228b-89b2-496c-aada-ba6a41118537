import { Storage, useEvent } from '@/common';
import { Page } from '@/components';
import ScriptActionSheet from '@/components/scriptActionSheet';
import { EChart } from '@/components/taroEcharts';
import config from '@/config';
import { HomePath } from '@/constants/homePath';
import { ScriptType } from '@/constants/scriptType';
import { StorageEnvKey } from '@/constants/storage';
import { useLogin } from '@/hooks';
import {
    generateReport,
    getChatParam,
    getPPTSummaryDetail,
    getReport,
    getReportCheckLogin,
    getReportLogin,
    getresScript
} from '@/services/chat';
import { getShareSetting } from '@/services/user';
import type { PPTSummaryVo, ReportVo, ScriptVO } from '@/types/chat';
import { Col, Dialog, Divider, Icon, Row, Tab, Tabs } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Image, ScrollView, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import Taro, { pxTransform, useLoad, useRouter, useShareAppMessage, useUnload } from '@tarojs/taro';
import { useUpdateEffect } from 'ahooks';
import classNames from 'classNames';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import AudioPlayer from './components/audioFile';
import CommentsQuestion from './components/commentsQuestion';
import CommentsSkill from './components/commentsSkill';
import ComprehensiveScoreDialog from './components/ComprehensiveScoreDialog';
import ImgSwiper from './components/imgSwiper';
import ScrollableTab from './components/Tab';
import styles from './index.less';
const ReportAuth = `${config.cdnPrefix}Fluency/report_auth.svg`;
const ReportFail = `${config.cdnPrefix}report/report_fail.png`;
const score1 = `${config.cdnPrefix}report/score_1.png`;
const score2 = `${config.cdnPrefix}report/score_2.png`;
const score3 = `${config.cdnPrefix}report/score_3.png`;
const score4 = `${config.cdnPrefix}report/score_4.png`;
const score5 = `${config.cdnPrefix}report/score_5.png`;
const score6 = `${config.cdnPrefix}report/score_6.png`;
const score7 = `${config.cdnPrefix}report/score_7.png`;
const score8 = `${config.cdnPrefix}report/score_8.png`;
const soundNo = `${config.cdnPrefix}Fluency/sound_No.png`;
const ReportImg = `${config.cdnPrefix}report/report.jpg`;
const IconComplete = `${config.cdnPrefix}slide/fail_icon.png`;
const raderPng = `${config.cdnPrefix}Fluency/rader.png`;
const FluencyPng = `${config.cdnPrefix}Fluency/Fluency.png`;
const allContextPng = `${config.cdnPrefix}Fluency/allContext.png`;
const speackIconSvg = `${config.cdnPrefix}report/speak_icon.svg`;

const App = () => {
    const { params } = useRouter<{
        id: string;
        list_status?: string;
        form?: string;
        to?: string;
        PPTid?: any;
    }>();
    const { id, list_status, form, to } = params; // 报告ID
    console.log('params', params);
    const [loadingFlag, setLoadingFlag] = useState(false); // 是否正在请求中
    const [carouselScript, setcarouselScript] = useState([]); // 是否正在请求中
    const [showScript, setShowScript] = useState(false);
    const getReportTimer = useRef<any>();
    const chartId = useRef<string>('');
    const [voiceRateDimensions, setvoiceRateDimensions] = useState<any>();
    const voiceWeight = useMemo(() => {
        if (voiceRateDimensions) {
            return voiceRateDimensions.voiceWeight;
        }
        return 0;
    }, [voiceRateDimensions]);
    const [skillWeight, setSkillWeight] = useState<any>();
    const [contentIntegrityWeight, setContentIntegrityWeight] = useState<any>();
    const Dialog_ = Dialog.createOnlyDialog();
    const report = {
        id: '',
        status: 1,
        createTime: '',
        updateTime: '',
        score: 0,
        sumup: '',
        chatId: '',
        dimension: '',
        emanName: '',
        scene: '',
        dimensionArray: [],
        chatStartTime: '',
        chatEndTime: '',
        scriptDetailVO: {} as ScriptVO
    };
    const [reportDetail, setReportDetail] = useState<ReportVo>(report);
    const [reportResult, setReportResult] = useState<boolean>(true); // 请求返回结果
    const [chatDuring, setChatDuring] = useState<string>(''); // 聊天时长
    const [summary, setSummary] = useState<PPTSummaryVo>(); // 演练记录
    /**
     * 判断ppt列表中，是否存在我的讲解，如果不存在，展示 pptSummaryDetail 接口中的 讲解数据
     */
    const summaryAnswer = useMemo(() => {
        const isExistAnswerInPPTList = reportDetail?.scriptDetailVO?.ppt?.some(
            (item: any) => typeof item.score === 'number'
        );
        return isExistAnswerInPPTList ? '' : summary?.answer;
    }, [reportDetail, summary]);
    const comprehensiveScoreDescription = useMemo(() => {
        const dimension = JSON.parse(reportDetail?.dimension || '[]');
        // console.log('dimension', dimension);

        if (Array.isArray(dimension) && voiceWeight) {
            // 声音权重存在 && dimension 是一个数组，说明这个一个老数据 内容权重 = 100 - 声音权重展示
            return `内容完整性得分*${100 - voiceWeight}%+声音流畅度得分*${voiceWeight}%`;
        } else if (
            !Array.isArray(dimension) &&
            dimension.skillWeight !== undefined &&
            dimension.contentIntegrityWeight !== undefined
        ) {
            // 新标准：讲解技巧权重 内容完整性权重、 声音流畅性 可能不存在
            const descriptionList = [];
            if (skillWeight) {
                descriptionList.push(`讲解技巧得分*${skillWeight}%`);
            }
            if (contentIntegrityWeight) {
                descriptionList.push(`内容完整性得分*${contentIntegrityWeight}%`);
            }
            if (voiceWeight) {
                descriptionList.push(`声音流畅度得分*${voiceWeight}%`);
            }
            // console.log('description', skillWeight, contentIntegrityWeight, voiceWeight, descriptionList.join('+'));

            return descriptionList.join('+');
        } else {
            return '';
        }
    }, [voiceWeight, skillWeight, contentIntegrityWeight, reportDetail]);
    const backStatus = useRef(0);
    const [canShare, setCanShare] = useState(false);
    const [reportResultPpt, setReportResultPpt] = useState<boolean>(true); // 请求返回结果
    const [scrollIntoView, setScrollIntoView] = useState('');
    const getScoreImg = () => {
        const { score } = reportDetail! || 0;
        if (score < 30) {
            return score8;
        }
        switch (Math.floor(score / 10)) {
            case 3:
                return score7;
            case 4:
                return score6;
            case 5:
                return score5;
            case 6:
                return score4;
            case 7:
                return score3;
            case 8:
                return score2;
            case 9:
                return score1;
            case 10:
                return score1;
        }
    };
    // const [visiblebubble, setVisiblebubble] = useState(false);
    // const [positionbubble, setPositionbubble] = useState('top');

    const needLogin = useRef(false);

    const chartRef = useRef<any>();
    const chartRefRadar = useRef<any>();

    const chartRefRadarSon = useRef<any>();
    const chartRefAudioSpeed = useRef<any>(); // 语速
    const chartRefAudioVolume = useRef<any>(); // 音量
    const chartRefAudioRedundancy = useRef<any>(); // 冗余词

    const centerArr = ['50%', '70%'];
    const option = useRef({
        grid: {
            bottom: 10
        },
        series: [
            {
                name: '灰色刻度尺',
                type: 'gauge',
                z: 4,
                radius: '90%',
                splitNumber: 1,
                startAngle: 180,
                endAngle: 0,
                center: centerArr, // 整体的位置设置
                min: 0,
                max: 100,
                animationDuration: 300,
                data: [{ value: reportDetail?.score }],
                title: {
                    offsetCenter: [0, -35],
                    fontSize: 14,
                    color: '#9597A0'
                },
                detail: {
                    fontSize: 38,
                    offsetCenter: [0, -20],
                    valueAnimation: true,
                    color: '#272C47',
                    fontWeight: 200
                },
                axisLine: {
                    roundCap: true,
                    lineStyle: {
                        width: 9,
                        color: [
                            [0, '#F5F8FF'],
                            [1, '#F5F8FF']
                        ]
                    }
                },
                progress: {
                    show: false,
                    roundCap: true,
                    width: 9
                },
                itemStyle: {
                    color: '#4C82FB'
                },
                axisLabel: {
                    show: false
                },
                pointer: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: false
                }
            },
            {
                type: 'pie',
                z: 5,
                color: ['transparent', 'transparent', 'transparent'],
                animationEasingUpdate: 'cubicOut',
                animationDuration: 300,
                startAngle: 180,
                silent: 'ture',
                center: centerArr,
                radius: ['98%', '100%'],
                hoverAnimation: false,
                labelLine: {
                    show: false
                },
                title: {
                    // text: '综合得分',
                    textStyle: {
                        fontSize: 38,
                        color: '#272C47',
                        fontWeight: 600
                    }
                },
                data: [
                    {
                        name: '',
                        value: reportDetail?.score,
                        itemStyle: {
                            borderColor: '#4C82FB',
                            borderWidth: 12
                        }
                    },
                    {
                        // 画中间的图标
                        name: '',
                        value: 0,
                        label: {
                            rotate: 90,
                            position: 'inside',
                            width: 12,
                            height: 12,
                            verticalAlign: 'bottom',
                            backgroundColor: '#5EA1FC',
                            borderRadius: 12,
                            borderWidth: 5,
                            borderColor: '#ffffff',
                            shadowColor: 'rgba(0,0,0,0.1)',
                            shadowBlur: 4,
                            shadowOffsetY: 2
                        }
                    },
                    {
                        // 画剩余的刻度圆环
                        name: '',
                        value: 100 - reportDetail?.score,
                        itemStyle: {
                            borderColor: '#E7E9FE',
                            borderWidth: 12
                        }
                    },
                    {
                        // 画剩余的刻度圆环
                        name: '',
                        value: 100
                    }
                ]
            }
        ]
    });
    const optionAudioSpeed = useRef({
        xAxis: {
            type: 'category',
            show: false,
            boundaryGap: false,
            data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'] // 示例数据
        },
        yAxis: {
            type: 'value',
            show: false,
            splitLine: {
                show: false
            },
            min: 0, // 设置Y轴的最小值为0
            max: 400 //
        },
        grid: {
            top: 40,
            bottom: 20,
            left: 0,
            right: '10%',
            backgroundColor: '#F6F7FB',
            containLabel: true
        },
        series: [
            {
                data: [130, 150, 170, 160, 140, 130, 150, 180, 190, 220], // 示例数据
                type: 'line',
                smooth: true,
                showSymbol: false,

                lineStyle: {
                    color: '#4F66FF', // 线条颜色
                    width: 1, // 线条宽度
                    type: 'dashed', // 虚线类型
                    dashOffset: '0' // 虚线偏移量，可选
                },
                markArea: {
                    silent: true,
                    label: {
                        position: ['95%', '50%'],
                        color: '#9597A0'
                    },
                    data: [
                        [
                            {
                                name: '慢',
                                yAxis: 0,
                                x: '0',
                                itemStyle: {
                                    color: '#F6F7FB'
                                }
                            },
                            { x: '100%', yAxis: 139 }
                        ],
                        [
                            {
                                name: '中',
                                yAxis: 179,
                                x: '0',
                                itemStyle: {
                                    color: '#EFF0F7'
                                }
                            },
                            { x: '100%', yAxis: 139 }
                        ],
                        [
                            {
                                name: '快',
                                yAxis: 179,
                                x: '0',
                                itemStyle: {
                                    color: '#F6F7FB'
                                }
                            },
                            { x: '100%', yAxis: 500 }
                        ]
                    ]
                }
            }
        ],
        visualMap: {
            show: false,
            pieces: [
                { gt: 0, lte: 139, color: '#4F66FF' },
                { gt: 139, lte: 179, color: '#4F66FF' },
                { gt: 179, color: '#4F66FF' }
            ],
            outOfRange: {
                color: '#4F66FF'
            }
        }
    });
    const optionAudioVolume = useRef({
        grid: {
            top: 40,
            bottom: 20,
            left: 10,
            right: '10%',
            backgroundColor: '#F6F7FB',
            containLabel: true
        },

        xAxis: {
            // 改为分类轴
            type: 'category',
            show: false,
            boundaryGap: false,
            data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '14', '15', '17', '17'] // 示例数据
        },
        yAxis: {
            // 改为数值轴
            type: 'value',
            show: false,
            splitLine: {
                show: false
            },
            min: 0, // 设置Y轴的最小值为0
            max: 400 //
        },
        series: [
            {
                type: 'pictorialBar',
                itemStyle: {
                    color: '#F6F7FB'
                },
                symbolRepeat: true,
                symbolMargin: 3,
                symbol: 'rect',
                symbolSize: [24, 5], // 调整装饰条方向
                data: [130, 150, 170, 160, 140, 130, 150, 180, 190, 220, 130, 150, 170, 160, 140, 140, 140],
                z: 3
            },
            {
                type: 'bar',
                barWidth: 24,
                itemStyle: {
                    color: '#4F66FF'
                },
                data: [130, 150, 170, 160, 140, 130, 150, 180, 190, 220, 130, 150, 170, 160, 140, 140, 140],
                markArea: {
                    silent: true,
                    label: {
                        position: ['95%', '50%'],
                        color: '#9597A0'
                    },

                    data: [
                        [
                            {
                                name: '慢',
                                yAxis: 0,
                                x: '0',
                                itemStyle: {
                                    color: '#F6F7FB'
                                }
                            },
                            {
                                x: '100%',
                                yAxis: 139
                            }
                        ],
                        [
                            {
                                name: '中',
                                yAxis: 179,
                                x: '0',
                                itemStyle: {
                                    borderColor: '#989898', // 边框颜色
                                    borderWidth: 1, // 边框宽度
                                    borderType: 'dashed',

                                    color: '#E2E8FF'
                                }
                            },
                            {
                                x: '100%',
                                yAxis: 139
                            }
                        ],
                        [
                            {
                                name: '快',
                                x: '0',
                                yAxis: 179,
                                itemStyle: {
                                    color: '#F6F7FB'
                                }
                            },
                            { x: '100%', yAxis: 500 }
                        ]
                    ]
                }
            }
        ]
    });
    const optionAudioRedundancy = useRef({
        backgroundColor: '#F6F7FB',
        grid: {
            top: 40,
            bottom: 10,
            left: 10,
            right: '10%',
            backgroundColor: '#F6F7FB',
            containLabel: true
        },
        xAxis: {
            axisTick: {
                show: false // 隐藏刻度条
            },
            type: 'category',
            data: ['嗯', '啊', '哦哦', '那个', '然后', '这个', '唉'],
            axisLabel: {
                show: true,
                textStyle: {
                    fontSize: 14 // 设置字体大小为14px
                }
            }
        },
        yAxis: {
            show: false,
            splitLine: {
                show: false
            },

            type: 'value'
        },
        series: [
            {
                data: [120, 200, 150, 80, 70, 110, 130],
                type: 'bar',
                barWidth: 24,
                itemStyle: {
                    color: '#4F66FF'
                },
                label: {
                    normal: {
                        show: true,
                        position: 'top',
                        formatter: (e) => {
                            return `${e.value}次`;
                        },
                        fontSize: '12px',
                        color: '#9597A0',
                        fontFamily: 'siyuan',
                        fontWeight: 'bold',
                        offset: [0, -5]
                    }
                },
                markArea: {
                    silent: true,
                    label: {
                        position: ['95%', '50%'],
                        color: '#9597A0'
                    },

                    data: [
                        [
                            {
                                x: '0',
                                yAxis: 10000000009,
                                itemStyle: {
                                    color: '#F6F7FB'
                                }
                            },
                            { x: '100%', yAxis: 0 }
                        ]
                    ]
                }
            }
        ]
    });
    const optionRadar = useRef({
        radar: [
            {
                indicator: [
                    { text: '开场白', max: 100 },
                    { text: '探寻需求', max: 100 },
                    { text: '产品呈现', max: 100 },
                    { text: '异常处理', max: 100 },
                    { text: '缔结', max: 100 }
                ],
                radius: 61,
                center: ['53%', '50%']
            }
        ],
        series: [
            {
                type: 'radar',
                lineStyle: {
                    color: '#4F66FF',
                    width: 2
                },
                tooltip: {
                    trigger: 'item'
                },
                color: '#4F66FF',
                areaStyle: {},
                data: [
                    {
                        // label: {
                        //     normal: {
                        //         show: true,
                        //         color: '#4F66FF'
                        //     }
                        // },
                        value: [0, 0, 0, 0, 0],
                        name: 'A Software'
                    }
                ]
            }
        ]
    });
    const optionRadarSon = useRef({
        radar: [
            {
                indicator: [
                    { text: '开场白', max: 100 },
                    { text: '探寻需求', max: 100 },
                    { text: '产品呈现', max: 100 },
                    { text: '异常处理', max: 100 },
                    { text: '缔结', max: 100 }
                ],
                radius: 61,
                center: ['53%', '50%']
            }
        ],
        series: [
            {
                type: 'radar',
                lineStyle: {
                    color: '#4F66FF',
                    width: 2
                },
                tooltip: {
                    trigger: 'item'
                },
                color: '#4F66FF',
                areaStyle: {},
                data: [
                    {
                        // label: {
                        //     normal: {
                        //         show: true,
                        //         color: '#4F66FF'
                        //     }
                        // },
                        value: [0, 0, 0, 0, 0],
                        name: 'A Software'
                    }
                ]
            }
        ]
    });
    useUpdateEffect(() => {
        if (reportDetail.scriptDetailVO.type == 3) {
            getPPTSummaryDetail(reportDetail.chatId || '').then((listRes: any) => {
                setSummary(listRes.data.data);
            });
        }
    }, [reportDetail]);

    const [waiteButton, setWaiteButton] = useState(true);
    const [isRadarShow, setisRadarShow] = useState<any>(undefined);
    // useEffect(async()=>{
    //     const reportRes = await getReport(id);
    // },[])
    function convertScore(score: any, all: any) {
        if (score && all) {
            // 计算比例
            const ratio = score / all;
            const newratio = ratio * 5;
            // 换算成5分制
            const score5 = newratio.toFixed(1);
            return score5;
        }
    }

    const { login } = useLogin({
        onSuccess: (userInfo) => {
            console.log('login success');
            initData(true);
            Taro.showToast({
                icon: 'none',
                title: '登录成功'
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });

    const _getReport = useEvent(async () => {
        // let reportRes;
        // if (form === 'share') {
        //     reportRes = await getReportShare(id);
        // } else {
        //     reportRes = await getReport(id);
        // }
        // 获取报告详情
        // 请求错误

        try {
            const reportRes = needLogin.current ? await getReportLogin(id) : await getReport(id);
            // status:1和4，报告未生成||报告重新生成中，10s请求一次
            Taro.hideLoading();
            const { data } = reportRes.data;
            setisRadarShow(data.scriptDetailVO.type);
            const voiceRat = JSON.parse(data?.voiceRateDimension);
            setvoiceRateDimensions(voiceRat);
            backStatus.current = data.status;
            chartId.current = data.chatId;
            // if (data.status === 1 || data.status === 4) {
            //     const scriptType = data.scriptDetailVO.type;
            //     if (scriptType === 3) {
            //         Taro.redirectTo({ url: `/pages/practicePPT/historyPPT/index?chatId=${data.chatId}` });
            //     } else {
            //         Taro.redirectTo({ url: `/pages/chat/history/index?chatId=${data.chatId}` });
            //     }
            // } else
            if (data.status === 1 || data.status === 4) {
                setReportDetail(data);
                if (!getReportTimer.current) {
                    getReportTimer.current = setInterval(() => {
                        _getReport();
                    }, 10 * 1000);
                }
            } else if (data.status === 2) {
                // 生成成功
                if (getReportTimer.current) {
                    clearInterval(getReportTimer.current);
                }
                setLoadingFlag(false);
                setReportResult(true);
                Taro.nextTick(() => {
                    const dimension = JSON.parse(data?.dimension || '[]');
                    const dimensionArray = Array.isArray(dimension) ? dimension : dimension?.dimension;
                    if (dimension?.dimension) {
                        setSkillWeight(dimension?.skillWeight);
                        setContentIntegrityWeight(dimension?.contentIntegrityWeight);
                    }
                    let radarValueData = [];
                    if (data.scriptDetailVO.type === 1) {
                        // 按照5分制显示处理逻辑
                        radarValueData = dimensionArray?.map((item: any) => {
                            const fiveScore = convertScore(item.score, item.fullScore);
                            return fiveScore;
                        });

                        if (voiceRat) {
                            radarValueData.push(convertScore(data?.voiceRateScore, 100));
                        }
                    }

                    if (data.scriptDetailVO.type === 3) {
                        // 按照5分制显示处理逻辑
                        radarValueData = dimensionArray?.map((item: any) => {
                            const fiveScore = convertScore(item.score, item.fullScore);
                            return fiveScore;
                        });

                        if (typeof data?.integrityScore === 'number') {
                            radarValueData.push(convertScore(data?.integrityScore, 100));
                        }

                        if (voiceRat) {
                            radarValueData.push(convertScore(data?.voiceRateScore, 100));
                        }
                    }

                    const voiceRateDimension = JSON.parse(data?.voiceRateDimension);
                    // 子维度雷达图
                    const randerSon: any = [];
                    if (data.scriptDetailVO.type == 1 || data.scriptDetailVO.type == 3) {
                        dimensionArray.forEach((item: any) => {
                            randerSon.push(...item.children);
                        });
                    }

                    const randerSonEchartArr = randerSon?.map((item: any) => {
                        return {
                            text: item.name,
                            max: 5, // 根据你的数据范围调整,\
                            color: '#4F66FF'
                        };
                    });
                    const randerSonEchartArrValue = randerSon?.map((item: any) => {
                        const isfiveScore = convertScore(item.score, item.fullScore);
                        return isfiveScore;
                    });
                    if (voiceRat) {
                        randerSonEchartArr.push({ text: '语速', max: 5, color: '#4F66FF' });
                        randerSonEchartArr.push({ text: '音量', max: 5, color: '#4F66FF' });
                        randerSonEchartArr.push({ text: '冗余词', max: 5, color: '#4F66FF' });
                    }
                    if (voiceRat) {
                        const isyusu = convertScore(voiceRat.rateVoice.score, voiceRat.rateVoice.totalScore);
                        const isyinliang = convertScore(voiceRat.volumeVoice.score, voiceRat.volumeVoice.totalScore);
                        const isrongyuci = convertScore(
                            voiceRat.redundantWordVoice.score,
                            voiceRat.redundantWordVoice.totalScore
                        );
                        randerSonEchartArrValue.push(isyusu ?? 0);
                        randerSonEchartArrValue.push(isyinliang ?? 0);
                        randerSonEchartArrValue.push(isrongyuci ?? 0);
                    }

                    // 雷达图主维度处理
                    let radarTitleData = [];

                    if (data.scriptDetailVO.type === 1) {
                        radarTitleData = dimensionArray?.map((item: any) => {
                            const newsttr = item.name.substring(0, 3);
                            const fiveScore = convertScore(item.score, item.fullScore);
                            const newText =
                                item.name.length < 5
                                    ? `${item.name} ${fiveScore ?? 0}分`
                                    : `${newsttr}... ${fiveScore ?? 0}分`;
                            return {
                                name: newText,
                                max: 5, // 根据你的数据范围调整，
                                color: '#4F66FF'
                            };
                        });
                        if (voiceRat) {
                            const isyusu = convertScore(data?.voiceRateScore, 100);

                            radarTitleData.push({
                                name: `流畅度 ${isyusu ?? 0}分`,
                                max: 5, // 根据你的数据范围调整，
                                color: '#4F66FF'
                            });
                        }
                    }
                    if (data.scriptDetailVO.type === 3) {
                        radarTitleData = dimensionArray?.map((item: any) => {
                            const newsttr = item.name.substring(0, 3);
                            // const fiveScore = convertScore(item.score, item.fullScore);
                            const newText = item.name.length < 5 ? `${item.name}` : `${newsttr}...`;
                            return {
                                name: newText,
                                max: 5, // 根据你的数据范围调整，
                                color: '#4F66FF'
                            };
                        });

                        radarTitleData.push({
                            name: '内容完整性',
                            max: 5, // 根据你的数据范围调整，
                            color: '#4F66FF'
                        });

                        if (voiceRat) {
                            radarTitleData.push({
                                name: '声音流畅度',
                                max: 5, // 根据你的数据范围调整，
                                color: '#4F66FF'
                            });
                        }
                    }
                    console.log('radarTitleData', radarTitleData);

                    // 时间
                    const chatDuring = dayjs(
                        Number(dayjs(data.chatEndTime)) - Number(dayjs(data.chatStartTime))
                    ).format('mm分ss秒');
                    setChatDuring(chatDuring);
                    const reportDetail = { ...data, dimensionArray };
                    setReportDetail(() => {
                        return reportDetail;
                    });

                    option.current = {
                        grid: {
                            bottom: 10
                        },
                        series: [
                            {
                                name: '灰色刻度尺',
                                type: 'gauge',
                                z: 4,
                                radius: '90%',
                                splitNumber: 1,
                                startAngle: 180,
                                endAngle: 0,
                                center: centerArr, // 整体的位置设置
                                min: 0,
                                max: 100,
                                animationDuration: 300,
                                data: [{ value: reportDetail?.score }],
                                title: {
                                    offsetCenter: [0, -35],
                                    fontSize: 14,
                                    color: '#9597A0'
                                },
                                detail: {
                                    fontSize: 38,
                                    offsetCenter: [0, -20],
                                    valueAnimation: true,
                                    color: '#272C47',
                                    fontWeight: 200
                                },
                                axisLine: {
                                    roundCap: true,
                                    lineStyle: {
                                        width: 9,
                                        color: [
                                            [0, '#F5F8FF'],
                                            [1, '#F5F8FF']
                                        ]
                                    }
                                },
                                progress: {
                                    show: false,
                                    roundCap: true,
                                    width: 9
                                },
                                itemStyle: {
                                    color: '#4C82FB'
                                },
                                axisLabel: {
                                    show: false
                                },
                                pointer: {
                                    show: false
                                },
                                axisTick: {
                                    show: false
                                },
                                splitLine: {
                                    show: false
                                }
                            },
                            {
                                type: 'pie',
                                z: 5,
                                color: ['transparent', 'transparent', 'transparent'],
                                animationEasingUpdate: 'cubicOut',
                                animationDuration: 300,
                                startAngle: 180,
                                center: centerArr,
                                radius: ['98%', '100%'],
                                hoverAnimation: false,
                                silent: 'ture',
                                labelLine: {
                                    show: false
                                },
                                title: {
                                    // text: '综合得分',
                                    textStyle: {
                                        fontSize: 38,
                                        color: '#272C47',
                                        fontWeight: 600
                                    }
                                },
                                data: [
                                    {
                                        name: '',
                                        value: reportDetail?.score,
                                        itemStyle: {
                                            borderColor: '#4C82FB',
                                            borderWidth: 12
                                        }
                                    },
                                    {
                                        // 画中间的图标
                                        name: '',
                                        value: 0,
                                        label: {
                                            rotate: 90,
                                            position: 'inside',
                                            width: 12,
                                            height: 12,
                                            verticalAlign: 'bottom',
                                            backgroundColor: '#5EA1FC',
                                            borderRadius: 12,
                                            borderWidth: 5,
                                            borderColor: '#ffffff',
                                            shadowColor: 'rgba(0,0,0,0.1)',
                                            shadowBlur: 4,
                                            shadowOffsetY: 2
                                        }
                                    },
                                    {
                                        // 画剩余的刻度圆环
                                        name: '',
                                        value: 100 - reportDetail?.score,
                                        itemStyle: {
                                            borderColor: '#E7E9FE',
                                            borderWidth: 12
                                        }
                                    },
                                    {
                                        // 画剩余的刻度圆环
                                        name: '',
                                        value: 100
                                    }
                                ]
                            }
                        ]
                    };
                    console.log(
                        randerSonEchartArr,
                        randerSonEchartArrValue,
                        'randerSonEchartArrValueranderSonEchartArrValue'
                    );
                    optionRadar.current = {
                        radar: [
                            {
                                indicator: radarTitleData || [],
                                // indicator: [
                                //     { name: '啦啦啦... 4.0分', max: 6500 },
                                //     { name: '啦啦啦... 4.0分', max: 16000 },
                                //     { name: '啦啦啦... 4.0分', max: 30000 },
                                //     { name: '啦啦啦... 4.0分', max: 38000 },
                                //     { name: '啦啦啦... 4.0分', max: 52000 },
                                //     { name: '啦啦啦... 4.0分', max: 25000 }
                                // ],
                                radius: radarTitleData?.length > 5 ? 50 : 60,
                                center: ['53%', '50%']
                            }
                        ],
                        series: [
                            {
                                type: 'radar',
                                lineStyle: {
                                    color: '#4F66FF',
                                    width: 2
                                },
                                tooltip: {
                                    trigger: 'item'
                                },
                                areaStyle: {},
                                color: 'rgba(157, 170, 255, 0.5)',
                                data: [
                                    {
                                        // label: {
                                        //     normal: {
                                        //         show: true,
                                        //         color: '#4F66FF'
                                        //     }
                                        // },
                                        value: radarValueData || [],
                                        // value: ['5', '5', '5', '0.0', '0.0'],
                                        name: 'A Software'
                                    }
                                ]
                            }
                        ]
                    };
                    optionRadarSon.current = {
                        radar: [
                            {
                                indicator: randerSonEchartArr || [],
                                // indicator: [
                                //     { name: '啦啦啦... 4.0分', max: 6500 },
                                //     { name: '啦啦啦... 4.0分', max: 16000 },
                                //     { name: '啦啦啦... 4.0分', max: 30000 },
                                //     { name: '啦啦啦... 4.0分', max: 38000 },
                                //     { name: '啦啦啦... 4.0分', max: 52000 },
                                //     { name: '啦啦啦... 4.0分', max: 25000 }
                                // ],
                                radius: radarTitleData?.length > 5 ? 50 : 60,
                                center: ['53%', '50%']
                            }
                        ],
                        series: [
                            {
                                type: 'radar',
                                lineStyle: {
                                    color: '#4F66FF',
                                    width: 2
                                },
                                tooltip: {
                                    trigger: 'item'
                                },
                                areaStyle: {},
                                color: 'rgba(157, 170, 255, 0.5)',
                                data: [
                                    {
                                        // label: {
                                        //     normal: {
                                        //         show: true,
                                        //         color: '#4F66FF'
                                        //     }
                                        // },
                                        // value: radarValueData,
                                        value: randerSonEchartArrValue || [],
                                        name: 'A Software'
                                    }
                                ]
                            }
                        ]
                    };
                    const max =
                        voiceRateDimension?.rateVoice?.averageSpeakSpeed > voiceRateDimension?.rateVoice?.max
                            ? voiceRateDimension?.rateVoice?.averageSpeakSpeed * 1.05
                            : voiceRateDimension?.rateVoice?.max * 1.5;
                    optionAudioSpeed.current = {
                        xAxis: {
                            type: 'category',
                            show: false,
                            boundaryGap: false,
                            data: ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'] // 示例数据
                        },
                        yAxis: {
                            type: 'value',
                            show: false,
                            splitLine: {
                                show: false
                            },
                            min: 0, // 设置Y轴的最小值为0
                            max //
                        },
                        grid: {
                            top: 40,
                            bottom: 20,
                            left: 0,
                            right: '10%',
                            backgroundColor: '#F6F7FB',
                            containLabel: true
                        },
                        series: [
                            {
                                data: new Array(10).fill(voiceRateDimension?.rateVoice?.averageSpeakSpeed), // 示例数据
                                type: 'line',
                                smooth: true,
                                showSymbol: false,

                                lineStyle: {
                                    color: '#4F66FF', // 线条颜色
                                    width: 1, // 线条宽度
                                    type: 'dashed', // 虚线类型
                                    dashOffset: '0' // 虚线偏移量，可选
                                },
                                markArea: {
                                    silent: true,
                                    label: {
                                        position: ['95%', '50%'],
                                        color: '#9597A0'
                                    },
                                    data: [
                                        [
                                            {
                                                name: '慢',
                                                yAxis: 0,
                                                x: '0',
                                                itemStyle: {
                                                    color: '#F6F7FB'
                                                }
                                            },
                                            { x: '100%', yAxis: voiceRateDimension?.rateVoice?.min }
                                        ],
                                        [
                                            {
                                                name: '中',
                                                yAxis: voiceRateDimension?.rateVoice?.max,
                                                x: '0',
                                                itemStyle: {
                                                    color: '#EFF0F7'
                                                }
                                            },
                                            { x: '100%', yAxis: voiceRateDimension?.rateVoice?.min }
                                        ],
                                        [
                                            {
                                                name: '快',
                                                yAxis: voiceRateDimension?.rateVoice?.max,
                                                x: '0',
                                                itemStyle: {
                                                    color: '#F6F7FB'
                                                }
                                            },
                                            { x: '100%', yAxis: max }
                                        ]
                                    ]
                                }
                            }
                        ],
                        visualMap: {
                            show: false,
                            pieces: [
                                { gt: 0, lte: 139, color: '#4F66FF' },
                                { gt: 139, lte: 179, color: '#4F66FF' },
                                { gt: 179, color: '#4F66FF' }
                            ],
                            outOfRange: {
                                color: '#4F66FF'
                            }
                        }
                    };
                    const maxVolume =
                        voiceRateDimension?.volumeVoice?.averageVolumeDB > voiceRateDimension?.volumeVoice?.max
                            ? voiceRateDimension?.volumeVoice?.averageVolumeDB * 1.5
                            : voiceRateDimension?.volumeVoice?.max * 1.5;
                    console.log(maxVolume, 'maxVolumemaxVolumemaxVolume');
                    optionAudioVolume.current = {
                        grid: {
                            top: 40,
                            bottom: 20,
                            left: 10,
                            right: '10%',
                            backgroundColor: '#F6F7FB',
                            containLabel: true
                        },

                        xAxis: {
                            // 改为分类轴
                            type: 'category',
                            show: false,
                            boundaryGap: false,
                            data: voiceRateDimension?.volumeVoice?.averageVolumeDB // 示例数据
                        },
                        yAxis: {
                            // 改为数值轴
                            type: 'value',
                            show: false,
                            splitLine: {
                                show: false
                            },
                            min: 0, // 设置Y轴的最小值为0
                            max: maxVolume //
                        },
                        series: [
                            {
                                type: 'pictorialBar',
                                itemStyle: {
                                    color: '#4F66FF'
                                },
                                symbolRepeat: true,
                                symbolMargin: 3,
                                symbol: 'rect',
                                symbolSize: [15, 5], // 调整装饰条方向
                                data: voiceRateDimension?.volumeVoice?.dbList,
                                z: 3
                            },
                            {
                                type: 'bar',
                                barWidth: 15,
                                itemStyle: {
                                    color: '#F6F7FB'
                                },
                                data: voiceRateDimension?.volumeVoice?.dbList,
                                markArea: {
                                    silent: true,
                                    label: {
                                        position: ['95%', '50%'],
                                        color: '#9597A0'
                                    },

                                    data: [
                                        [
                                            {
                                                name: '低',
                                                yAxis: 0,
                                                x: '0',
                                                itemStyle: {
                                                    color: '#F6F7FB'
                                                }
                                            },
                                            {
                                                x: '100%',
                                                yAxis: voiceRateDimension?.volumeVoice?.min
                                            }
                                        ],
                                        [
                                            {
                                                name: '中',
                                                yAxis: voiceRateDimension?.volumeVoice?.min,
                                                x: '0',
                                                itemStyle: {
                                                    borderColor: '#989898', // 边框颜色
                                                    borderWidth: 1, // 边框宽度
                                                    borderType: 'dashed',

                                                    color: '#E2E8FF'
                                                }
                                            },
                                            {
                                                x: '100%',
                                                yAxis: voiceRateDimension?.volumeVoice?.max
                                            }
                                        ],
                                        [
                                            {
                                                name: '高',
                                                x: '0',
                                                yAxis: voiceRateDimension?.volumeVoice?.max,
                                                itemStyle: {
                                                    color: '#F6F7FB'
                                                }
                                            },
                                            { x: '100%', yAxis: maxVolume }
                                        ]
                                    ]
                                }
                            }
                        ]
                    };

                    let xdata: any;
                    let ydata: any;
                    if (voiceRateDimension?.redundantWordVoice?.redundantWordCountMap) {
                        const sortedObj = Object.fromEntries(
                            Object.entries(voiceRateDimension?.redundantWordVoice?.redundantWordCountMap).sort(
                                ([, a]: any, [, b]: any) => b - a
                            )
                        );
                        xdata = Object.keys(sortedObj) || [];
                        ydata = Object.values(sortedObj) || [];
                    } else {
                        xdata = [];
                        ydata = [];
                    }

                    optionAudioRedundancy.current = {
                        backgroundColor: '#F6F7FB',
                        grid: {
                            top: 40,
                            bottom: 10,
                            left: 10,
                            right: '10%',
                            backgroundColor: '#F6F7FB',
                            containLabel: true
                        },
                        xAxis: {
                            axisTick: {
                                show: false // 隐藏刻度条
                            },
                            type: 'category',
                            data: xdata,
                            axisLabel: {
                                show: true,
                                textStyle: {
                                    fontSize: 14 // 设置字体大小为14px
                                }
                            }
                        },
                        yAxis: {
                            show: false,
                            splitLine: {
                                show: false
                            },

                            type: 'value'
                        },
                        series: [
                            {
                                data: ydata,
                                type: 'bar',
                                barWidth: 24,
                                itemStyle: {
                                    color: '#4F66FF'
                                },
                                label: {
                                    normal: {
                                        show: true,
                                        position: 'top',
                                        formatter: (e) => {
                                            return `${e.value}次`;
                                        },
                                        fontSize: '14px',
                                        color: '#9597A0',
                                        fontFamily: 'siyuan',
                                        fontWeight: 'bold',
                                        offset: [0, -5]
                                    }
                                },
                                markArea: {
                                    silent: true,
                                    label: {
                                        position: ['95%', '50%'],
                                        color: '#9597A0'
                                    },

                                    data: [
                                        [
                                            {
                                                x: '0',
                                                yAxis: 10000000009,
                                                itemStyle: {
                                                    color: '#F6F7FB'
                                                }
                                            },
                                            { x: '100%', yAxis: 0 }
                                        ]
                                    ]
                                }
                            }
                        ]
                    };
                    chartRefRadar.current?.refresh(optionRadar.current);
                    chartRef.current?.refresh(option.current);
                    chartRefRadarSon.current?.refresh(optionRadarSon.current);
                    chartRefAudioVolume.current?.refresh(optionAudioVolume.current);
                    chartRefAudioSpeed.current?.refresh(optionAudioSpeed.current);
                    chartRefAudioRedundancy.current?.refresh(optionAudioRedundancy.current);
                });
            } else if (data.status === 3) {
                setReportDetail(data);
                if (getReportTimer.current) {
                    clearInterval(getReportTimer.current);
                }
                setLoadingFlag(false);
                setReportResult(false);
            } else {
                setReportDetail(data);
                if (getReportTimer.current) {
                    clearInterval(getReportTimer.current);
                }
                if (data.scriptDetailVO.type == 3) {
                    setReportResultPpt(false);
                }
                setLoadingFlag(false);
                setReportResult(false);
            }
        } catch (error) {
            Taro.hideLoading();
            console.log('report', error);
            if (getReportTimer.current) {
                clearInterval(getReportTimer.current);
            }
            setLoadingFlag(false);
            setReportResult(false);
            // if (form === 'share') {
            //     login(`/pages/chat/report/index?form=share&id=${id}`);
            // }
            return;
        }
    });
    useShareAppMessage(() => {
        return {
            title: '邀请你查看我的对话报告',
            path: `/pages/chat/report/index?id=${id}&form=share`
            // imageUrl: 'https://quantum-test-1320888065.cos.ap-nanjing.myqcloud.com/2024/02/23/rpknP0RUAH/avatar.jpg'
        };
    });

    // useEffect(() => {
    //     getresScript().then((resScripts) => {
    //         console.log(resScripts.data.data, 'resScript');
    //     });
    // }, []);
    const handleRetry = async () => {
        const res = await generateReport(chartId.current, true);
        const resScript = await getresScript();
        if (resScript?.data?.data?.length > 0) {
            setcarouselScript(resScript?.data?.data);
        }

        backStatus.current = 4;
        if (res.data.code === 200) {
            setLoadingFlag(true);
            setReportResult(true);
            setTimeout(() => {
                _getReport();
            }, 1500);
        }
    };

    const gotoHistory = () => {
        if (form === 'share') {
            Storage.set(StorageEnvKey.REPORT_DATA, reportDetail);
            Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${reportDetail.chatId}&from=${form}` });
        } else {
            Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${reportDetail.chatId}` });
        }
    };
    const refreshHome = () => {
        if (Number(list_status) && Number(list_status) !== backStatus.current) {
            Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
        }
    };
    const gotoHome = () => {
        Taro.reLaunch({ url: HomePath.MIDDLE });
    };

    const goBack = () => {
        if (form === 'share' || to === 'home') {
            gotoHome();
        } else {
            Taro.navigateBack();
        }
    };

    const prictiseAgian = useCallback(async () => {
        if (reportDetail.scriptDetailVO.type === 3) {
            Taro.navigateTo({
                url: `/pages/practicePPT/detail/index?pptId=${reportDetail.scriptDetailVO.id}&from=history`
            });
        } else {
            try {
                const { data } = await getChatParam(chartId.current);
                Taro.navigateTo({
                    url: `/pages/chat/sceneExercise/index?emanId=${data.data.emanId}&sceneId=${data.data.sceneId}`
                });
            } catch (e) {}
        }
    }, [reportDetail]);

    async function initData() {
        if (form !== 'share') {
            try {
                const shareFalgRes = await getShareSetting();
                setCanShare(shareFalgRes.data.data);
                if (shareFalgRes.data.data) {
                    Taro.showShareMenu({});
                } else {
                    Taro.hideShareMenu();
                }
            } catch (_e) {
                setCanShare(false);
                Taro.hideShareMenu();
            }
        }
        setLoadingFlag(true);
        if (!id) {
            Taro.showToast({ title: '暂无报告', icon: 'none' });
            setTimeout(() => {
                Taro.navigateBack();
            }, 1500);
            return;
        }
        Taro.showLoading();
        const resScript = getresScript().then((resScript) => {
            if (resScript?.data?.data?.length > 0) {
                setcarouselScript(resScript?.data?.data);
            }
        });

        _getReport();
    }

    useLoad(async (options: any) => {
        const token = Storage.get(StorageEnvKey.TOKEN);
        if (options.form === 'share') {
            const res = await getReportCheckLogin(id);
            needLogin.current = !res.data.data;
            if (res.data.data) {
                // 公开，调用无登录接口
                initData();
            } else {
                // 不公开，判断是否有token
                if (token) {
                    // todo: token过期跳转了怎么跳回来
                    initData();
                } else {
                    login({ url: `/pages/chat/report/index?form=share&id=${options.id}` });
                }
            }
        } else {
            // 跳转登录
            initData();
        }
    });

    useUnload(() => {
        clearInterval(getReportTimer.current);
        refreshHome();
        // if (form === 'share' || to === 'home') {
        //     gotoHome();
        // }
    });

    const [sentence, setSentence] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setSentence(true);
        }, 5000); // 5000毫秒 = 5秒

        return () => clearTimeout(timer); // 清理定时器，避免内存泄漏
    }, []); // 空数组依赖项确保useEffect只运行一次
    const [activeTab, setActiveTab] = useState(0);

    const tabsList = useMemo(() => {
        //
        const tempList = [{ tabName: '讲解技巧' }, { tabName: '内容完整性' }];
        if (voiceRateDimensions) {
            tempList.push({
                tabName: '声音流畅度'
            });
        }
        // return [{ tabName: '讲解技巧' }, { tabName: '内容完整性' }, { tabName: '声音流畅度' }]
        return tempList;
    }, [voiceRateDimensions]);
    const tabsListppt = [{ tabName: '演练记录' }, { tabName: '演练要求' }];
    const handleTabChange = (index: any) => {
        console.log('handleTabChange', index);
        setActiveTab(index);
        _getReport();
        // todo: 获取reportDetail的top
        Taro.createSelectorQuery()
            .select('#reportDetail')
            .boundingClientRect((res) => {
                console.log('reportDetail', res);
                if (res.top <= 110) {
                    setScrollIntoView('#reportDetail');
                    Taro.nextTick(() => {
                        setScrollIntoView('');
                    });
                }
            })
            .exec();
    };
    const [dialog, setDialog] = useState(false);
    const confirmBubbless = () => {
        if (reportDetail.scriptDetailVO.type !== 3) return;
        setDialog(true);
        // Dialog_.alert({
        //     title: '综合得分',
        //     showCancelButton: false,
        //     showConfirmButton: false,
        //     // cancelButtonText: '我知道了',
        //     message: (
        //         <View className={styles.content}>
        //             <View className={styles.text}>当前综合得分为： </View>
        //             <View className={styles.text}>{comprehensiveScoreDescription}</View>
        //             {/* <View onClick={onClose} className={styles.text}>我知道了</View> */}
        //             <View className={styles.bottom}>
        //                 <Button
        //                     // style={{
        //                     //     '--padding-md': pxTransform(28),
        //                     //     '--button-normal-height': pxTransform(96)
        //                     // }}
        //                     round
        //                     block
        //                     onClick={() => Dialog_.close()}
        //                     color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
        //                 >
        //                     我知道了
        //                 </Button>
        //             </View>
        //             {/* {typeof content === 'string' ? <Text className={styles.text}>{content}</Text> : content} */}
        //         </View>
        //     )
        // }).then((value) => {
        //     console.log('dialog result', value);
        // });
    };

    return (
        <Page className={styles.page}>
            {/* <NavBar
                className={styles.navbar}
                title='结果报告'
                // leftArrow
                border={false}
                safeAreaInsetTop
                renderLeft={
                    form === 'share' ? (
                        <Icon size={pxTransform(48)} name='wap-home-o' />
                    ) : (
                        <Icon size={pxTransform(48)} name='arrow-left' />
                    )
                }
                onClickLeft={() => goBack()}
            /> */}
            <View className={styles.newNavBar}>
                <View className={styles.crossnewNavBar} onClick={() => goBack()}>
                    {form === 'share' ? (
                        <Icon size={pxTransform(48)} name='wap-home-o' />
                    ) : (
                        <Icon size={pxTransform(48)} name='arrow-left' />
                    )}
                </View>
                <View className={styles.timetit}>结果报告</View>
                <View className={styles.times} />
            </View>
            <ScrollView className={styles.page_body} scrollY scrollIntoView={scrollIntoView}>
                {/* 生成报告中 */}
                {loadingFlag && (
                    <View className={classNames(styles.card, styles.card_full_normal)}>
                        <Image className={styles.report_img} src={ReportImg} />

                        <View className={styles.report_loading}>
                            <View>报告生成中</View>
                            <Image
                                className={styles.report_loading_icon}
                                src={`${config.cdnPrefix}report/loading.svg`}
                            />
                        </View>
                        <View className={styles.report_tip}>预计需要1-5分钟，稍后可以前往首页左上角历史记录查看</View>
                        {sentence && (
                            <View>
                                <View className={styles.swiperTopic}>
                                    <Image
                                        src={`${config.cdnPrefix}bulb.jpg`}
                                        className={styles.score_startop}
                                        mode='aspectFit'
                                    />
                                    拜访小技巧：
                                </View>
                                <Swiper
                                    className={styles.swiperBoxs}
                                    indicatorColor='#999'
                                    indicatorActiveColor='#333'
                                    indicatorDots={false}
                                    circular
                                    autoplay
                                >
                                    {carouselScript.map((item: any, index) => {
                                        const newItem = item.slice(0, 166);
                                        return (
                                            <SwiperItem key={index} className={styles.swiperBox}>
                                                {item?.length > 166 ? (
                                                    <View className={styles.swiperText}>{newItem}...</View>
                                                ) : (
                                                    <View className={styles.swiperText}>{item}</View>
                                                )}
                                            </SwiperItem>
                                        );
                                    })}
                                </Swiper>
                            </View>
                        )}

                        {/* ------------------ */}
                        {waiteButton ? (
                            <View className={styles.report_button_question}>
                                <Button
                                    style={{
                                        '--padding-md': pxTransform(28),
                                        '--button-normal-height': pxTransform(80)
                                    }}
                                    block
                                    round
                                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                    onClick={prictiseAgian}
                                >
                                    再练一次
                                </Button>
                                <Button
                                    onClick={gotoHome}
                                    style={{
                                        '--padding-md': pxTransform(28),
                                        '--button-normal-height': pxTransform(80),
                                        color: '#777777'
                                    }}
                                    round
                                    block
                                    color='#F6F6F6'
                                >
                                    返回首页
                                </Button>
                            </View>
                        ) : null}
                    </View>
                )}

                {/* 生成报告成功 */}
                {reportResult && !loadingFlag && (
                    <View
                        className={classNames(styles.report_content, {
                            [styles.report_nobtn]: form === 'share' || !canShare
                        })}
                    >
                        <View
                            className={
                                form !== 'share' && reportResult && !loadingFlag && canShare ? styles.bottom_share : ''
                            }
                        >
                            {reportDetail.scriptDetailVO.type == 3 ? (
                                <View className={styles.time}>
                                    {reportDetail.chatStartTime &&
                                    dayjs(reportDetail.chatStartTime).isSame(dayjs(), 'year')
                                        ? dayjs(reportDetail.chatStartTime).format('MM月DD日 HH:mm')
                                        : dayjs(reportDetail.chatStartTime).format('YYYY年MM月DD日 HH:mm')}{' '}
                                    时长{chatDuring}
                                </View>
                            ) : null}

                            <View className={styles.card}>
                                <View className={styles.comprehensive_score}>
                                    <Text style={{ paddingRight: pxTransform(9) }}> 综合得分</Text>
                                    {reportDetail.scriptDetailVO.type === 3 && comprehensiveScoreDescription ? (
                                        <Icon
                                            onClick={confirmBubbless}
                                            name='question'
                                            size={pxTransform(36)}
                                            color='#BEBEBE'
                                        />
                                    ) : null}
                                </View>
                                {/* <Dialog_ /> */}
                                <ComprehensiveScoreDialog
                                    show={dialog}
                                    setShow={setDialog}
                                    comprehensiveScoreDescription={comprehensiveScoreDescription}
                                />
                                <view className={styles.report_chart} style={{ overflow: 'hidden' }}>
                                    <Image src={getScoreImg()} className={styles.score_img} />
                                    <EChart ref={chartRef} canvasId='score-canvas' />
                                </view>

                                {reportDetail.scriptDetailVO.type == 3 ? null : (
                                    <View>
                                        <Divider borderColor='#EDEDED' />
                                        <Row gutter={pxTransform(24)}>
                                            <Col span='12'>
                                                <View onClick={gotoHistory} className={styles.report_button}>
                                                    对话记录
                                                </View>
                                            </Col>
                                            <Col span='12'>
                                                <View
                                                    className={styles.report_button}
                                                    onClick={() => setShowScript(true)}
                                                >
                                                    脚本
                                                </View>
                                            </Col>
                                        </Row>

                                        <View className={styles.time}>
                                            {reportDetail.chatStartTime &&
                                            dayjs(reportDetail.chatStartTime).isSame(dayjs(), 'year')
                                                ? dayjs(reportDetail.chatStartTime).format('MM月DD日 HH:mm')
                                                : dayjs(reportDetail.chatStartTime).format('YYYY年MM月DD日 HH:mm')}{' '}
                                            时长{chatDuring}
                                        </View>
                                    </View>
                                )}
                            </View>
                            {(isRadarShow == 1 || isRadarShow == 3) && (
                                <View className={styles.card}>
                                    <View className={styles.score_title}>
                                        <View className={styles.score_title_box}>
                                            <Image src={raderPng} className={styles.score_star} mode='aspectFit' />
                                            <Text className={styles.score_title_text}>能力分析</Text>
                                        </View>
                                    </View>
                                    <Tabs
                                        swipeable
                                        color='#5465F6'
                                        titleActiveColor='#5465F6'
                                        active={0}
                                        onChange={() => _getReport()}
                                    >
                                        <Tab title='主维度'>
                                            <View style={{ width: '100%', height: '100%' }}>
                                                <EChart ref={chartRefRadar} canvasId='score-canvas2' />
                                            </View>
                                        </Tab>
                                        <Tab title='子维度'>
                                            <View style={{ width: '100%', height: '100%' }}>
                                                <EChart ref={chartRefRadarSon} canvasId='score-canvas3' />
                                            </View>
                                        </Tab>
                                    </Tabs>
                                    {/* <Divider borderColor='#EDEDED' /> */}

                                    <View className={styles.summarize}>{reportDetail.sumup}</View>
                                </View>
                            )}

                            <View className={styles.cardaa} id='reportDetail'>
                                {reportDetail.scriptDetailVO.type == 3 ? (
                                    <ScrollableTab
                                        tabs={tabsList}
                                        activeTab={activeTab}
                                        onTabChange={handleTabChange}
                                    />
                                ) : null}

                                <View
                                    className={classNames(styles.tab_content, {
                                        [styles.show]: activeTab === 0
                                    })}
                                >
                                    {reportDetail.scriptDetailVO.type == 3 && (
                                        <>
                                            <View className={styles.score_title}>
                                                <View className={styles.score_title_box}>
                                                    <Image
                                                        src={speackIconSvg}
                                                        className={styles.score_star}
                                                        mode='aspectFit'
                                                    />
                                                    <Text className={styles.score_title_text}>
                                                        讲解技巧
                                                        {typeof reportDetail?.contentScore === 'number'
                                                            ? ` ${reportDetail?.contentScore}分`
                                                            : ' '}
                                                    </Text>
                                                </View>

                                                {skillWeight !== null && (
                                                    <View className={styles.score_title_weight}>
                                                        权重{skillWeight}%
                                                    </View>
                                                )}
                                            </View>
                                            <Divider borderColor='#EDEDED' />
                                        </>
                                    )}
                                    <View className={styles.share_style}>
                                        {reportDetail.dimensionArray?.map((item, index) => {
                                            if (reportDetail.scriptDetailVO.type === ScriptType.SKILL) {
                                                return <CommentsSkill key={`item${index}`} data={item} />;
                                            } else if (reportDetail.scriptDetailVO.type === ScriptType.QUESTION) {
                                                return <CommentsQuestion key={`item${index}`} data={item} />;
                                            } else {
                                                return <CommentsSkill key={`item${index}`} data={item} />;
                                            }
                                        })}
                                    </View>
                                </View>

                                <View
                                    className={classNames(styles.tab_content, {
                                        [styles.show]: activeTab === 1
                                    })}
                                >
                                    {reportDetail.scriptDetailVO.type == 3 && (
                                        <>
                                            <View className={styles.score_title}>
                                                <View className={styles.score_title_box}>
                                                    <Image
                                                        src={allContextPng}
                                                        className={styles.score_star}
                                                        mode='aspectFit'
                                                    />
                                                    <Text className={styles.score_title_text}>
                                                        内容完整性
                                                        {typeof reportDetail?.integrityScore === 'number'
                                                            ? ` ${reportDetail?.integrityScore}分`
                                                            : ' '}
                                                    </Text>
                                                </View>

                                                {contentIntegrityWeight !== null && (
                                                    <View className={styles.score_title_weight}>
                                                        权重
                                                        {contentIntegrityWeight}%
                                                    </View>
                                                )}
                                            </View>
                                            <Divider borderColor='#EDEDED' />
                                        </>
                                    )}
                                    <View className={styles.share_style}>
                                        {activeTab === 1 && (
                                            <AudioPlayer
                                                summary={summary}
                                                chatId={reportDetail.chatId}
                                                backgroundColor='#ffffff'
                                                boxStyle={{ display: 'inline-block' }}
                                            />
                                        )}
                                    </View>
                                    {activeTab === 1 && (
                                        <View className={styles.share_style}>
                                            <ImgSwiper
                                                reportDetail={reportDetail?.scriptDetailVO?.ppt}
                                                pptScoringType={reportDetail?.scriptDetailVO.pptScoringType}
                                            />
                                        </View>
                                    )}
                                    {/* TODO 如果没有评分，只显示 我的讲解 */}
                                    {summaryAnswer && <View className={styles.summary_answer}>{summaryAnswer}</View>}
                                </View>
                                {isRadarShow === 3 && voiceRateDimensions && (
                                    <View
                                        className={classNames(styles.tab_content, { [styles.show]: activeTab === 2 })}
                                    >
                                        <View className={styles.score_title}>
                                            <View className={styles.score_title_box}>
                                                <Image
                                                    src={FluencyPng}
                                                    className={styles.score_star}
                                                    mode='aspectFit'
                                                />
                                                <Text className={styles.score_title_text}>
                                                    声音流畅度
                                                    {typeof reportDetail?.voiceRateScore === 'number'
                                                        ? ` ${reportDetail?.voiceRateScore}分`
                                                        : ' 未涉及'}
                                                </Text>
                                            </View>
                                            {voiceWeight !== null && (
                                                <View className={styles.score_title_weight}>权重{voiceWeight}%</View>
                                            )}
                                        </View>
                                        <Divider borderColor='#EDEDED' />
                                        {voiceRateDimensions ? (
                                            <View>
                                                {' '}
                                                {/* -----------语速 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            {/*  rateVoice   redundantWordVoice     volumeVoice */}
                                                            <Text className={styles.audio_chat_score_bold}>语速：</Text>
                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.rateVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>平均</Text>

                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.rateVoice?.averageSpeakSpeed}
                                                            </Text>

                                                            <Text className={styles.audio_chat_score_text}>
                                                                字/分钟
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <EChart
                                                        ref={chartRefAudioSpeed}
                                                        canvasId='audio-speed'
                                                        width={610}
                                                        height={360}
                                                    />

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.rateVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                                {/* ---------音量 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_bold}>音量：</Text>

                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.volumeVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>平均</Text>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.volumeVoice?.averageVolumeDB}
                                                            </Text>
                                                            <Text className={styles.audio_chat_score_text}>db</Text>
                                                        </View>
                                                    </View>
                                                    <EChart
                                                        width={610}
                                                        height={360}
                                                        ref={chartRefAudioVolume}
                                                        canvasId='audio-volume'
                                                    />

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.volumeVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                                {/* ---------冗余词 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                冗余词：
                                                            </Text>
                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.redundantWordVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>累计</Text>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.redundantWordVoice?.num}
                                                            </Text>

                                                            <Text className={styles.audio_chat_score_text}>次</Text>
                                                        </View>
                                                    </View>
                                                    <View className={styles.chartRefAudioRedundancy}>
                                                        <EChart
                                                            ref={chartRefAudioRedundancy}
                                                            width={610}
                                                            height={360}
                                                            canvasId='audio-Redundancy'
                                                        />
                                                    </View>

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.redundantWordVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>
                                        ) : (
                                            <View className={styles.content_No}>
                                                <Image className={styles.reportss_img} src={soundNo} />
                                                <View className={styles.text}>用户未使用语音，无法进行流畅度打分</View>
                                            </View>
                                        )}
                                    </View>
                                )}
                            </View>
                        </View>
                    </View>
                )}

                {/* ppt生成报告失败 */}
                {!reportResult && !loadingFlag && !reportResultPpt && (
                    <View
                        className={classNames(styles.report_content, {
                            [styles.report_nobtn]: form === 'share' || !canShare
                        })}
                    >
                        <View
                            className={
                                form !== 'share' && reportResult && !loadingFlag && canShare ? styles.bottom_share : ''
                            }
                        >
                            {reportDetail.scriptDetailVO.type == 3 ? (
                                <View className={styles.time}>
                                    {reportDetail.chatStartTime &&
                                    dayjs(reportDetail.chatStartTime).isSame(dayjs(), 'year')
                                        ? dayjs(reportDetail.chatStartTime).format('MM月DD日 HH:mm')
                                        : dayjs(reportDetail.chatStartTime).format('YYYY年MM月DD日 HH:mm')}{' '}
                                    时长{chatDuring}
                                </View>
                            ) : null}
                            <View className={classNames(styles.card, styles.card_full)}>
                                <Image src={IconComplete} className={styles.report_fail} mode='aspectFit' />
                                <View className={styles.tips}>报告生成失败</View>
                                <Button
                                    style={{
                                        '--padding-md': pxTransform(17),
                                        '--button-normal-height': pxTransform(80)
                                    }}
                                    className={styles.btn_retry}
                                    block
                                    round
                                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                    onClick={handleRetry}
                                >
                                    重试
                                </Button>
                            </View>
                            {reportDetail.scriptDetailVO.type == 3 ? (
                                <ScrollableTab tabs={tabsListppt} activeTab={activeTab} onTabChange={handleTabChange} />
                            ) : null}
                            <View className={styles.cardaa}>
                                {activeTab == 0 && voiceRateDimensions ? (
                                    <View>
                                        <AudioPlayer
                                            summary={summary}
                                            chatId={reportDetail.chatId}
                                            backgroundColor='#ffffff'
                                            boxStyle={{ display: 'inline-block' }}
                                        />
                                    </View>
                                ) : (
                                    <View>
                                        <ImgSwiper reportDetail={reportDetail?.scriptDetailVO?.ppt} />
                                    </View>
                                )}
                                {activeTab == 0 && isRadarShow == 3 && voiceRateDimensions ? (
                                    <View className={styles.card}>
                                        <View className={styles.score_title}>
                                            <Image src={FluencyPng} className={styles.score_star} mode='aspectFit' />
                                            <Text>
                                                声音流畅度
                                                {voiceRateDimensions ? ` ${reportDetail?.voiceRateScore}分` : ' 未涉及'}
                                            </Text>
                                        </View>
                                        {voiceRateDimensions ? (
                                            <View>
                                                {/* -----------语速 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            {/*  rateVoice   redundantWordVoice     volumeVoice */}
                                                            <Text className={styles.audio_chat_score_bold}>语速：</Text>
                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.rateVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>平均</Text>

                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.rateVoice?.averageSpeakSpeed}
                                                            </Text>

                                                            <Text className={styles.audio_chat_score_text}>
                                                                字/分钟
                                                            </Text>
                                                        </View>
                                                    </View>

                                                    <EChart
                                                        ref={chartRefAudioSpeed}
                                                        canvasId='audio-speed'
                                                        width={610}
                                                        height={360}
                                                    />

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.rateVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                                {/* ---------音量 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_bold}>音量：</Text>

                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.volumeVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>平均</Text>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.volumeVoice?.averageVolumeDB}
                                                            </Text>
                                                            <Text className={styles.audio_chat_score_text}>db</Text>
                                                        </View>
                                                    </View>
                                                    <EChart
                                                        width={610}
                                                        height={360}
                                                        ref={chartRefAudioVolume}
                                                        canvasId='audio-volume'
                                                    />

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.volumeVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                                {/* ---------冗余词 */}
                                                <View className={styles.audio_charts_block}>
                                                    <View className={styles.audio_chat_score}>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                冗余词：
                                                            </Text>
                                                            <Text className={styles.audio_chat_score_text}>
                                                                {voiceRateDimensions?.redundantWordVoice?.description}
                                                            </Text>
                                                        </View>
                                                        <View>
                                                            <Text className={styles.audio_chat_score_text}>累计</Text>
                                                            <Text className={styles.audio_chat_score_bold}>
                                                                {voiceRateDimensions?.redundantWordVoice?.num}
                                                            </Text>

                                                            <Text className={styles.audio_chat_score_text}>次</Text>
                                                        </View>
                                                    </View>
                                                    <View className={styles.chartRefAudioRedundancy}>
                                                        <EChart
                                                            ref={chartRefAudioRedundancy}
                                                            width={610}
                                                            height={360}
                                                            canvasId='audio-Redundancy'
                                                        />
                                                    </View>

                                                    <View className={styles.adviseText}>
                                                        建议：
                                                        <Text
                                                            style={{ color: '#67686F' }}
                                                            className={styles.adviseTexttip}
                                                        >
                                                            {voiceRateDimensions?.redundantWordVoice?.suggest || '无'}
                                                        </Text>
                                                    </View>
                                                </View>
                                            </View>
                                        ) : (
                                            <View className={styles.content_No}>
                                                <Image className={styles.reportss_img} src={soundNo} />
                                                <View className={styles.text}>用户未使用语音，无法进行流畅度打分</View>
                                            </View>
                                        )}
                                    </View>
                                ) : null}
                            </View>
                        </View>
                    </View>
                )}

                {/* 生成报告失败 */}
                {!reportResult && !loadingFlag && reportResultPpt && (
                    <View className={classNames(styles.card, styles.card_full)}>
                        <Image
                            src={form === 'share' ? ReportAuth : ReportFail}
                            className={styles.report_fail}
                            mode='aspectFit'
                        />
                        <View className={styles.tip}>
                            {form === 'share' ? '暂无查看权限' : '哎呀~报告生成失败了，请重试'}
                        </View>
                        {form !== 'share' && (
                            <Button
                                style={{
                                    '--padding-md': pxTransform(17),
                                    '--button-normal-height': pxTransform(80)
                                }}
                                className={styles.btn_retry}
                                block
                                round
                                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                onClick={handleRetry}
                            >
                                重试
                            </Button>
                        )}
                    </View>
                )}
            </ScrollView>
            {form !== 'share' && reportResult && !loadingFlag && canShare && (
                <View className={styles.bottom}>
                    <Button
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        disabled={loadingFlag}
                        block
                        openType='share'
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        {reportResult && !loadingFlag ? '分享' : '生成后可分享'}
                    </Button>
                </View>
            )}
            <ScriptActionSheet
                showScript={showScript}
                currentScript={reportDetail?.scriptDetailVO}
                emanName={reportDetail.emanName}
                showScriptClose={() => setShowScript(false)}
            />
        </Page>
    );
};

export default App;
