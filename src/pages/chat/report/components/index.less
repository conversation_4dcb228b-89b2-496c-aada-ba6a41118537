.score {
    &_block {
        margin-bottom: 48px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    &_title {
        display: flex;
        margin-top: 40px - 28px;
        margin-bottom: 20px;
        font-weight: bold;
        font-size: 32px;
        align-items: center;
    }

    &_name {
        flex: 1;
        margin-right: 1em;
    }

    &_star {
        width: 36px;
        height: 36px;
        margin-right: 16px;
    }

    &_content {
        box-sizing: border-box;
        padding: 28px;
        color: #67686f;
        font-weight: 400;
        font-size: 28px;
        background: #f6f7fb;
        border-radius: 32px;

    }

    &_item {
        position: relative;
        box-sizing: border-box;
        margin-bottom: 28px;
        padding-left: 28px;

        &::before {
            position: absolute;
            top: 14px;
            left: 0;
            width: 12px;
            height: 12px;
            background-color: #4f66ff;
            border-radius: 12px;
            content: '';
        }
    }

    &_comment {
        margin-bottom: 24px;
    }

    &_advice {
        color: #4f66ff;
        font-weight: bold;
    }

    &_more {
        color: #4f66ff;
        font-size: 24px;
        text-align: right;

        text {
            padding: 5px 10px;
        }
    }
}