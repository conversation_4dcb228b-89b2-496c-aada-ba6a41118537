import { ScrollView, Text, View } from '@tarojs/components';
import React, { useState } from 'react';
import styles from './index.less';

const ScrollableTab = ({ tabs, activeTab, onTabChange }) => {
    const handleTabClick = (index) => {
        onTabChange(index);
    };
    return (
        <View className={styles.container}>
            <View className={styles.tab_scroll}>
                {tabs.map((tab, index) => (
                    <View
                        key={index}
                        id={`tab${index}`}
                        className={`${styles.tab_item}  ${
                            activeTab === index
                                ? index == 0
                                    ? styles.active1
                                    : index == 2
                                    ? styles.active3
                                    : styles.active
                                : ''
                        }`}
                        onClick={() => handleTabClick(index)}
                    >
                        <View>{tab.tabName}</View>
                    </View>
                ))}
            </View>
        </View>
    );
};

export default ScrollableTab;
