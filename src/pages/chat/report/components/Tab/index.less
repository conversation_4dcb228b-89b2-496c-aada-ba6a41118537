 .container {
   border-radius: 16.5px;
   background-color: #FAFAFA;
   //  margin-bottom: -20px;
   //  margin-top: -20px;
   margin-bottom: 32px;
   z-index: 99;
   //  margin-right: 32px;
   //  margin-left: 32px;
   position: sticky;
   /* 关键：设置为 sticky */
   top: 0;
   /* 固定在顶部 */
   overflow: hidden;
 }

 .tab_scroll {
   // width: 100%;
   display: flex;
   height: 72px;
   align-items: center;
   white-space: nowrap;
   overflow: hidden;
 }

 .tab_item {
   flex: 1;
   height: 72px;
   padding: 10px 20px;
   text-align: center;
   cursor: pointer;
   line-height: 56px;
   box-sizing: border-box;
   color: #666666;
 }

 .active1 {
   border-radius: 16.5px 0px 0px 16.5px;
   background-color: #4F66FF;
   color: #fff;
 }

 .active3 {
   border-radius: 0px 16.5px 16.5px 0px;
   background-color: #4F66FF;
   color: #fff;
 }

 .active {
   background-color: #4F66FF;
   color: #fff;
 }

 .tab_content {
   padding: 20px;
 }