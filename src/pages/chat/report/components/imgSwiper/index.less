.image_text {
   display: flex;
   align-items: center;
   text-align: center;

   // justify-content: space-between;
   font-size: 24px;
   color: #67686F;
   margin-top: 24px;
}

.arrow {
   margin-right: 20px;
}

.currentIndex {
   // margin-right: 100px;
   flex: 1;
   width: 160px;
   color: #272C47;
}

.goToPrev {
   width: 100px;
   margin-right: 10px;
}

.goToNext {
   width: 100px;
   margin-right: 20px;
}

.score {
   color: #272c47;
   font-size: 32px;
   font-weight: 500;
   margin-top: 42px;
}

.require {
   color: #67686F;
   margin-top: 30px;
   font-size: 26px;
}

.history {
   padding-top: 40px;


   &_empty {
      margin-top: 250px;
      text-align: center;

      &_img {
         margin: auto;
      }

      &_txt {
         margin-top: 24px;
         color: #9597a0;
         text-align: center;
         font-size: 28px;
         line-height: 40px;
      }
   }
}

.label_name {
   color: #4F66FF;
   font-weight: 500;
   font-size: 28px;
   margin-bottom: 12px;
}