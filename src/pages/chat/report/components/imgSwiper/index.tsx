import config from '@/config';
import { Icon, Image } from '@antmjs/vantui';
import { Swiper, SwiperItem, Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import { useMemo, useState } from 'react';
// import './index.scss';
import { useEvent } from '@hera/react-utils';
import styles from './index.less';
export default function Index(props: any) {
    const [currentIndex, setCurrentIndex] = useState(0);
    const [height] = useState(200);
    const goToNext = useEvent(() => {
        const nextIndex = currentIndex + 1;
        const n = nextIndex >= props?.reportDetail?.length ? 0 : nextIndex;
        console.log('next', currentIndex, n);
        setCurrentIndex(n);
    });
    const goToPrev = useEvent(() => {
        const prevIndex = currentIndex - 1;
        const n = prevIndex < 0 ? props?.reportDetail?.length - 1 : prevIndex;
        console.log('prev', currentIndex, n);
        setCurrentIndex(n);
    });
    // 监听 Swiper 切换事件，更新当前索引
    const handleChange = (event: any) => {
        setCurrentIndex(event.detail.current);
    };
    const audioItem = useMemo(() => {
        console.log('audioItem', props?.reportDetail, currentIndex);
        return props?.reportDetail?.[currentIndex];
    }, [props?.reportDetail, currentIndex]);
    const keywords = useMemo(() => {
        const keywordList = JSON.parse(audioItem?.keywords || '[]');
        if (Array.isArray(keywordList)) {
            return keywordList
                .reduce((pre: string[], cur: { name: string; synonyms: string[]; remark: string }) => {
                    const synonyms = cur.synonyms || [];
                    const name = cur.name || '';
                    const kwsList = [name, ...synonyms].filter(Boolean).join('/');
                    return [...pre, kwsList];
                }, [])
                .join('、');
        }
        return '';
    }, [audioItem?.keywords]);
    return (
        <View>
            <View>
                {props?.reportDetail ? (
                    <>
                        <View>
                            <Swiper
                                current={currentIndex}
                                onChange={handleChange}
                                autoplay={false}
                                circular
                                style={{ borderRadius: 12, height }}
                            >
                                {props?.reportDetail?.map((item: any, index: any) => (
                                    <SwiperItem key={`swiper#demo1${index}`}>
                                        <Image
                                            src={item.imageUrl}
                                            // style={{ borderRadius: `${pxTransform(16)} ${pxTransform(16)} 0 0` }}
                                            // className={styles.history_user_avatar}
                                            radius={pxTransform(16)}
                                            width={pxTransform(634)}
                                            height={pxTransform(360)}
                                            fit='cover'
                                        />
                                    </SwiperItem>
                                ))}
                            </Swiper>
                            <View className={styles.image_text}>
                                <Icon className={styles.arrow} name='arrow-left' size={pxTransform(24)} />
                                <View onClick={goToPrev} className={styles.goToPrev}>
                                    上一页
                                </View>
                                <View className={styles.currentIndex}>
                                    第{currentIndex + 1}/{props?.reportDetail.length}页
                                </View>
                                <View className={styles.goToNext} onClick={goToNext}>
                                    下一页
                                </View>
                                <Icon name='arrow' size={pxTransform(24)} />
                            </View>
                            {/* 得分 */}
                            {typeof audioItem.score === 'number' && (
                                <View className={styles.score}>得分：{audioItem.score}/100</View>
                            )}

                            {/* 演练要求 */}
                            {props.pptScoringType === 1 && typeof audioItem.score === 'number' && (
                                <View className={styles.require}>
                                    <Text className={styles.label_name}>演练要求：</Text>{' '}
                                    {audioItem.requirement || '无'}
                                </View>
                            )}

                            {/* 关键词 */}
                            {[2, 3].includes(props.pptScoringType) && typeof audioItem.score === 'number' && (
                                <View className={styles.require}>
                                    <Text className={styles.label_name}>关键词：</Text> {keywords || '无'}
                                </View>
                            )}

                            {/* 我的讲解 */}
                            {typeof audioItem.score === 'number' && (
                                <View className={styles.require}>
                                    <Text className={styles.label_name}>我的讲解：</Text>
                                    {audioItem.answer || '无'}
                                </View>
                            )}

                            {/* 点评 */}
                            {typeof audioItem.score === 'number' && (
                                <View className={styles.require}>
                                    <Text className={styles.label_name}>点评：</Text> {audioItem.comment || '无'}
                                </View>
                            )}
                        </View>
                    </>
                ) : (
                    <View className={styles.history_empty}>
                        <Image
                            fit='cover'
                            width={pxTransform(400)}
                            height={pxTransform(200)}
                            src={`${config.cdnPrefix}slide/no_record.png`}
                        />
                        <View className={styles.history_empty_txt}>无演练要求</View>
                    </View>
                )}
            </View>
        </View>
    );
}
