import AudioPlayer from '@/components/audioPlayer';
import type { PPTSummaryVo } from '@/types/chat';
import { View } from '@tarojs/components';
// import styles from './index.less';
export default function Index(props: { src: string; summary: PPTSummaryVo; chatId: string }) {
    const { summary, chatId } = props;
    return (
        <View>
            {summary && summary.audioUrl && <AudioPlayer id={chatId} url={summary.audioUrl} />}
            {/* <View className={styles.audio_summary}>{summary && summary.answer}</View> */}
        </View>
    );
}
