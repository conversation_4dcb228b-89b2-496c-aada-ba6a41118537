import { Image, Text, View } from '@tarojs/components';
import styles from './index.less';

import config from '@/config';
const IconStar = `${config.cdnPrefix}report/icon_star.png`;

interface Props {
    data: any;
}

const Index: React.FC<Props> = (props) => {
    const { data } = props;
    return (
        <View className={styles.score_block}>
            <View className={styles.score_title}>
                <Image src={IconStar} className={styles.score_star} mode='aspectFit' />
                <Text>
                    {data.name}：{data.score}/{data.fullScore}分
                </Text>
            </View>
            <View className={styles.score_content}>
                {data.children.map((element: any, index: any) => {
                    return (
                        <View className={styles.score_item} key={`element${index}`}>
                            {element.name}： {element.score}/ {element.fullScore}分
                            {element.remark && (
                                <Text>
                                    {' '}
                                    （{element.remark} {element.suggestAnswer ? '' : '）'}
                                </Text>
                            )}
                            {element.suggestAnswer && <Text>可参考话术“{element.suggestAnswer}”）</Text>}
                        </View>
                    );
                })}
                <View>
                    <Text className={styles.score_advice}>建议：</Text>
                    {data.suggest || '暂无'}
                </View>
            </View>
        </View>
    );
};

export default Index;
