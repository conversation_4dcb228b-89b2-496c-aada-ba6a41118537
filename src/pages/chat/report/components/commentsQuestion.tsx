import { Image, Text, View } from '@tarojs/components';
import { useState } from 'react';
import styles from './index.less';
import config from '@/config';

interface Props {
    data: any;
}

const IconStar = `${config.cdnPrefix}report/icon_star.png`;

const Index: React.FC<Props> = (props) => {
    const { data } = props;
    const [suggestVisible, setSuggestVisible] = useState(false);
    const toggleVisible = () => {
        setSuggestVisible(!suggestVisible);
    };
    return (
        <View className={styles.score_block}>
            <View className={styles.score_title}>
                <Image src={IconStar} className={styles.score_star} mode='aspectFit' />
                <Text className={styles.score_name}>{data.question}</Text>
                <Text>{data.score}分</Text>
            </View>
            <View className={styles.score_content}>
                <View className={styles.score_comment}>
                    <Text className={styles.score_advice}>点评：</Text>
                    {data.comment || '暂无'}
                </View>
                <View className={styles.score_comment}>
                    <Text className={styles.score_advice}>建议：</Text>
                    {data.suggest || '暂无'}
                </View>
                <View style={{ display: suggestVisible ? 'block' : 'none' }}>
                    <View className={styles.score_comment}>
                        <Text className={styles.score_advice}>我的回答：</Text>
                        {data.answer || '暂无'}
                    </View>
                    <View className={styles.score_comment}>
                        <Text className={styles.score_advice}>参考答案：</Text>
                        {data.suggestAnswer || '暂无'}
                    </View>
                </View>
                <View className={styles.score_more}>
                    <Text onClick={toggleVisible}>{suggestVisible ? '折叠' : '参考答案'}</Text>
                </View>
            </View>
        </View>
    );
};

export default Index;
