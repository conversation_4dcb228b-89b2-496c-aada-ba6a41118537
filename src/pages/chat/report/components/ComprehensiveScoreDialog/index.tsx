import { Dialog } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { View } from '@tarojs/components';
import styles from './index.less';

interface IProps {
    show: boolean;
    setShow: (show: boolean) => void;
    comprehensiveScoreDescription: string;
}

export default function ComprehensiveScoreDialog({ show, setShow, comprehensiveScoreDescription }: IProps) {
    return (
        <Dialog show={show} onClose={() => setShow(false)} showConfirmButton={false}>
            <View className={styles.content}>
                <View className={styles.title}>综合得分</View>
                <View className={styles.text}>当前综合得分为： </View>
                <View className={styles.text}>{comprehensiveScoreDescription}</View>
                <View className={styles.bottom}>
                    <Button
                        round
                        block
                        onClick={() => setShow(false)}
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        我知道了
                    </Button>
                </View>
            </View>
        </Dialog>
    );
}
