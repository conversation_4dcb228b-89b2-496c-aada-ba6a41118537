@import '@/styles/index.less';

page {
    background: linear-gradient(191deg, #dddeff99 1.1%, #eef3ff99 22%),
        linear-gradient(169deg, #cff7f4 0.76%, #d1ebff 12.52%, #fff 36%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
}

.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.page_body {
    flex: 1;
    box-sizing: border-box;
    overflow-y: auto;
    position: relative;
}

.newNavBar {
    margin-top: 100rpx;
    /* z-index: 1; */
    /* width: 100%; */
    font-size: 16px;
    height: 100rpx;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
    vertical-align: middle;
    /* top: 8rpx; */
    align-items: center;
    /* left: 20rpx; */
    /* position: absolute; */
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.times {
    height: 20px;
    width: 10px;
}

.timetit {
    padding-right: 68px;
    font-weight: 800;
    font-weight: 400;
    font-size: 32px;

}

.crossnewNavBar {
    padding-left: 47rpx;
}

.swiperText {

    // text-align: center;
    margin-left: 30px;
    margin-right: 30px;
    font-size: 24px;
    margin-top: 23px;
    font-weight: 500;
    color: #272C47;
}

.swiperTopic {
    margin-bottom: 12px;
    margin-top: 46px;
    margin-left: 30px;
    font-size: 24px;
    color: #67686F;
}

:global {
    .score-canvas2 {
        height: 420px !important;
        width: 610px !important;
        margin-top: 30px !important;
        pointer-events: none !important;
        // border: solid 1px saddlebrown;
    }
}

:global {
    .score-canvas3 {
        height: 420px !important;
        width: 610px !important;
        margin-top: 30px !important;
        pointer-events: none !important;
        // border: solid 1px saddlebrown;
    }
}

:global {
    .audio-speed {
        height: 360px !important;
        width: 630px !important;
        // margin-top: 30px !important;
        pointer-events: none !important;
        // border: solid 1px saddlebrown;
    }
}

:global {
    .audio-volume {
        height: 360px !important;
        width: 630px !important;
        // margin-top: 30px !important;
        pointer-events: none !important;
        // border: solid 1px saddlebrown;
    }
}

:global {
    .audio-Redundancy {
        height: 360px !important;
        width: 630px !important;
        // width: 120px !important;
        // margin-top: 30px !important;
        pointer-events: none !important;
        // border: solid 1px saddlebrown;
    }
}

.swiperBoxs {
    height: 295px;
    margin-bottom: 8px;
    background-color: rgba(140, 178, 255, 0.1);
}

.swiperBox {
    height: 295px;
    margin-top: 12px;

}

.score_star {
    width: 36px;
    height: 36px;
    margin-right: 16px;
}

.score_startop {
    width: 22px;
    height: 28px;
    margin-right: 16px;
    margin-top: 5px;
    margin-bottom: -2px;
}

.score_title {
    display: flex;
    margin-top: 40px - 28px;
    margin-bottom: 20px;
   
    justify-content: space-between;
    align-items: center;
    overflow: hidden;

    &_box {
        display: flex;
        align-items: center;
    }
    &_text {
        font-weight: bold;
        font-size: 32px;
    }

    &_weight {
        font-size: 28px;
        color: #9597A0;
    }
}

.cardaa {
    // height: 100vh;
    box-sizing: border-box;
    margin: 48px 32px;
    padding: 28px;

    background: #fff;
    border-radius: 32px;
    position: relative;

    &:first-child {
        margin-top: 46px;
    }
}

.card {
    box-sizing: border-box;
    margin: 48px 32px;
    padding: 28px;
    background: #fff;
    border-radius: 32px;
    position: relative;

    &:first-child {
        margin-top: 46px;
    }
}

.card_full {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(100% - 48px - 96px);
    margin-top: 48px !important;
}

.card_full_normal {
    height: calc(100% - 48px - 96px);
    margin-top: 48px !important;
    position: relative;
}

.report_content {
    padding-bottom: 180px;
}

.report_nobtn {
    padding-bottom: 60px;
}

.progress {
    &_box {
        display: flex;
        align-items: center;
        box-sizing: border-box;
        width: 100%;
        padding-right: 70px;
        padding-left: 70px;
    }

    &_bar {
        flex: 1;
    }

    &_text {
        margin-left: 16px;
        font-weight: 400;
        font-size: 28px;
    }
}

.tip {
    margin-top: 40px;
    color: #9597a0;
    font-weight: 400;
    font-size: 28px;
}

.tips {
    margin-top: 40px;
    color: #000000;
    font-weight: 400;
    font-size: 28px;
}

.report_fail {
    width: 400px;
    height: 172px;
}

.btn_retry {
    width: 318px;
    margin-top: 40px;
}

.report_chart {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 290px;
    margin-bottom: -50px;
}

.score_img {
    position: absolute;
    top: 12px;
    right: 12px;
    width: 118px;
    height: 104px;
}

.summarize {
    margin-top: 40px;
    margin-bottom: 36px;
    font-weight: 400;
    font-size: 28px;
    line-height: 40px;
}

.report_button {
    height: 99px;
    color: #272c47;
    font-weight: 400;
    font-size: 28px;
    line-height: 99px;
    text-align: center;
    background: #fafafa;
    border: none;
    border-radius: 16px;
}

.time {
    margin-top: 32px;
    margin-bottom: 36px - 28px;
    font-weight: 400;
    font-size: 24px;
    text-align: center;
}

.bottom {
    .footer-fixed;

    bottom: 0;
    z-index: 100;
    box-sizing: border-box;
    padding: 21px 36px 48px 36px;
    // background-color: #fff;
}

.report_img {
    width: 288px;
    height: 288px;
    display: block;
    margin: 0 auto;
    padding-top: 74px;
}

.report_loading {
    color: #272c47;
    font-weight: 600;
    font-size: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 32px;

    &_icon {
        width: 48px;
        height: 48px;
        display: block;
        margin-left: 16px;
        animation: spin 1s linear infinite;
    }
}

.report_tip {
    text-align: center;
    font-size: 28px;
    font-weight: 400;
    line-height: 40px;
    margin: 32px 120px 0 120px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.report_button_question {
    width: calc(100% - 60px);
    display: flex;
    flex-direction: column;
    gap: 24px;
    position: absolute;
    bottom: 80px - 28px;
    left: 30px;
    right: 30px;
}

.share_style {
    //padding-bottom: 100px;
}

.audio_speed_chat {
    width: 730px;
    height: 240px;
}

.audio_chat_score {
    display: flex;
    justify-content: space-between;
    font-size: 28px;
    color: #6D6D6D;

    &_bold {
        font-size: 28px;
        font-weight: 500;
        color: #272C47;
    }

    &_text {
        font-size: 28px;
        color: #6D6D6D;
        font-weight: 400;
    }
}

.adviseTexttip {
    font-weight: 400;
}

.adviseText {
    font-weight: 800;
    font-size: 28px;
    margin-bottom: 32px;
    color: #4F66FF
}

.chartRefAudioRedundancy {
    margin-top: 30px;
    margin-bottom: 20px;
}

.comprehensive_score {
    font-size: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9597A0;
}

.content_No {
    position: relative;
    z-index: 1;

    .texts_core {
        font-size: 32px;
        font-weight: 800;
        margin-bottom: 24px;
        text-align: center;
        color: #272C47;
    }

    .text {
        text-align: center;
        font-size: 28rpx;
        margin-bottom: 12px;
        color: #67686F;
        line-height: 1.5;
    }
}

.reportss_img {
    width: 240px;
    height: 156px;
    display: block;
    margin: 0 auto;
    padding-bottom: 24px;
}

// .saassas{
//     border-radius: 16.5px;
//    background-color: #FAFAFA;
//    margin-bottom: -20px;
//    margin-top: -20px;
//    z-index: 99;
//    margin-right: 32px;
//    margin-left: 32px;
//    position: sticky; /* 关键：设置为 sticky */
//    top: 0; /* 固定在顶部 */
//    }
// components/BubbleCard/index.scss
.bubble_card {
    position: absolute;
    padding: 20rpx;
    width: 608px;
    border-radius: 12rpx;
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    z-index: 1000;

    &_top {
        margin-bottom: 16rpx;
    }



    .content {
        position: relative;
        z-index: 1;

        .texts_core {
            font-size: 32px;
            font-weight: 800;
            margin-bottom: 24px;
            text-align: center;
            color: #272C47;
        }

        .text {
            text-align: center;
            font-size: 28rpx;
            margin-bottom: 12px;
            color: #67686F;
            line-height: 1.5;
        }
    }

    .triangle {
        position: absolute;
        width: 0;
        height: 0;
        z-index: 1001; // 确保在内容层上方

        &_top {
            border-width: 0 16rpx 16rpx 16rpx;
            border-color: transparent transparent var(--triangle-color, #fff) transparent;
            bottom: -16rpx;
            left: 50%;
            transform: translateX(-50%);
            top: -14px !important;
            left: 59% !important;
            border-top-color: #eee0 !important;
            border-left-color: #eee0 !important;
            border-right-color: #eee0 !important;
            // border-bottom-color: rgba(221, 11, 11, 0.667) !important;
            border-bottom-color: linear-gradient(#000, #fff) !important;
            border-style: solid !important;
            transform: translateX(-50%);
        }


    }
}

.bottom {
    bottom: 0;
    box-sizing: border-box;
    padding: 21px 36px 48px 36px;

}

.summary_answer {
    color: #67686F;
    font-size: 26px;
    white-space: pre-wrap;
}

.tab_content {
    display: none;
    &.show {
        display: block;
    }
}