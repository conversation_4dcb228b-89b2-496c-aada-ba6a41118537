import IconHelpOff from '@/assets/icon_help_off.svg';
import IconVideo from '@/assets/icon_video.svg';
import IconVideoClose from '@/assets/icon_video_close.svg';
import { Storage, useEvent, useRequest, useStorage } from '@/common';
import Aihelp from '@/components/aihelp';
import ChatHistory from '@/components/chatHistory';
import FeedbackDialog from '@/components/feedbackDialog';
import FeedbackSucess from '@/components/feedbackSucess';
import HarmonyDialog from '@/components/HarmonyDialog';
import VoiceLoading from '@/components/voiceLoading';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import {
    ChatMode,
    getVoiceStatusKey,
    RecognizeStatus,
    SubtitleVisible,
    VoiceInteraction,
    VoiceStatus
} from '@/constants/voicetype';
import RecordDialog from '@/pages/chat/components/recordDialog';
import { ttsErrorHandler } from '@/pages/chat/components/utils';
import VoiceWave from '@/pages/practicePPT/practice/components/practicePPTDialog/generateBars';
import {
    doneChat,
    doneGenerateReport,
    generateReport,
    getAllChatHistory,
    qaAnswerRewrite,
    qaCompletions,
    qaSave
} from '@/services/chat';
import { getServerTime, ttsVoice, uploadLog } from '@/services/common';
import { audioUploadComplete, cosUpload } from '@/services/cos';
import type { ChatVO, HistoryVO, QaItem, Question } from '@/types/chat';
import { ChatActionSheetType, RoleEnum } from '@/types/chat';
import type { FeedbackData, LogInfo } from '@/types/common';
import { blurImage } from '@/utils/imageUtils';
import { checkHarmony } from '@/utils/permission';
import { removeDuplicateText } from '@/utils/sentenseUtils';
import AudioSpeechRecognizer from '@/utils/speechRecognizer/AudioSpeechRecognizer';
import { ActionSheet, Dialog, Form, FormItem, Icon, NavBar, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Canvas, Image, Text, Textarea, View } from '@tarojs/components';
import type { NodesRef } from '@tarojs/taro';
import Taro, {
    createInnerAudioContext,
    createSelectorQuery,
    getCurrentPages,
    getFileSystemManager,
    nextTick,
    offNetworkWeakChange,
    onNetworkWeakChange,
    pageScrollTo,
    pxTransform,
    setInnerAudioOption,
    setKeepScreenOn
} from '@tarojs/taro';
import { useCountDown, useMount, useThrottleFn, useUnmount } from 'ahooks';
import classnames from 'classnames';
import dayjs from 'dayjs';
import dayjsDuration from 'dayjs/plugin/duration';
import lottie from 'lottie-miniprogram';
import type { ReactNode } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import '../../../assets/vant.global.less';
import ActionMore from '../actionMore/index';
import ActionVoice from '../actionVoice/index';
import styles from '../dialog.less';
import voiceStyle from '../voice.less';

dayjs.extend(dayjsDuration);
const IconChat = `${config.cdnPrefix}chat/icon_chat.svg`;
const IconChatDisabled = `${config.cdnPrefix}chat/icon_chat_disabled.svg`;
const send_0 = `${config.cdnPrefix}dialogue/send_0.png`;
const send_1 = `${config.cdnPrefix}dialogue/send_1.png`;
const TelImg = `${config.cdnPrefix}dialogue/tel.png`;
const scriptImg = `${config.cdnPrefix}sceneExercise/script.png`;
const IconCancel = `${config.cdnPrefix}svg/icon_cancel.svg`;
const IconComplete = `${config.cdnPrefix}svg/icon_complete.svg`;
const IconDrop = `${config.cdnPrefix}svg/icon_drop.svg`;
const IconFly = `${config.cdnPrefix}svg/icon_fly.svg`;
const IconHangup = `${config.cdnPrefix}svg/icon_hangup.svg`;
const IconHangupMini = `${config.cdnPrefix}svg/icon_hangup_mini.svg`;
const IconMore = `${config.cdnPrefix}svg/icon_more.svg`;
const IconSpeak = `${config.cdnPrefix}svg/icon_speak.svg`;
const IconSpeakDisabled = `${config.cdnPrefix}svg/icon_speak_disabled.svg`;
const IconStop = `${config.cdnPrefix}svg/icon_stop.svg`;
const IconHelpBlue = `${config.cdnPrefix}aihelp/icon_help_blue.svg`;
const IconHelp = `${config.cdnPrefix}aihelp/icon_help.svg`;
const IconSubtitleOff = `${config.cdnPrefix}svg/icon_subtitle_off.svg`;
const IconSubtitleOn = `${config.cdnPrefix}svg/icon_subtitle_on.svg`;

// import VoiceDialogueQuestion from '../voiceDialogueQuestion';
type Props = {
    chatId: string;
    data?: ChatVO;
    interrupt: string;
    from: string;
    mode: string;
    onShowScript: () => void;
    onLoaded: () => void;
};
const enum SpeechStatus {
    PlayStart,
    Playing,
    PlayEnd
}
const defaultTone = {
    name: '',
    speed: 0.4, // [-2，2]，分别对应不同语速：-2代表0.6倍，-1代表0.8倍，0代表1.0倍（默认），1代表1.2倍，2代表1.5倍。如果需要更细化的语速，可以保留小数点后一位，例如0.5 1.1 1.8等
    volume: 0, // [0，10]
    voiceType: 301035 // 音色,爱小梅-多情感女声
};
const overtime = 5000; // 不说话超时时间
const standbyTime = 5000; // 超时后待机时间
const slienceTime = 2000; // 不说话时间
const speechLength = 145; // 文本转换长度

const audio = createInnerAudioContext();
const Dialog_ = Dialog.createOnlyDialog();
const Toast_ = Toast.createOnlyToast();
const Index: React.FC<Props> = (props) => {
    const { chatId, interrupt, from, data, onShowScript, onLoaded } = props;
    const speechRecognizerManager = useRef<AudioSpeechRecognizer>(); // 获取实时录音识别管理器
    const userInfo = Storage.get(StorageEnvKey.USERINFO);
    const time = useRef(0); // 便于开发对话页
    const [history, setHistory] = useState<HistoryVO[]>([]);
    const historyRef = useRef<HistoryVO[]>([]);
    const [assistantMessage, setAssistantMessage] = useState<string>();
    const [canSend, setCanSend] = useState(false);
    const [chatActionSheetType, setChatActionSheetType] = useState<ChatActionSheetType>();
    const [showActionSheet, setShowActionSheet] = useState(false);
    const [leftTime, setLeftTime] = useState<number>();
    const [keyHeight, setKeyHeight] = useState(0);
    const [translateHeight, setTranslateHeight] = useState(0);
    const [showScriptBtn, setShowScriptBtn] = useState(true);
    const [aiHelpEnable, setAiHelpEnable] = useState(true);
    const [canGenerate, setCanGenerate] = useState<boolean>(true);
    const [aiHelpShow, setAiHelpShow] = useState<boolean>(false);
    const [customTitle, setCustomTitle] = useState({ title: '' });
    const [showVideoClose, setShowVideoClose] = useState(ChatMode.SHUZIREN);
    // const voicePageRef = useRef<any>();
    const chatData = useRef({ chatReportId: '', done: false }); // 或者直接用chat里的数据，不需要chatData
    const [showVoicePage, setShowVoicePage] = useState(false); // 语音界面显示
    const showVoicePageRef = useRef<boolean>(false);
    const questions = useRef<QaItem[]>([]);
    const [questionList, setQuestionList] = useState<QaItem[]>([]);
    const [questionNow, setQuestionNow] = useState(0);
    const questionNowRef = useRef(0);
    const [questionLoading, setQuestionLoading] = useState(false);
    const [decibelPlay, setdecibelPlay] = useState<any>('');
    const [isZore, setisZore] = useState<any>('');
    let isadd = 1;
    const createTime = useRef('');
    const serverTime = useRef('');
    const [voiceInteraction, setVoiceInteraction] = useState(VoiceInteraction.Manual);
    const voiceInteractionRef = useRef<VoiceInteraction>();
    const [showMoreActionSheet, setShowMoreActionSheet] = useState(false);
    const [showVoiceActionSheet, setShowVoiceActionSheet] = useState(false);

    const isTimeout = useRef<boolean>(false);
    const [leftMinutes, setLeftMinutes] = useState<number>();
    const [leftSeconds, setLeftSeconds] = useState<number>();

    const [timeLeftText, setTimeLeftText] = useState<string>();

    const leftMinutesRef = useRef<number>();
    const leftSecondsRef = useRef<number>();
    const isOver = useRef<boolean>(false);

    const recognizeStatusRef = useRef<RecognizeStatus>();
    const statusRef = useRef<VoiceStatus>();
    const [status, setStatus] = useState<VoiceStatus>();
    const [statusText, setStatusText] = useState<string[]>();
    const [statusLoading, setStatusLoading] = useState<ReactNode>();
    const intervalSpeech = useRef<any>(); // 定时检查语音播放
    const intervalText = useRef<any>(); // 定时检查ai文本
    const aiSentence = useRef<string[]>([]); // AI语音回复的文字
    const userSentence = useRef<{
        i: number;
        text: string;
        filePath?: string;
        fileSize?: number;
        chatHistoryId?: string;
    }>(); // 用户语音识别出来的文字
    const aiVoiceList = useRef<{ id: number; text: string; url: string }[]>([]);
    const aiVoiceNow = useRef<{
        id: number;
        text: string;
        url: string;
        status: SpeechStatus;
    } | null>();

    const timerOvertime = useRef<any>(); // 5s不说话倒计时
    const timerStandby = useRef<any>(); // 待机倒计时
    const timerNoSentence = useRef<any>(); // 只有录音没有识别完成回调
    const bubbleAnimate = useRef<any>();
    const waveAnimate = useRef<any>();
    const tone = useRef({ ...defaultTone });
    const isFinish = useRef<boolean>(false);
    const [sayPressed, setSayPressed] = useState<boolean>(false);

    const [recognizerTime, setRecognizerTime] = useState<number>(60);
    const recognizerTimeRef = useRef<any>();

    const [subtitleShow, setSubtitleShow] = useState<SubtitleVisible>(SubtitleVisible.Show);
    const [subtitleText, setSubtitleText] = useState<string>();

    const touchTop = useRef<number>(660);

    const [feedbackShow, setFeedbackShow] = useState<boolean>(false);
    const [feedbackSuccessShow, setFeedbackSuccessShow] = useState<boolean>(false);
    const feedbackData = useRef<FeedbackData>({
        client: 'wechat',
        version: '',
        brand: '',
        wxVersion: '',
        SDKVersion: '',
        model: '',
        system: '',
        platform: '',
        environment: '',
        microphoneAuthorized: undefined,
        appId: '',
        name: '',
        phone: '',
        company: '',
        path: '',
        chatId: '',
        description: '',
        logs: []
    });

    const [recordDialogShow, setRecordDialogShow] = useState<boolean>(false);
    const AvatarDefault = `${config.cdnPrefix}avatar_default.png`;
    const [isQnq, setIsQnq] = useState<boolean>(false);
    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);
    const formIt = Form.useForm();
    const timeLimitType = useRef<number>();
    const questionTimer = useRef<any>();
    const [questionTime, setQuestionTime] = useState<number>();
    const questionTimeout = useRef<boolean>(false);
    const scriptQuestions = useRef<Question[]>();

    const isHarmonyRef = useRef<boolean>(false);
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    // const audioDurationTimer = useRef<any>();
    // function clearAudioDurationTimer() {
    //     if (audioDurationTimer.current) {
    //         clearTimeout(audioDurationTimer.current);
    //         audioDurationTimer.current = null;
    //     }
    // }
    let bottomContent_height = 0;
    let topBox_height = 0;
    let window_height = 0;

    const backgroundBlur = useMemo(() => {
        const bg = data?.eman.background || '';
        if (bg && isWework === '0') {
            const url = blurImage(bg, isQnq ? 'huawei' : 'tencent');
            return url;
        } else {
            return bg;
        }
    }, [data, isWework, isQnq]);
    const onScrollView = () => {
        nextTick(() => {
            pageScrollTo({
                selector: '#footer'
            });
        });
    };
    const addLog = (log: LogInfo) => {
        const params = {
            ...log,
            time: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
        };
        console.log('log', params);
        feedbackData.current.logs.push(params);
    };
    const handleFeedback = () => {
        setShowMoreActionSheet(false);
        setFeedbackShow(true);
    };
    const submitFeedback = async (values: { description: string }) => {
        feedbackData.current.description = values.description;
        try {
            const res = await uploadLog(JSON.stringify(feedbackData.current));
            if (res.data.data) {
                feedbackData.current.logs = [];
                feedbackData.current.description = '';
                setFeedbackSuccessShow(true);
            }
        } catch (error) {
            Taro.showToast({
                title: '提交失败',
                icon: 'none'
            });
        }
    };

    const clearRecognizerTimer = () => {
        if (recognizerTimeRef.current) {
            clearInterval(recognizerTimeRef.current);
            recognizerTimeRef.current = undefined;
        }
    };

    async function doneActionSheet() {
        try {
            if (showVoicePageRef.current) {
                // 语音的情况
                finishVoice();
            }
            await doneChat(chatId, false);

            chatData.current.done = true;
            setChatActionSheetType(ChatActionSheetType.OFF);
            setShowActionSheet(true);
        } catch (error) {
            addLog({
                level: 'error',
                message: 'countdown end request doneChat error',
                data: {
                    error
                }
            });
        }
    }

    // const toggleSubtitle = () => {
    //     setSubtitleShow((prev) => {
    //         const s = prev === SubtitleVisible.Hide ? SubtitleVisible.Show : SubtitleVisible.Hide;
    //         Storage.set(StorageEnvKey.SUBTITLE_VISIBLE, s);
    //         return s;
    //     });
    // };

    const handleChatMode = useEvent(() => {
        const mode = showVideoClose === ChatMode.AUDIO ? ChatMode.SHUZIREN : ChatMode.AUDIO;
        setShowVideoClose(mode);
        Storage.set(StorageEnvKey.CHAT_MODE, mode);
        if (mode === ChatMode.SHUZIREN) {
            Dialog_.alert({
                title: '3D动态效果已开启',
                message: '下次练习时生效，为您带来更沉浸的互动体验',
                confirmButtonText: '我知道了',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    });

    const handleAiHelpClose = () => {
        setAiHelpShow(false);
    };
    function handleTimeOut() {
        addLog({
            level: 'debug',
            message: 'handleTimeOut',
            data: {
                status: statusRef.current,
                showVoicePage: showVoicePageRef.current,
                voiceInteraction: voiceInteractionRef.current
            }
        });
        setCanGenerate(false);
        clearQuestionTimer();
        if (showVoicePageRef.current) {
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Overtime) {
                    // 语音识别中，停止识别
                    stop();
                } else {
                    doneActionSheet();
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.Listening) {
                    // 语音识别中，停止识别
                    changeStatus(VoiceStatus.Waiting);
                    stop();
                    setSayPressed(false);
                } else if (statusRef.current === VoiceStatus.BeforeListen) {
                    changeStatus(VoiceStatus.Waiting);
                    setSayPressed(false);
                    doneActionSheet();
                } else {
                    doneActionSheet();
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.TapManual) {
                if (statusRef.current === VoiceStatus.BeforeListen) {
                    changeStatus(VoiceStatus.Waiting);
                    doneActionSheet();
                } else if (statusRef.current === VoiceStatus.Listening) {
                    changeStatus(VoiceStatus.WaitSend); // 切换到等待发送状态，等待识别文字和录音文件
                    stop();
                } else if (statusRef.current === VoiceStatus.AllowSend) {
                    // 点按模式
                    changeStatus(VoiceStatus.Waiting);
                    handleSend('audio');
                } else if (statusRef.current === VoiceStatus.WaitSend) {
                    // 在识别那边处理
                } else {
                    doneActionSheet();
                }
            }
        } else {
            doneActionSheet();
        }
    }

    const { cancel } = useRequest(() => getServerTime(), {
        pollingInterval: 30000, // 每隔30s更新一次当前时间，避免篡改手机本地时间导致倒计时错误
        onSuccess: async (data) => {
            serverTime.current = data;
            // console.log('getServerTime-createTime', createTime.current);
            if (createTime.current) {
                const leftTime =
                    dayjs(createTime.current).add(time.current, 'm').valueOf() - dayjs(serverTime.current).valueOf();
                // const leftTime = Math.floor(Math.random() * (60000 - 10000 + 1) + 10000);
                addLog({
                    level: 'debug',
                    message: 'getServerTime-leftTime',
                    data: {
                        createTime: createTime.current,
                        serverTime: serverTime.current,
                        leftTime
                    }
                });
                setLeftTime(leftTime);
                if (leftTime <= 0) {
                    cancel();
                    if (!isTimeout.current) {
                        isTimeout.current = true;
                        handleTimeOut();
                    }
                }
            }
        }
    });

    const [, formattedRes] = useCountDown({
        // 此处默认且只能使用当前系统时间，如何拿服务器时间去做纠正 => 使用leftTime代替targetDate
        leftTime,
        // targetDate,
        onEnd: async () => {
            addLog({
                level: 'trace',
                message: 'countdown end'
            });
            if (!isTimeout.current) {
                isTimeout.current = true;
                handleTimeOut();
            }
        }
    });

    const { minutes, seconds } = formattedRes;
    // console.log(minutes, seconds);
    useEffect(() => {
        if (isOver.current && leftMinutesRef.current === undefined && leftSecondsRef.current === undefined) {
            leftMinutesRef.current = minutes;
            leftSecondsRef.current = seconds;

            setLeftMinutes(minutes);
            setLeftSeconds(seconds);

            setLeftTime(undefined);
        }
        // console.log(minutes, seconds, isOver.current, leftMinutesRef.current, leftSecondsRef.current);
    }, [minutes, seconds]);
    useEffect(() => {
        const m = leftMinutes !== undefined ? leftMinutes : minutes;
        const s = leftSeconds !== undefined ? leftSeconds : seconds;
        setTimeLeftText(`${m >= 10 ? m : `0${m}`}:${s >= 10 ? s : `0${s}`}`);
    }, [leftMinutes, leftSeconds, minutes, seconds]);
    // 为实现从历史页进入对话页时候能滚动底部
    useEffect(() => {
        onScrollView();
    }, [history]);

    function handleNext() {
        const i = questionNowRef.current;
        if (isTimeout.current) return; // 倒计时结束就不继续下一题了
        if (i < questions.current.length - 1) {
            setCustomTitle({
                title: '对方正在输入...'
            });
            const nextIndex = i + 1;
            setQuestionNow(nextIndex);
            questionNowRef.current = nextIndex;

            setTimeout(async () => {
                await saveQuestion(nextIndex);
                nextQuestion(nextIndex);
            }, 500);
        } else {
            // 答题完了

            isOver.current = true;
            setQuestionLoading(true);
            cancel();

            if (showVoicePageRef.current) {
                // 语音的情况
                // stop();
                changeStatus(VoiceStatus.Loading);

                // finishVoice();
                if (questionTimeout.current) {
                    answerComplete(i);
                }
            } else {
                changeStatus(VoiceStatus.Complete);
                setChatActionSheetType(ChatActionSheetType.COMPLETE);
                setShowActionSheet(true);
            }
            addLog({
                level: 'trace',
                message: '答题完了'
            });
            // 这里结束，无法再保存对话
            /* try {
                await doneChat(chatId, false);
                chatData.current.done = true;
            } catch (error) {
                console.log(error);
            } */
        }

        questionTimeout.current = false;
    }

    async function handleSend(m: string, text?: string) {
        const i = questionNowRef.current;
        clearQuestionTimer();
        console.log('handleSend', i, text, userSentence.current);
        const answer = m === 'text' ? text : userSentence.current?.text;
        questions.current[i].answer = answer;
        setHistory((history) => {
            const newHistory = [
                ...history,
                {
                    role: RoleEnum.USER,
                    content: answer,
                    avatar: userInfo?.avatar,
                    qaId: questions.current[i].qaId
                }
            ];
            historyRef.current = newHistory;
            return newHistory;
        });
        formIt.resetFields();
        setCanSend(false);
        // console.log('send', message, new Date().getTime());
        try {
            const param: any = {
                qaId: questions.current[i].qaId,
                answer
            };
            const audioFileName = `${chatId}_${Date.now()}`;
            if (m === 'audio') {
                param['audioFileName'] = audioFileName;
            }

            const res = await qaCompletions(chatId, param);

            console.log('qaCompletions', res);

            const uploadUrl = res.header['upload-url'];
            const { mimeType } = res.header;
            const path = res.header['key'];
            const { chatQAId, chatHistoryId } = res.data.data;
            questions.current[i] = {
                ...questions.current[i],
                ossConfig: {
                    audioFileName,
                    path,
                    uploadUrl,
                    mimeType
                }
            };

            questions.current[i].chatQAId = chatQAId;
            questions.current[i].chatHistoryId = chatHistoryId;

            addLog({
                level: 'debug',
                message: 'qaCompletions',
                data: {
                    i,
                    path,
                    timeOut: isTimeout.current
                }
            });
            if (userSentence.current && m === 'audio') {
                userSentence.current.chatHistoryId = chatHistoryId;
                if (userSentence.current?.filePath) {
                    addLog({
                        level: 'trace',
                        message: '有录音，发送重新识别'
                    });
                    sentenceRecognize({ ...userSentence.current });
                }
            }
            userSentence.current = undefined;
            handleNext();
            onScrollView();
        } catch (error) {
            addLog({
                level: 'error',
                message: 'qaCompletions',
                data: {
                    error
                }
            });
        }
    }

    const onSend = () => {
        const message = formIt.getFieldValue('message');
        addLog({
            level: 'debug',
            message: 'onSend',
            data: {
                message
            }
        });
        if (message === null || message === undefined || message === '' || !message.trim()) {
            // Toast_.show未出现
            // console.log('请输入内容');
            // Toast_.show({
            //     message: '请输入内容'
            // });
            return;
        }
        handleSend('text', message);
    };

    const chatEnd = async () => {
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
        console.log(historyRef.current);
        if (
            recognizeStatusRef.current === RecognizeStatus.SentenceBegin ||
            recognizeStatusRef.current === RecognizeStatus.SentenceChange
        ) {
            stop();
        }
        const checkRewriteTimer = setInterval(async () => {
            const userAnswers = historyRef.current.filter(
                (item) => item.role === RoleEnum.USER && 'upload' in item && item.upload === false
            );

            console.log('userAnswers', userAnswers);
            if (userAnswers.length === 0) {
                clearInterval(checkRewriteTimer);
                console.info('结束，并生成报告');

                try {
                    const res = await doneChat(chatId, false);
                    Taro.hideLoading();
                    if (res.data.code === 200) {
                        chatData.current.done = true;
                        setShowActionSheet(false);
                        Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                        const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                        if (homePath) {
                            Taro.switchTab({ url: homePath });
                        } else {
                            Taro.redirectTo({ url: HomePath.MIDDLE });
                        }
                    }
                } catch (error) {
                    addLog({
                        level: 'error',
                        message: 'chatEnd doneChat',
                        data: {
                            error
                        }
                    });
                    Taro.hideLoading();
                    Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                    const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                    if (homePath) {
                        Taro.switchTab({ url: homePath });
                    } else {
                        Taro.redirectTo({ url: HomePath.MIDDLE });
                    }
                }
            }
        }, 100);
    };

    const openReport = async () => {
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
        addLog({
            level: 'trace',
            message: 'openReport'
        });
        console.log(historyRef.current);
        if (
            recognizeStatusRef.current === RecognizeStatus.SentenceBegin ||
            recognizeStatusRef.current === RecognizeStatus.SentenceChange
        ) {
            stop();
        }
        const checkRewriteTimer = setInterval(async () => {
            const userAnswers = historyRef.current.filter(
                (item) => item.role === RoleEnum.USER && 'upload' in item && item.upload === false
            );
            console.log('userAnswers', userAnswers);
            if (userAnswers.length === 0) {
                clearInterval(checkRewriteTimer);
                console.info('结束，并生成报告');
                try {
                    Taro.hideLoading();
                    if (chatData.current.done) {
                        const res = await generateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    } else {
                        const res = await doneGenerateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    }
                } catch (error) {
                    addLog({
                        level: 'error',
                        message: 'openReport doneChat',
                        data: {
                            error
                        }
                    });
                    console.log(error);
                    Taro.hideLoading();
                    if (chatData.current.done) {
                        const res = await generateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    } else {
                        const res = await doneGenerateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    }
                }
            }
        }, 100);
    };

    function goBack() {
        setChatActionSheetType(ChatActionSheetType.FINISH);
        setShowActionSheet(true);
    }

    Taro.useUnload(() => {
        feedbackData.current.description = '自动上传日志';
        Taro.eventCenter.trigger('uploadLogs', JSON.stringify(feedbackData.current));
        Storage.set(StorageEnvKey.REFRESH_HOME, 1);
    });

    const actionSheet = useMemo(() => {
        if (chatActionSheetType === ChatActionSheetType.FINISH) {
            return {
                title: '确认结束练习吗？',
                actions: [
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.COMPLETE) {
            return {
                title: '完成答题',
                actions: [
                    {
                        text: '生成报告',

                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.INTERRUPT) {
            return {
                title: '练习中断，是否继续？',
                actions: [
                    {
                        text: '继续对话',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: () => {
                                setShowActionSheet(false);
                                // 打断后，语音重新开始
                                initQuestion();
                            }
                        }
                    },
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: '#F6F6F6',
                            style: { color: '#777777' },
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
        if (chatActionSheetType === ChatActionSheetType.OFF) {
            return {
                title: '练习时间结束，对话已关闭',
                actions: [
                    {
                        text: '生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
    }, [chatActionSheetType]);

    function clearQuestionTimer() {
        console.log('clearQuestionTimer');
        if (questionTimer.current) {
            clearInterval(questionTimer.current);
            questionTimer.current = null;
            setQuestionTime(undefined);
        }
    }

    async function nextQuestion(qNow: number) {
        const nextQuestion = questions.current[qNow];
        addLog({
            level: 'trace',
            message: 'nextQuestion',
            data: {
                qNow,
                nextQuestion,
                showVoicePage: showVoicePageRef.current
            }
        });
        initQuestionTimer(qNow);
        if (showVoicePageRef.current) {
            speakStart();
            speakProcess(nextQuestion.question); // 传给语音合成
        }

        setShowScriptBtn(false);
        setQuestionLoading(true);
        if (showVoicePageRef.current) {
            setHistory((history) => {
                const newHistory = [...history, { role: RoleEnum.ASSISTANT, content: nextQuestion.question }];
                historyRef.current = newHistory;
                return newHistory;
            });
            setAssistantMessage(undefined);
            setShowScriptBtn(true);
            setQuestionLoading(false);
        } else {
            setAssistantMessage(nextQuestion.question);
            const timeNeed = nextQuestion.question.split('').length * 50;
            const interval = setInterval(() => {
                onScrollView();
            }, 50 * 10); // 50ms*10个字一行
            // todo useInterval useTimeout
            setTimeout(() => {
                clearInterval(interval);
                setAssistantMessage((assistantMessage) => {
                    setHistory((history) => {
                        const newHistory = [
                            ...history,
                            { role: RoleEnum.ASSISTANT, content: assistantMessage as string }
                        ];
                        historyRef.current = newHistory;
                        return newHistory;
                    });
                    return undefined;
                });
            }, timeNeed + 1000);
            setTimeout(() => {
                setShowScriptBtn(true);
                setQuestionLoading(false);
            }, timeNeed + 1000);
        }

        setCustomTitle({
            title: data?.eman?.name ?? ''
        });
    }

    async function saveQuestion(qNow: number) {
        const nextQ = questions.current[qNow];
        addLog({
            level: 'debug',
            message: 'saveQuestion',
            data: {
                qNow,
                answer: questions.current[qNow].answer,
                nextQ
            }
        });
        console.log('saveQuestion', qNow, questions.current[qNow].answer, nextQ);
        if (!questions.current[qNow].answer) {
            // 如果没有回答，则保存问题
            try {
                await qaSave(chatId, nextQ.qaId);
            } catch (error) {
                console.log('qaSave', error);
            }
        }
    }

    const handleShowVoicePage = () => {
        setShowVoicePage(true);
        showVoicePageRef.current = true;
        addLog({
            level: 'trace',
            message: 'handleVoicePage'
        });
    };

    const handleVoiceLogic = () => {
        if (!isOver.current && !isTimeout.current) {
            console.log('onVoiceClick', questions.current[questionNowRef.current].answer);
            setSubtitleText(questions.current[questionNowRef.current].question);
            handleShowVoicePage();
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                // changeStatus(VoiceStatus.Listening);
                start();
            } else if (
                voiceInteractionRef.current === VoiceInteraction.Manual ||
                voiceInteractionRef.current === VoiceInteraction.TapManual
            ) {
                changeStatus(VoiceStatus.WaitListen);
            }
        } else {
            handleShowVoicePage();
        }
    };

    const onVoiceClick = useCallback(() => {
        addLog({ level: 'trace', message: 'onVoiceClick' });
        if (data?.eman.zipFileUrl && data?.eman.show3dFlag) {
            if (
                isHarmonyRef.current ||
                Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null
            ) {
                const url = `/pages/shuziren/index?chatId=${data?.id}&from=question`;
                Taro.redirectTo({ url });
            } else {
                handleVoiceLogic();
            }
        } else {
            if (isHarmonyRef.current) {
                setShowHarmonyDialog(true);
            } else {
                handleVoiceLogic();
            }
        }
    }, [data, minutes, seconds]);

    /* const handleErrorDialog = (msg: string) => {
        Dialog_.confirm({
            message: (
                <Block>
                    <Image style={{ width: pxTransform(240), height: pxTransform(240) }} src={errorImg} />
                    <View
                        style={{
                            fontSize: pxTransform(32),
                            textAlign: 'center',
                            marginTop: pxTransform(56),
                            marginBottom: pxTransform(56)
                        }}
                    >
                        {msg}
                    </View>
                </Block>
            ),
            confirmButtonText: '刷新',
            confirmButtonColor: '#4F66FF',
            onConfirm() {
                Taro.reLaunch({
                    url: '/pages/home/<USER>'
                });
            }
        });
    };*/

    const showMore = () => {
        setShowMoreActionSheet(true);
    };

    const showActionSheetVoiceSetting = () => {
        setShowMoreActionSheet(false);
        setShowVoiceActionSheet(true);
    };

    const voicePopupConfirm = () => {
        Dialog_.alert({
            title: '修改成功',
            message: '下次练习时生效',
            confirmButtonText: '关闭',
            confirmButtonColor: '#4F66FF'
        }).then(() => {});
    };
    async function answerComplete(i: number) {
        if (isTimeout.current) {
            doneActionSheet();
        } else {
            if (i === questions.current.length - 1) {
                addLog({
                    level: 'trace',
                    message: 'answerComplete 回答结束'
                });
                isOver.current = true;
                isFinish.current = true;
                console.log('questionNow', questionNowRef.current);
                finishComplete();
                try {
                    await doneChat(chatId, false);
                    chatData.current.done = true;
                } catch (error) {
                    addLog({
                        level: 'error',
                        message: 'answerComplete doneChat',
                        data: {
                            error
                        }
                    });
                }
            }
        }
    }
    const handleSentenceRecognize = async (path: string, i: number, text: string) => {
        addLog({
            level: 'debug',
            message: 'handleSentenceRewrite',
            data: {
                path,
                i
            }
        });
        if (i === null || i === undefined) return;
        if (questions.current[i].chatQAId && questions.current[i].chatHistoryId) {
            console.log('history', historyRef.current);

            try {
                const res = await qaAnswerRewrite({
                    chatQAId: questions.current[i].chatQAId,
                    chatHistoryId: questions.current[i].chatHistoryId,
                    key: path
                });
                historyRef.current.forEach((item) => {
                    if (item.qaId && item.qaId === questions.current[i].qaId) {
                        if (res.data.data) {
                            item.content = res.data.data;
                        }
                    }
                });
                setHistory(historyRef.current);
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'qaAnswerRewrite',
                    data: {
                        error
                    }
                });
            }
        }
    };

    const showCompleteAnswer = () => {
        setChatActionSheetType(ChatActionSheetType.COMPLETE);
        setShowActionSheet(true);
    };

    const splitSentence = (text: string) => {
        if (text.length <= speechLength) {
            aiSentence.current.push(text);
        } else {
            // 每speechLength长度的文本push到aiSentence.current
            const sentenceSplit = text.split(/[?!。？！]/g);
            for (let i = 0; i < sentenceSplit.length; i++) {
                const sp = sentenceSplit[i];
                if (sp.length <= speechLength) {
                    if (sp !== '') {
                        aiSentence.current.push(sp);
                    }
                } else {
                    for (let j = 0; j < sp.length; j += speechLength) {
                        aiSentence.current.push(sp.substring(j, j + speechLength));
                    }
                }
            }
        }
    };

    function changeStatus(status: VoiceStatus) {
        addLog({
            level: 'debug',
            message: 'changeStatus',
            data: {
                status
            }
        });
        statusRef.current = status;
        setStatus(status);
        switch (status) {
            case VoiceStatus.Start:
                setStatusText(['准备好了吗，开始答题']);
                setStatusLoading(<VoiceLoading type='dot' />);
                break;
            case VoiceStatus.Listening:
                setStatusText(['我在听，你说']);
                setStatusLoading(<VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />);

                break;
            case VoiceStatus.WaitListen:
            case VoiceStatus.WaitSend:
            case VoiceStatus.AllowSend:
                setStatusText(['']);
                setStatusLoading(<VoiceLoading type='barStatic' />);
                break;
            case VoiceStatus.Waiting:
                setStatusText(['稍等，让我想想']);
                setStatusLoading(<VoiceLoading type='dot' />);
                break;
            case VoiceStatus.Speaking:
                setStatusText(['点击可打断']);
                setStatusLoading(<VoiceLoading type='shortBar' />);
                break;
            case VoiceStatus.Overtime:
                setStatusText(['我好像没听清', '请用普通话大声一点']);
                setStatusLoading(null);
                break;
            case VoiceStatus.Standby:
                setStatusText(['点击屏幕重试']);
                setStatusLoading(<VoiceLoading type='repeat' />);
                break;
            case VoiceStatus.Cancel:
                setStatusText(['松手取消发送']);
                setStatusLoading(<Image src={IconCancel} className={styles.cancel_icon} />);
                break;
            case VoiceStatus.Complete:
                setStatusText(['已完成全部答题']);
                setStatusLoading(<Image src={IconComplete} className={styles.complete_icon} />);
                break;
            default:
                setStatusText(['']);
                setStatusLoading(null);
                break;
        }
    }
    const recognizeStartTimer = useRef<NodeJS.Timeout | null>(null);
    function start() {
        addLog({
            level: 'trace',
            message: 'start 启动识别'
        });
        recognizeStartTimer.current = setTimeout(() => {
            speechRecognizerManager.current?.start();
        }, 500);
    }

    function stopAISpeaking() {
        addLog({
            level: 'trace',
            message: 'stopAISpeaking'
        });

        if (intervalSpeech.current) {
            clearInterval(intervalSpeech.current);
            intervalSpeech.current = null;
        }
        if (intervalText.current) {
            clearInterval(intervalText.current);
            intervalText.current = null;
        }
        // setSubtitleText('');
        aiSentence.current = [];
        aiVoiceList.current = [];
        audio.stop();
        aiVoiceNow.current = null;
    }

    function speakProcess(text: string) {
        console.log('ai回复中', text, isFinish.current);
        if (isFinish.current) return;

        // 优化语音断句问题
        splitSentence(text);
    }

    function finishComplete() {
        changeStatus(VoiceStatus.Complete);
        stop();
        stopAISpeaking();
    }

    const statusTextClick = () => {
        addLog({
            level: 'trace',
            message: 'statusTextClick',
            data: {
                voiceInteraction: voiceInteractionRef.current,
                status: statusRef.current
            }
        });
        // 打断

        // if (statusRef.current === VoiceStatus.Speaking) {
        //     stopAISpeaking();
        // }
        if (voiceInteractionRef.current === VoiceInteraction.Auto) {
            // 待机重试
            if (statusRef.current === VoiceStatus.Speaking) {
                stopAISpeaking();
                start();
            }
            if (statusRef.current === VoiceStatus.Standby) {
                start();
            }
        } else if (
            voiceInteractionRef.current === VoiceInteraction.Manual ||
            voiceInteractionRef.current === VoiceInteraction.TapManual
        ) {
            // 按住说话
            if (statusRef.current === VoiceStatus.Standby) {
                start();
            }
        }
    };
    const finish = () => {
        stop();
        stopAISpeaking();
        if (voiceInteractionRef.current === VoiceInteraction.Auto) {
            if (!isOver.current) {
                changeStatus(VoiceStatus.Stop);
            }
        } else if (
            voiceInteractionRef.current === VoiceInteraction.Manual ||
            voiceInteractionRef.current === VoiceInteraction.TapManual
        ) {
            if (!isOver.current) {
                changeStatus(VoiceStatus.WaitListen);
            }
        }
        // userSentence.current = '';
    };

    const handleClose = () => {
        addLog({
            level: 'trace',
            message: 'handleClose',
            data: {
                status: statusRef.current
            }
        });
        if (
            statusRef.current === VoiceStatus.BeforeListen ||
            statusRef.current === VoiceStatus.Listening ||
            statusRef.current === VoiceStatus.Overtime ||
            statusRef.current === VoiceStatus.WaitSend ||
            statusRef.current === VoiceStatus.AllowSend ||
            statusRef.current === VoiceStatus.Waiting
        ) {
            Taro.showToast({
                title: '不支持录音时切换',
                icon: 'none'
            });
        } else {
            finish();
            setShowVoicePage(false);
            showVoicePageRef.current = false;
            onScrollView();
        }
    };

    function finishVoice() {
        addLog({
            level: 'trace',
            message: 'finishVoice',
            data: {
                questionNow: questionNowRef.current
            }
        });
        isFinish.current = true;
        if (
            questionNowRef.current === questions.current.length - 1 &&
            questions.current[questionNowRef.current].answer
        ) {
            finishComplete();
        } else {
            finish();
        }
    }
    function stop() {
        addLog({
            level: 'trace',
            message: 'stop 停止识别'
        });
        clearRecognizerTimer();
        if (timerOvertime.current) {
            clearTimeout(timerOvertime.current);
            timerOvertime.current = null;
        }
        if (timerStandby.current) {
            clearTimeout(timerStandby.current);
            timerStandby.current = null;
        }
        speechRecognizerManager.current?.stop();
    }
    const handleFinish = () => {
        // stop();
        goBack();
    };

    const { run: handleTouchStart } = useThrottleFn(
        () => {
            addLog({
                level: 'debug',
                message: 'handleTouchStart',
                data: {
                    status: statusRef.current
                }
            });
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            if (statusRef.current === VoiceStatus.WaitListen || status === VoiceStatus.Speaking) {
                stopAISpeaking();
                setSayPressed(true);
                Taro.vibrateShort({ type: 'heavy' });
                changeStatus(VoiceStatus.BeforeListen);
                start();
            }
        },
        {
            wait: 1500,
            leading: true,
            trailing: false
        }
    );

    const handleTouchEnd = () => {
        addLog({
            level: 'debug',
            message: 'handleTouchEnd',
            data: {
                status: statusRef.current
            }
        });
        if (statusRef.current === VoiceStatus.Listening) {
            changeStatus(VoiceStatus.Waiting);

            stop();
            setSayPressed(false);
        } else if (statusRef.current === VoiceStatus.Cancel) {
            changeStatus(VoiceStatus.WaitListen);
            setSayPressed(false);
            stop();
        } else if (statusRef.current === VoiceStatus.BeforeListen) {
            changeStatus(VoiceStatus.WaitListen);
            setSayPressed(false);
            if (recognizeStartTimer.current) {
                clearTimeout(recognizeStartTimer.current);
            }
        } else if (statusRef.current === VoiceStatus.WaitListen) {
            setSayPressed(false);
        }
    };

    const { run: handleTouchMove } = useThrottleFn(
        (e: any) => {
            console.log('handleTouchMove', getVoiceStatusKey(statusRef.current), e.touches, e.changedTouches);
            if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Cancel) {
                if (touchTop.current) {
                    if (e.changedTouches[0].clientY < touchTop.current) {
                        // 超出区域
                        changeStatus(VoiceStatus.Cancel);
                    } else {
                        changeStatus(VoiceStatus.Listening);
                    }
                } else {
                    createSelectorQuery()
                        .select('#actionfooter')
                        .boundingClientRect((res: NodesRef.BoundingClientRectCallbackResult) => {
                            console.log('handleTouchMove', e.changedTouches, e.touches, res);
                            touchTop.current = res.top;
                            if (e.changedTouches[0].clientY < res.top) {
                                // 超出区域
                                changeStatus(VoiceStatus.Cancel);
                            } else {
                                changeStatus(VoiceStatus.Listening);
                            }
                        })
                        .exec();
                }
            }
        },
        {
            wait: 100
        }
    );

    const { run: handleTapSay } = useThrottleFn(
        () => {
            console.log(statusRef.current, 'statusRef.currentstatusRef.current');
            addLog({
                level: 'trace',
                message: 'handleTapSay',
                data: {
                    status: statusRef.current
                }
            });
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            if (statusRef.current === VoiceStatus.Speaking) {
                stopAISpeaking();
                Taro.vibrateShort({ type: 'heavy' });
                changeStatus(VoiceStatus.BeforeListen);
                start();
            }
            if (statusRef.current === VoiceStatus.WaitListen) {
                Taro.vibrateShort({ type: 'heavy' });
                changeStatus(VoiceStatus.BeforeListen);
                start();
            } else if (statusRef.current === VoiceStatus.BeforeListen) {
                changeStatus(VoiceStatus.WaitListen);
            } else if (statusRef.current === VoiceStatus.Listening) {
                changeStatus(VoiceStatus.WaitSend); // 切换到等待发送状态，等待识别文字和录音文件
                stop();
            }
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    const { run: handleTapSend } = useThrottleFn(
        () => {
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            if (statusRef.current === VoiceStatus.AllowSend) {
                addLog({
                    level: 'trace',
                    message: 'handleTapSend',
                    data: {
                        status: statusRef.current
                    }
                });
                handleAiHelpClose();
                changeStatus(VoiceStatus.Waiting);
                handleSend('audio');
            }
        },
        {
            wait: 500,
            leading: false,
            trailing: true
        }
    );

    const { run: handleTapDrop } = useThrottleFn(
        () => {
            setRecordDialogShow(true);
        },
        {
            wait: 500,
            leading: false,
            trailing: true
        }
    );

    // 丢掉当前识别
    const { run: handleDrop } = useThrottleFn(
        () => {
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            addLog({
                level: 'debug',
                message: 'handleDrop',
                data: {
                    status: statusRef.current
                }
            });
            if (statusRef.current === VoiceStatus.AllowSend) {
                userSentence.current = undefined;
                changeStatus(VoiceStatus.WaitListen);
            }
            setRecordDialogShow(false);
        },
        {
            wait: 500,
            leading: false,
            trailing: true
        }
    );

    const handleComplete = () => {
        addLog({
            level: 'trace',
            message: 'handleComplete',
            data: {
                status: statusRef.current
            }
        });
        if (statusRef.current === VoiceStatus.Complete) {
            console.log('handleComplete');
            showCompleteAnswer();
        }
    };

    async function sentenceRecognize({
        i,
        text,
        filePath,
        fileSize
    }: {
        i: number;
        text: string;
        filePath: string;
        fileSize: number;
    }) {
        addLog({
            level: 'debug',
            message: 'sentenceRecognize',
            data: {
                i,
                text,
                filePath,
                fileSize,
                questions: questions.current[i]
            }
        });
        // changeStatus(VoiceStatus.Loading);
        if (text) {
            cosUpload(questions.current[i].ossConfig, filePath)
                .then(async (data: any) => {
                    // 上传成功
                    console.info('cosUpload Success', data);
                    addLog({
                        level: 'trace',
                        message: 'cosUpload Success'
                    });
                    try {
                        await audioUploadComplete(questions.current[i].ossConfig.path);
                        historyRef.current.forEach((item) => {
                            if (item.qaId && item.qaId === questions.current[i].qaId) {
                                item['upload'] = true;
                            }
                        });
                        handleSentenceRecognize(questions.current[i].ossConfig.path, i, text);
                        answerComplete(i);
                        /* getFileSystemManager().readFile({
                            filePath,
                            success: (fileRes: any) => {
                                console.log('fileRes', fileSize);
                                audioSpeechRecognizerFlash({
                                    appid: config.QCloudAIVoice.appId,
                                    secretid: config.QCloudAIVoice.secretId,
                                    secretkey: config.QCloudAIVoice.secretKey,
                                    data: fileRes.data,
                                    dataLen: fileSize,
                                    voice_format: 'pcm',
                                    engine_type: '16k_zh_large', // 引擎类型
                                    hotword_id: config.QCloudAIVoice.hotword_id,
                                    customization_id: config.QCloudAIVoice.customization_id,
                                    success: (data: { request_id: string; result: string }) => {
                                        console.log('sentenceRecognition succ:', data.result);

                                        handleSentenceRecognize(data.result, i);
                                    },
                                    fail: async (err: any) => {
                                        console.log('sentenceRecognition fail:', err);

                                        answerComplete(i);
                                    }
                                });
                            },
                            fail: async () => {
                                answerComplete(i);
                            }
                        }); */
                    } catch (error) {
                        answerComplete(i);
                        addLog({
                            level: 'error',
                            message: 'audioUploadComplete',
                            data: {
                                error
                            }
                        });
                    }
                })
                .catch((error: any) => {
                    // 上传失败
                    answerComplete(i);
                    addLog({
                        level: 'error',
                        message: 'cosUpload',
                        data: {
                            error
                        }
                    });
                });
        } else {
            answerComplete(i);
            addLog({
                level: 'warn',
                message: 'cosUpload no text'
            });
        }
    }
    const ttsErrorTimer = useRef<NodeJS.Timeout | null>(null);
    const ttsErrorNext = (id: number) => {
        const fi = aiVoiceList.current.findIndex((item) => item.id === id);
        if (fi !== -1) {
            if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                aiVoiceNow.current.status = SpeechStatus.PlayEnd;
            }

            aiVoiceList.current.splice(fi, 1);
        }
        if (ttsErrorTimer.current) {
            clearTimeout(ttsErrorTimer.current);
        }
        if (voiceInteractionRef.current === VoiceInteraction.Auto) {
            ttsErrorTimer.current = setTimeout(() => {
                if (aiVoiceList.current.length === 0 && aiVoiceNow.current?.status === SpeechStatus.PlayEnd) {
                    // ai回复完毕，继续聆听
                    addLog({
                        level: 'trace',
                        message: 'tts错误，ai语音播报完毕,继续聆听'
                    });
                    stopAISpeaking();
                    if (
                        voiceInteractionRef.current === VoiceInteraction.Manual ||
                        voiceInteractionRef.current === VoiceInteraction.TapManual
                    ) {
                        changeStatus(VoiceStatus.WaitListen);
                    } else if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                        if (statusRef.current !== VoiceStatus.Listening) {
                            start();
                        }
                    }
                }
            }, 5000);
        }
    };

    // 文本转语音;
    function textToSpeech(text: string) {
        // console.log(`开始ai文本转语音:${text}`, new Date().getTime());
        const id = new Date().getTime();
        aiVoiceList.current.push({
            id,
            text,
            url: ''
        });
        addLog({
            level: 'debug',
            message: 'textToSpeech',
            data: {
                id,
                text
            }
        });
        // console.log(questionNowRef.current);
        ttsVoice({
            name: tone.current.name,
            type: data?.eman.gender,
            text,
            mainId:
                questions.current[questionNowRef.current].question.length < speechLength
                    ? questions.current[questionNowRef.current].qaId
                    : null,
            encoding: 'mp3'
        })
            .then((res) => {
                const audioPath = `${Taro.env.USER_DATA_PATH}/chat/voice_${id}.mp3`;
                console.log(res);
                if (res.data.data) {
                    const fs = Taro.getFileSystemManager();
                    const dir = `${Taro.env.USER_DATA_PATH}/chat`;
                    try {
                        fs.accessSync(dir);
                    } catch (e) {
                        addLog({
                            level: 'error',
                            message: `no ${dir}`,
                            data: {
                                id,
                                text
                            }
                        });

                        try {
                            fs.mkdirSync(dir, false);
                        } catch (error) {
                            setSubtitleText(text);

                            addLog({
                                level: 'error',
                                message: `mkdir error ${dir}`,
                                data: {
                                    id,
                                    text
                                }
                            });
                        }
                    }
                    fs.writeFile({
                        filePath: audioPath,
                        data: res.data.data,
                        encoding: 'base64',
                        success: (e: any) => {
                            if (ttsErrorTimer.current) {
                                clearTimeout(ttsErrorTimer.current);
                            }
                            console.log('ai语音文件保存成功', e);
                            aiVoiceList.current.forEach((item) => {
                                if (item.id === id) {
                                    item.url = audioPath;
                                }
                            });
                            // console.log(aiVoiceList.current);
                        },
                        fail(error) {
                            setSubtitleText(text);
                            ttsErrorNext(id);
                            addLog({
                                level: 'error',
                                message: 'tts ai语音文件保存失败',
                                data: {
                                    id,
                                    text,
                                    error
                                }
                            });
                        }
                    });
                } else {
                    setSubtitleText(text);
                    ttsErrorNext(id);
                    Taro.showToast({
                        title: '语音生成错误',
                        icon: 'none'
                    });
                    addLog({
                        level: 'warn',
                        message: 'tts no data',
                        data: {
                            id,
                            text
                        }
                    });
                }
            })
            .catch((error) => {
                setSubtitleText(text);
                ttsErrorNext(id);
                ttsErrorHandler(error);
                addLog({
                    level: 'error',
                    message: 'tts error',
                    data: {
                        id,
                        text,
                        error
                    }
                });
            });
        /*   plugin.textToSpeech({
            content: text, // 中文最大支持150个汉字（全角标点符号算一个汉字）；英文最大支持500个字母（半角标点符号算一个字母）
            ...tone.current,
            emotionCategory: 'neutral',
            projectId: 0,
            sampleRate: 16000, // 音频采样率：16000：16k（默认），8000：8k
            success(data: any) {
                const url = data.result.filePath;
                if (url && url.length > 0) {
                    // console.log('语音', text, url, new Date().getTime());
                    aiVoiceList.current.forEach((item) => {
                        if (item.id === id) {
                            item.url = url;
                        }
                    });
                } else {
                    const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                    if (fi !== -1) {
                        if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                            aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                        }
                        aiVoiceList.current.splice(fi, 1);
                    }

                    console.log('没有语音', fi);
                }
            },
            fail(error: any) {
                const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                if (fi !== -1) {
                    if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                        aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                    }

                    aiVoiceList.current.splice(fi, 1);
                }
                console.log('textToSpeech', error);
            }
        }); */
    }

    const handleVoicePlay = () => {
        // console.log('aiVoiceList', aiVoiceList.current);
        // console.log('aiVoiceNow', aiVoiceNow.current);
        if (aiVoiceList.current && aiVoiceList.current.length > 0) {
            // 还有未播放的语音，接下去播放第一条
            const [currentSpeech] = aiVoiceList.current;
            if (!aiVoiceNow.current || aiVoiceNow.current.status === SpeechStatus.PlayEnd) {
                aiVoiceNow.current = { ...currentSpeech, status: SpeechStatus.PlayStart };
            }

            if (aiVoiceNow.current.status === SpeechStatus.PlayStart && currentSpeech.url) {
                // console.log(`开始播放语音`, aiVoiceNow.current,  new Date().getTime());
                // clearAudioDurationTimer();
                aiVoiceNow.current.status = SpeechStatus.Playing;
                aiVoiceNow.current.url = currentSpeech.url;
                setSubtitleText(aiVoiceNow.current?.text);
                const fs = getFileSystemManager();
                try {
                    fs.accessSync(aiVoiceNow.current.url);
                    audio.src = aiVoiceNow.current.url;
                    // audio.seek(0.1);
                    audio.onPlay(() => {
                        addLog({
                            level: 'debug',
                            message: '语音onPlay',
                            data: {
                                id: aiVoiceNow.current?.id,
                                text: aiVoiceNow.current?.text,
                                status: aiVoiceNow.current?.status
                            }
                        });
                    });
                    // 为了解决播放不执行onEnded的问题
                    /* audio.onTimeUpdate(() => {
                        console.log('duration', audio.duration);
                        // 解决音频没有播放结束的问题
                        if (
                            (audio.duration > 0 && audioDurationTimer.current === undefined) ||
                            audioDurationTimer.current === null
                        ) {
                            addLog({
                                level: 'debug',
                                message: 'audioDurationTimer',
                                data: {
                                    id: aiVoiceNow.current?.id,
                                    text: aiVoiceNow.current?.text,
                                    status: aiVoiceNow.current?.status,
                                    duration: audio.duration
                                }
                            });
                            audioDurationTimer.current = setTimeout(() => {
                                clearAudioDurationTimer();
                                addLog({
                                    level: 'debug',
                                    message: '主动播放onEnded',
                                    data: {
                                        ...aiVoiceNow.current,
                                        aiVoiceLength: aiVoiceList.current.length
                                    }
                                });
                                audio.stop();
                                audio.offPlay();
                                audio.offEnded();
                                audio.offError();
                                if (aiVoiceNow.current) {
                                    if (currentSpeech.id === aiVoiceNow.current.id) {
                                        aiVoiceList.current.shift();
                                        aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                                        console.log('剩余语音列表', aiVoiceList.current);
                                    }
                                }
                            }, audio.duration * 1000 + 1000);
                        }
                    });
 */
                    audio.onEnded(() => {
                        // 播放完，删除第一条

                        // clearAudioDurationTimer();
                        addLog({
                            level: 'debug',
                            message: '播放onEnded',
                            data: {
                                id: aiVoiceNow.current?.id,
                                text: aiVoiceNow.current?.text,
                                status: aiVoiceNow.current?.status,
                                aiVoiceLength: aiVoiceList.current.length
                            }
                        });
                        audio.offPlay();
                        audio.offEnded();
                        audio.offError();
                        if (aiVoiceNow.current) {
                            if (currentSpeech.id === aiVoiceNow.current.id) {
                                try {
                                    const res = getFileSystemManager().unlinkSync(aiVoiceNow.current.url);
                                    console.log('删除音频文件成功', res);
                                } catch (e) {
                                    addLog({
                                        level: 'error',
                                        message: '删除音频文件失败',
                                        data: {
                                            error: e,
                                            aiVoiceNow: aiVoiceNow.current
                                        }
                                    });
                                }
                                aiVoiceList.current.shift();
                                aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                                console.log('剩余语音列表', aiVoiceList.current);
                            }
                        }
                    });

                    audio.onError((res) => {
                        // clearAudioDurationTimer();
                        setSubtitleText('');
                        audio.offPlay();
                        audio.offEnded();
                        audio.offError();
                        if (aiVoiceNow.current) {
                            if (currentSpeech.id === aiVoiceNow.current.id) {
                                aiVoiceList.current.shift();
                                aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                            }
                        }
                        Taro.showToast({
                            title: '语音播放失败',
                            icon: 'none'
                        });
                        addLog({
                            level: 'error',
                            message: 'audio error',
                            data: {
                                error: res
                            }
                        });
                    });
                    addLog({
                        level: 'debug',
                        message: '开始播放语音',
                        data: {
                            id: aiVoiceNow.current.id,
                            text: aiVoiceNow.current.text,
                            status: aiVoiceNow.current.status
                        }
                    });

                    changeStatus(VoiceStatus.Speaking);
                    audio.play();
                } catch (e) {
                    // clearAudioDurationTimer();
                    setSubtitleText('');
                    audio.offPlay();
                    audio.offEnded();
                    audio.offError();
                    if (aiVoiceNow.current) {
                        if (currentSpeech.id === aiVoiceNow.current.id) {
                            aiVoiceList.current.shift();
                            aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                        }
                    }
                    Taro.showToast({
                        title: '语音播放失败',
                        icon: 'none'
                    });
                    addLog({
                        level: 'error',
                        message: 'audio error',
                        data: {
                            error: e
                        }
                    });
                }
            }
        }
        if (aiVoiceList.current.length === 0 && aiVoiceNow.current?.status === SpeechStatus.PlayEnd) {
            // ai回复完毕，继续聆听
            addLog({
                level: 'trace',
                message: 'ai语音播报完毕,继续聆听'
            });
            stopAISpeaking();
            if (
                voiceInteractionRef.current === VoiceInteraction.Manual ||
                voiceInteractionRef.current === VoiceInteraction.TapManual
            ) {
                changeStatus(VoiceStatus.WaitListen);
            } else if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current !== VoiceStatus.Listening) {
                    start();
                }
            }
        }
    };

    function speakStart() {
        if (isFinish.current) return;
        addLog({
            level: 'trace',
            message: 'ai开始回复'
        });

        if (intervalSpeech.current) {
            clearInterval(intervalSpeech.current);
        }

        if (intervalText.current) {
            clearInterval(intervalText.current);
        }

        intervalText.current = setInterval(() => {
            if (aiSentence.current && aiSentence.current.length > 0) {
                const nextText = aiSentence.current.shift();
                console.log('nextText', nextText);
                console.log('剩余文本', aiSentence.current);
                if (nextText !== undefined && nextText !== '') {
                    textToSpeech(nextText);
                }
            }
        }, 30);

        // 定时检查ai回复的语音列表
        intervalSpeech.current = setInterval(() => {
            handleVoicePlay();
        }, 30);
    }

    function validateBuffer(buffer: any) {
        const { byteLength } = buffer;

        // 确保长度是2的倍数（每个样本占2字节）
        if (byteLength % 2 !== 0) {
            buffer = buffer.slice(0, byteLength - 1);
        }

        return new Int16Array(buffer);
    }
    // 音量计算函数
    function calculateDecibel(buffer: any) {
        // 转换为16位整型数组（微信小程序默认PCM格式）
        const int16Array = validateBuffer(buffer);

        // 计算均方根值（RMS）
        let sum = 0;
        for (const sample of int16Array) {
            sum += sample * sample;
        }
        const rms = Math.sqrt(sum / int16Array.length);

        // 转换为分贝值（参考公式：dB = 20 * log10(rms / 32768))
        const decibel = 20 * Math.log10(rms / 32768);

        // 标准化到0-100区间（静音≈30dB，正常说话≈60dB）
        const minDb = -90; // 最小分贝值
        const maxDb = 0; // 最大分贝值
        return Math.max(0, Math.min(100, ((decibel - minDb) / (maxDb - minDb)) * 100));
    }

    const initTimerNoSentense = () => {
        if (timerNoSentence.current) {
            clearTimeout(timerNoSentence.current);
        }
        timerNoSentence.current = setTimeout(() => {
            if ((!userSentence.current || !userSentence.current.text) && statusRef.current === VoiceStatus.Waiting) {
                // 读取文件并转换成base64
                const fs = getFileSystemManager();
                fs.readFile({
                    filePath: res.tempFilePath,
                    encoding: 'base64',
                    success: (fileRes: any) => {
                        addLog({
                            level: 'error',
                            message: '未识别的音频',
                            data: {
                                fileRes
                            }
                        });
                    }
                });
                userSentence.current = undefined;
                const systemInfo = Taro.getSystemInfoSync();
                const { platform } = systemInfo;
                if (platform === 'ios') {
                    Taro.showToast({
                        icon: 'none',
                        title: '未识别到文字或麦克风被占用'
                    });
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '未识别到文字'
                    });
                }
                addLog({
                    level: 'warn',
                    message: 'RecorderStop 手动模式，sdk没有返回识别到的文字，切换到等待聆听状态'
                });
                if (isTimeout.current) {
                    doneActionSheet();
                } else {
                    changeStatus(VoiceStatus.WaitListen);
                    questionTimeoutNext();
                }
            }
        }, 6000);
    };

    function initVoice() {
        // 请在页面onLoad时初始化好下列函数并确保腾讯云账号信息已经设置
        const voice_interaction_type = Storage.get(StorageEnvKey.QUESTION_INTERACTION_TYPE) || VoiceInteraction.Manual;
        voiceInteractionRef.current = voice_interaction_type;
        setVoiceInteraction(voice_interaction_type);
        addLog({
            level: 'debug',
            message: '语音交互类型',
            data: {
                voice_interaction_type
            }
        });
        const voiceParams = {
            // 用户参数
            secretkey: config.QCloudAIVoice.secretKey,
            secretid: config.QCloudAIVoice.secretId,
            appid: config.QCloudAIVoice.appId, // 腾讯云账号appid（非微信appid）
            engine_model_type: '16k_zh_medical',
            voice_format: 1, // 音频格式，1：pcm，8：mp3，默认为1,
            needvad: 1, // 0：关闭 vad，1：开启 vad，默认为0。如果语音分片长度超过60秒，用户需开启 vad（人声检测切分功能）
            vad_silence_time: slienceTime, // 语音断句检测阈值，静音时长超过该阈值会被认为断句（多用在智能客服场景，需配合 needvad = 1 使用），取值范围：240-2000（默认1000），单位 ms
            noise_threshold: 0.2, // 噪音参数阈值，默认为0，取值范围：[-1,1]，对于一些音频片段，取值越大，判定为噪音情况越大。取值越小，判定为人声情况越大。慎用：可能影响识别效果
            hotword_id: config.QCloudAIVoice.hotword_id,
            customization_id: config.QCloudAIVoice.customization_id,
            convert_num_mode: 3 // 0：不转换，直接输出中文数字，1：根据场景智能转换为阿拉伯数字，3: 打开数学相关数字转换。默认值为1
        };
        if (
            voiceInteractionRef.current === VoiceInteraction.Manual ||
            voiceInteractionRef.current === VoiceInteraction.TapManual
        ) {
            // 如果是按住说话的，取消说话检测
            voiceParams.needvad = 0;
        }
        console.log('voiceParams', voiceParams);
        speechRecognizerManager.current = new AudioSpeechRecognizer(voiceParams, true);
        // 开始识别
        speechRecognizerManager.current.OnRecognitionStart = (res: any) => {
            // 开始识别之后
            recognizeStatusRef.current = RecognizeStatus.Start;
            addLog({
                level: 'debug',
                message: 'handleRecognitionStart',
                data: {
                    res,
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current
                }
            });
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                changeStatus(VoiceStatus.Listening);
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
                clearRecognizerTimer();
                setRecognizerTime(60);
                recognizerTimeRef.current = setInterval(() => {
                    setRecognizerTime((prev) => {
                        if (prev === 0) {
                            clearRecognizerTimer();
                            return 0;
                        }
                        return prev - 1;
                    });
                }, 1000);
                timerOvertime.current = setTimeout(() => {
                    // 超过5s没说话，进入超时阶段
                    addLog({
                        level: 'trace',
                        message: '超时'
                    });
                    changeStatus(VoiceStatus.Overtime);
                    if (timerStandby.current) {
                        clearTimeout(timerStandby.current);
                    }
                    timerStandby.current = setTimeout(() => {
                        addLog({
                            level: 'trace',
                            message: '待机'
                        });
                        changeStatus(VoiceStatus.Standby);

                        stop();
                        if (timerOvertime.current) {
                            clearTimeout(timerOvertime.current);
                        }
                        if (timerStandby.current) {
                            clearTimeout(timerStandby.current);
                        }
                        userSentence.current = undefined;
                    }, standbyTime);
                }, overtime);
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.WaitListen) {
                    stop();
                }
                if (statusRef.current === VoiceStatus.BeforeListen) {
                    changeStatus(VoiceStatus.Listening);
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.TapManual) {
                changeStatus(VoiceStatus.Listening);
            }
        };
        // 一句话开始
        speechRecognizerManager.current.OnSentenceBegin = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.SentenceBegin;
            addLog({
                level: 'debug',
                message: '一句话开始',
                data: {
                    res,
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current
                }
            });
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
                if (statusRef.current === VoiceStatus.Overtime) {
                    changeStatus(VoiceStatus.Listening);
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.WaitListen) {
                    // stop();
                }
            }
        };
        // 识别变化时
        speechRecognizerManager.current.OnRecognitionResultChange = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.SentenceChange;
            // addLog({
            //     level: 'debug',
            //     message: '识别变化时',
            //     data: {
            //         res,
            //         voiceInteraction: voiceInteractionRef.current,
            //         status: statusRef.current
            //     }
            // });
        };
        // 一句话结束
        speechRecognizerManager.current.OnSentenceEnd = (res: any) => {
            if (res.result.word_size === 0) {
                recognizeStatusRef.current = RecognizeStatus.SentenceEnd;
                // 超过1.5s不说话，停止语音识别，发给ai
                const resSentence = res.result.voice_text_str;

                addLog({
                    level: 'debug',
                    message: '一句话结束',
                    data: {
                        res,
                        voiceInteraction: voiceInteractionRef.current,
                        status: statusRef.current
                    }
                });
                if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                    if (resSentence.trim()) {
                        if (userSentence.current === null || userSentence.current === undefined) {
                            // 先流式识别出了
                            addLog({
                                level: 'trace',
                                message: '自动模式，文字先出'
                            });
                            userSentence.current = { i: questionNowRef.current, text: resSentence };
                            if (
                                statusRef.current === VoiceStatus.Listening ||
                                statusRef.current === VoiceStatus.Overtime
                            ) {
                                if (timerOvertime.current) {
                                    clearTimeout(timerOvertime.current);
                                }
                                if (timerStandby.current) {
                                    clearTimeout(timerStandby.current);
                                }
                                if (questionNowRef.current < questions.current.length - 1) {
                                    changeStatus(VoiceStatus.Waiting);
                                }
                                stop();
                            }
                        } else {
                            userSentence.current.text = resSentence;
                            stop();
                            if (
                                statusRef.current === VoiceStatus.Listening ||
                                statusRef.current === VoiceStatus.Overtime
                            ) {
                                if (timerOvertime.current) {
                                    clearTimeout(timerOvertime.current);
                                }
                                if (timerStandby.current) {
                                    clearTimeout(timerStandby.current);
                                }
                                if (questionNowRef.current < questions.current.length - 1) {
                                    changeStatus(VoiceStatus.Waiting);
                                }
                                handleSend('audio');
                            }
                            addLog({
                                level: 'debug',
                                message: '自动模式，已有录音,进行保存',
                                data: {
                                    text: userSentence.current.text,
                                    i: userSentence.current.i,
                                    fileSize: userSentence.current?.fileSize
                                }
                            });
                        }
                    } else {
                        stop();
                        userSentence.current = undefined;

                        if (isTimeout.current) {
                            changeStatus(VoiceStatus.Standby);
                            doneActionSheet();
                        } else {
                            if (questionTimeout.current) {
                                questionTimeoutNext();
                            } else {
                                changeStatus(VoiceStatus.Standby);
                            }
                        }
                        const systemInfo = Taro.getSystemInfoSync();
                        const { platform } = systemInfo;
                        if (platform === 'ios') {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字或麦克风被占用'
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字'
                            });
                        }

                        addLog({
                            level: 'trace',
                            message: '自动模式，未识别到文字,等待'
                        });
                    }
                } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                    console.log('手动模式', userSentence.current);
                    if (timerNoSentence.current) {
                        clearTimeout(timerNoSentence.current);
                    }
                    if (statusRef.current === VoiceStatus.Waiting) {
                        // 如果是等待AI回复状态
                        if (resSentence.trim()) {
                            const rdt = removeDuplicateText(resSentence);
                            // console.log('用户输入', userSentence.current);
                            if (userSentence.current === null || userSentence.current === undefined) {
                                userSentence.current = { i: questionNowRef.current, text: rdt };
                                addLog({
                                    level: 'trace',
                                    message: '手动模式，文字先出'
                                });
                            } else {
                                userSentence.current.text = rdt;
                                handleAiHelpClose();
                                handleSend('audio');
                                addLog({
                                    level: 'debug',
                                    message: '手动模式,进行保存',
                                    data: {
                                        text: userSentence.current.text,
                                        i: userSentence.current.i,
                                        fileSize: userSentence.current?.fileSize
                                    }
                                });
                            }
                        } else {
                            if (userSentence.current) {
                                userSentence.current = undefined;
                            }

                            if (isTimeout.current) {
                                changeStatus(VoiceStatus.WaitListen);
                                doneActionSheet();
                            } else {
                                if (questionTimeout.current) {
                                    questionTimeoutNext();
                                } else {
                                    changeStatus(VoiceStatus.WaitListen);
                                }
                            }
                            const systemInfo = Taro.getSystemInfoSync();
                            const { platform } = systemInfo;
                            if (platform === 'ios') {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字或麦克风被占用'
                                });
                            } else {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字'
                                });
                            }

                            addLog({
                                level: 'trace',
                                message: '手动模式，未识别到文字,等待'
                            });
                        }
                    } else if (statusRef.current === VoiceStatus.WaitListen) {
                        // stop();
                    } else if (statusRef.current === VoiceStatus.Listening) {
                        // 概率性，手指抬起前，有文字识别出来

                        // 如果是等待AI回复状态
                        if (userSentence.current) {
                            userSentence.current = undefined;
                        }
                        changeStatus(VoiceStatus.WaitListen);
                        if (isTimeout.current) {
                            doneActionSheet();
                        }
                        const systemInfo = Taro.getSystemInfoSync();
                        const { platform } = systemInfo;
                        if (platform === 'ios') {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字或麦克风被占用'
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字'
                            });
                        }

                        addLog({
                            level: 'trace',
                            message: '手动模式，未抬起按钮，就有文字识别出来'
                        });
                    }
                } else if (voiceInteractionRef.current === VoiceInteraction.TapManual) {
                    if (timerNoSentence.current) {
                        clearTimeout(timerNoSentence.current);
                    }

                    if (statusRef.current === VoiceStatus.WaitSend || statusRef.current === VoiceStatus.Waiting) {
                        if (resSentence.trim()) {
                            console.log('用户输入', userSentence.current);
                            const rdt = removeDuplicateText(resSentence);
                            if (userSentence.current === null || userSentence.current === undefined) {
                                userSentence.current = { i: questionNowRef.current, text: rdt };
                                addLog({
                                    level: 'trace',
                                    message: '手动点击模式，文字先出'
                                });
                            } else {
                                userSentence.current.text = rdt;
                                // handleSend(resSentence, questionNowRef.current, 'audio');
                                if (isTimeout.current) {
                                    handleSend('audio');
                                } else {
                                    if (questionTimeout.current) {
                                        handleSend('audio');
                                    } else {
                                        changeStatus(VoiceStatus.AllowSend);
                                    }
                                }

                                addLog({
                                    level: 'debug',
                                    message: '手动点击模式,进行保存',
                                    data: {
                                        text: userSentence.current.text,
                                        i: userSentence.current.i,
                                        fileSize: userSentence.current?.fileSize
                                    }
                                });
                            }
                        } else {
                            if (userSentence.current) {
                                userSentence.current = undefined;
                            }

                            if (isTimeout.current) {
                                doneActionSheet();
                            } else {
                                if (questionTimeout.current) {
                                    questionTimeoutNext();
                                } else {
                                    changeStatus(VoiceStatus.WaitListen);
                                }
                            }
                            const systemInfo = Taro.getSystemInfoSync();
                            const { platform } = systemInfo;
                            if (platform === 'ios') {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字或麦克风被占用'
                                });
                            } else {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字'
                                });
                            }
                            addLog({
                                level: 'trace',
                                message: '手动点击模式，未识别到文字,等待'
                            });
                        }
                    }
                }
            } else {
                addLog({
                    level: 'warn',
                    message: '一句话重复',
                    data: {
                        res
                    }
                });
            }
        };
        // 识别结束
        speechRecognizerManager.current.OnRecognitionComplete = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.Complete;
            addLog({
                level: 'debug',
                message: '识别结束',
                data: {
                    res,
                    status: statusRef.current
                }
            });
            if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (timerNoSentence.current) {
                    clearTimeout(timerNoSentence.current);
                }
            }
        };
        // 识别错误
        speechRecognizerManager.current.OnError = (res: any) => {
            addLog({
                level: 'error',
                message: '识别错误',
                data: {
                    res
                }
            });
            recognizeStatusRef.current = RecognizeStatus.Error;
            // code为6001时，国内站用户请检查是否使用境外代理，如果使用请关闭。境外调用需开通国际站服务
            console.error('识别失败', res, getVoiceStatusKey(statusRef.current));
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                changeStatus(VoiceStatus.Standby);
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
            } else if (
                voiceInteractionRef.current === VoiceInteraction.Manual ||
                voiceInteractionRef.current === VoiceInteraction.TapManual
            ) {
                changeStatus(VoiceStatus.WaitListen);
            }
            // 如果倒计时没结束，发生错误重新倒计时

            userSentence.current = undefined;
            // handleErrorDialog('语音识别异常，请刷新后重试');
            if (res.code) {
                if (res.code === '4006' || res.code === 4006) {
                    Taro.showToast({
                        icon: 'none',
                        title: '当前访问人数过多，\r\n请稍后重试'
                    });
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '说话时间太短'
                    });
                }
            } else {
                if (res.errMsg && res.errMsg.includes('system permission denied')) {
                    Taro.showToast({
                        icon: 'none',
                        title: `请开启${isWework === '0' ? '微信' : '企业微信'}麦克风权限`
                    });
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '语音识别错误'
                    });
                }
            }
            if (isTimeout.current) {
                doneActionSheet();
            } else {
                initQuestionTimer(questionNowRef.current);
            }
        };
        // 录音结束（最长10分钟）时回调
        speechRecognizerManager.current.OnRecorderStop = (res: any) => {
            addLog({
                level: 'debug',
                message: '录音结束',
                data: {
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current,
                    recorderRes: res,
                    userSentence: userSentence.current,
                    questionNow: questionNowRef.current,
                    questionLength: questions.current.length - 1
                }
            });

            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current === VoiceStatus.Standby) {
                    return;
                }
                if (userSentence.current === undefined || userSentence.current === null) {
                    // 如果文字还没识别，先创建，文字识别出来后改写
                    addLog({
                        level: 'trace',
                        message: 'RecorderStop 自动模式，录音先出来'
                    });
                    if (recognizeStatusRef.current === RecognizeStatus.Start) {
                        // 如果没有回调sentence
                        if (isTimeout.current) {
                            doneActionSheet();
                        } else {
                            if (questionTimeout.current) {
                                questionTimeoutNext();
                            }
                        }
                    } else {
                        userSentence.current = {
                            i: questionNowRef.current,
                            text: '',
                            filePath: res.tempFilePath,
                            fileSize: res.fileSize
                        };
                        initTimerNoSentense();
                    }
                } else {
                    // 如果文字先识别，再去识别改写
                    if (questionNowRef.current < questions.current.length - 1) {
                        changeStatus(VoiceStatus.Waiting);
                    }
                    userSentence.current.filePath = res.tempFilePath;
                    userSentence.current.fileSize = res.fileSize;
                    if (userSentence.current.text.trim()) {
                        handleSend('audio');
                    } else {
                        if (isTimeout.current) {
                            doneActionSheet();
                        } else {
                            if (questionTimeout.current) {
                                questionTimeoutNext();
                            }
                        }
                    }
                    addLog({
                        level: 'trace',
                        message: 'RecorderStop 自动模式，文字已有，得到录音，进行保存'
                    });
                }
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                    timerOvertime.current = null;
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                    timerStandby.current = null;
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                // 手动模式
                if (statusRef.current === VoiceStatus.Waiting) {
                    if (userSentence.current === undefined || userSentence.current === null) {
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动模式录音先出来'
                        });
                        userSentence.current = {
                            i: questionNowRef.current,
                            text: '',
                            filePath: res.tempFilePath,
                            fileSize: res.fileSize
                        };
                        initTimerNoSentense();
                    } else {
                        userSentence.current.filePath = res.tempFilePath;
                        userSentence.current.fileSize = res.fileSize;

                        handleSend('audio');
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动模式，文字已有，得到录音，进行保存'
                        });
                    }
                } else if (statusRef.current === VoiceStatus.WaitListen) {
                    userSentence.current = undefined;
                } else if (statusRef.current === VoiceStatus.Listening) {
                    userSentence.current = undefined;
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.TapManual) {
                if (statusRef.current === VoiceStatus.WaitSend || statusRef.current === VoiceStatus.Waiting) {
                    if (userSentence.current === undefined || userSentence.current === null) {
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动点击模式录音先出来'
                        });
                        userSentence.current = {
                            i: questionNowRef.current,
                            text: '',
                            filePath: res.tempFilePath,
                            fileSize: res.fileSize
                        };
                        timerNoSentence.current = setTimeout(() => {
                            if (
                                (!userSentence.current || !userSentence.current.text) &&
                                statusRef.current === VoiceStatus.Waiting
                            ) {
                                // 读取文件并转换成base64
                                const fs = getFileSystemManager();
                                fs.readFile({
                                    filePath: res.tempFilePath,
                                    encoding: 'base64',
                                    success: (fileRes: any) => {
                                        addLog({
                                            level: 'error',
                                            message: '未识别的音频',
                                            data: {
                                                fileRes
                                            }
                                        });
                                    }
                                });
                                userSentence.current = undefined;
                                const systemInfo = Taro.getSystemInfoSync();
                                const { platform } = systemInfo;
                                if (platform === 'ios') {
                                    Taro.showToast({
                                        icon: 'none',
                                        title: '未识别到文字或麦克风被占用'
                                    });
                                } else {
                                    Taro.showToast({
                                        icon: 'none',
                                        title: '未识别到文字'
                                    });
                                }
                                addLog({
                                    level: 'warn',
                                    message: 'RecorderStop 手动点击模式，sdk没有返回识别到的文字，切换到等待聆听状态'
                                });
                                if (questionTimeout.current) {
                                    questionTimeoutNext();
                                } else {
                                    changeStatus(VoiceStatus.WaitListen);
                                }
                            }
                        }, 5000);
                    } else {
                        userSentence.current.filePath = res.tempFilePath;
                        userSentence.current.fileSize = res.fileSize;

                        // handleSend(userSentence.current.text, questionNowRef.current, 'audio');
                        if (isTimeout.current || questionTimeout.current) {
                            handleSend('audio');
                        } else {
                            changeStatus(VoiceStatus.AllowSend);
                        }
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动点击模式，文字已有，得到录音，进行保存'
                        });
                    }
                }
            }

            // 小程序目前支持最大时长为10分钟，此参数提供给用户自定义设置，若不传，则为10分钟，超过10分钟，录音自动停止，插件也会关闭连接。
            // 若需要超过10分钟的场景，需要用户自己处理。可在 OnRecorderStop 回调中判断，重新开启录音，建立连接。
        };

        speechRecognizerManager.current.OnFrameRecorded = (res: any) => {
            const { frameBuffer } = res;

            // 2. 计算音量分贝值
            const decibel = calculateDecibel(frameBuffer);
            if (isadd == 1) {
                setdecibelPlay(decibel + 10);
                setisZore(decibel);
                isadd = 2;
                // setisadd(2);
            } else {
                setdecibelPlay(decibel + 40);
                setisZore(decibel);
                // setisadd(1);
                isadd = 1;
            }
        };

        speechRecognizerManager.current.OnLog = (log: LogInfo) => {
            addLog(log);
        };
        const systemInfo = Taro.getSystemInfoSync();
        // const isIOS18 = (systemInfo.platform === 'ios' || systemInfo.platform === 'iOS') && systemInfo.system.split(' ')[1].split('.')[0] === '18';
        // console.log('isIOS18', isIOS18);

        if (isWework === '0') {
            Taro.request({ url: `${config.cdnPrefix}chat/lottie_bubble.json` }).then(({ data }) => {
                createSelectorQuery()
                    .select('#bubble')
                    .node((res) => {
                        const canvas = res.node;
                        lottie.setup(canvas);
                        bubbleAnimate.current = lottie.loadAnimation({
                            loop: true,
                            autoplay: true,
                            rendererSettings: {
                                context: canvas.getContext('2d')
                            },
                            animationData: data
                        });
                    })
                    .exec();
            });
            Taro.request({ url: `${config.cdnPrefix}chat/lottie_wave.json` }).then(({ data }) => {
                createSelectorQuery()
                    .select('#wave')
                    .node((res) => {
                        const canvas = res.node;
                        const context = canvas.getContext('2d');
                        const dpr = systemInfo.pixelRatio;
                        canvas.width = canvas.width * dpr;
                        canvas.height = canvas.height * dpr;
                        context.scale(dpr, dpr);

                        lottie.setup(canvas);
                        waveAnimate.current = lottie.loadAnimation({
                            loop: true,
                            autoplay: true,
                            rendererSettings: {
                                context: canvas.getContext('2d')
                            },
                            animationData: data
                        });
                    })
                    .exec();
            });
        }
        onLoaded();
    }

    function initQuestion() {
        // changeStatus(VoiceStatus.Start);
        changeStatus(VoiceStatus.Waiting);
        handleShowVoicePage();
        initVoice();

        setTimeout(() => {
            const nextQuestion = questions.current[questionNowRef.current];
            speakStart();
            speakProcess(nextQuestion.question); // 传给语音合成
        }, 500);
    }

    const networkWeakListener = (res: { weakNet: boolean; networkType: string }) => {
        console.log('networkWeakListener', res);
        if (res.weakNet) {
            Toast_.show({
                message: '当前网络状况不佳',
                position: 'top'
            });
            addLog({
                level: 'warn',
                message: '当前网络状况不佳',
                data: {
                    networkType: res.networkType,
                    weakNet: res.weakNet
                }
            });
        }
    };

    useEffect(() => {
        console.log('eman', data?.eman);

        if (data && data.eman.tone) {
            try {
                const parseTone = JSON.parse(data.eman.tone);
                console.log('parTone', parseTone);
                if (parseTone.speed !== null && parseTone.speed !== undefined) {
                    tone.current.speed = parseTone.speed;
                }
                if (parseTone.volume !== null && parseTone.volume !== undefined) {
                    tone.current.volume = parseTone.volume;
                }
                if (parseTone.voiceType !== null && parseTone.voiceType !== undefined) {
                    tone.current.voiceType = parseTone.voiceType;
                }
                if (parseTone.name !== null && parseTone.name !== undefined) {
                    tone.current.name = parseTone.name;
                }
                addLog({
                    level: 'debug',
                    message: '设置音调',
                    data: {
                        tone: parseTone
                    }
                });
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'tone格式错误',
                    data: {
                        error
                    }
                });
            }
        }
    }, [data]);
    useEffect(() => {
        console.log('show', showVoicePage);
        if (showVoicePage) {
            createSelectorQuery()
                .select('#actionfooter')
                .boundingClientRect((res: any) => {
                    if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Cancel) {
                        console.log('footerheight', res);
                        touchTop.current = res.top;
                    }
                })
                .exec();
            if (isWework === '0') {
                onNetworkWeakChange(networkWeakListener);
            }
            setKeepScreenOn({
                keepScreenOn: true,
                fail(res: any) {
                    console.log('keepScreenOn error', res);
                }
            });
            setInnerAudioOption({ obeyMuteSwitch: false });
        } else {
            // if (questionNowRef.current === questionList.length - 1) {
            //     finishComplete();
            // } else {
            //     finish();
            // }
            if (isWework === '0') {
                offNetworkWeakChange(networkWeakListener);
            }

            setKeepScreenOn({
                keepScreenOn: false,
                fail(res: any) {
                    console.log('keepScreenOff error', res);
                }
            });
        }
    }, [showVoicePage]);

    useMount(async () => {
        console.log('mount``', data);

        try {
            const pages = getCurrentPages();
            feedbackData.current.path = pages[pages.length - 1].$taroPath || pages[pages.length - 1].route;

            const { appId, version } = Taro.getAccountInfoSync().miniProgram;
            feedbackData.current.appId = appId;
            setIsQnq(appId === AppIdConsts.qnq);
            feedbackData.current.version = version;
            Taro.getSystemInfo({
                success(res: any) {
                    console.log('getSystemInfo', res);
                    feedbackData.current.brand = res.brand;
                    feedbackData.current.model = res.model;
                    feedbackData.current.platform = res.platform;
                    feedbackData.current.system = res.system;
                    feedbackData.current.wxVersion = res.version;
                    feedbackData.current.SDKVersion = res.SDKVersion;
                    feedbackData.current.environment = res.environment;
                    feedbackData.current.microphoneAuthorized = res.microphoneAuthorized;
                    feedbackData.current.name = userInfo.name;
                    feedbackData.current.company = userInfo.companyName;
                    feedbackData.current.phone = userInfo.phoneNumber;
                }
            });
        } catch (error) {
            addLog({
                level: 'error',
                message: 'getAccountInfoSync',
                data: {
                    error
                }
            });
        }
        const voice_interaction_type = Storage.get(StorageEnvKey.QUESTION_INTERACTION_TYPE) || VoiceInteraction.Manual;
        const chatCurrentMode = Storage.get(StorageEnvKey.CHAT_MODE) || ChatMode.SHUZIREN;
        setShowVideoClose(chatCurrentMode);
        voiceInteractionRef.current = voice_interaction_type;
        setVoiceInteraction(voice_interaction_type);
        const subtitle_visible = Storage.get(StorageEnvKey.SUBTITLE_VISIBLE) || SubtitleVisible.Hide;
        setSubtitleShow(subtitle_visible);
        addLog({
            level: 'debug',
            message: '语音交互类型',
            data: {
                voice_interaction_type
            }
        });
        const sysinfo = Taro.getSystemInfoSync();
        console.log('-sysinfo-', sysinfo);
        window_height = sysinfo.windowHeight;

        setTimeout(() => {
            const query1 = Taro.createSelectorQuery();
            query1
                .select('#bottomContent')
                .boundingClientRect()
                .exec((rect) => {
                    bottomContent_height = rect[0].height;
                });
            const query2 = Taro.createSelectorQuery();
            query2
                .select('#topBox')
                .boundingClientRect()
                .exec((rect) => {
                    topBox_height = rect[0].height;
                });
        }, 1000);
        const isHarmony = await checkHarmony();
        console.log('isHarmony', isHarmony);
        isHarmonyRef.current = isHarmony;
        if (!data) return;

        questions.current = data.qaList;
        scriptQuestions.current = data.script.question;
        setQuestionList(data.qaList);
        // 找到哪题是没答的

        time.current = data.script.timeLimit;
        timeLimitType.current = data.script.timeLimitType;
        try {
            const { data: historyRes } = await getAllChatHistory(chatId);
            if (historyRes.code === 200) {
                setHistory(historyRes.data);
                historyRef.current = historyRes.data;
            }
            setCustomTitle({ title: data.eman.name });
            chatData.current = data;
            setAiHelpEnable(data.script.aiHelpFlag);
            const { data: serverTimeData } = await getServerTime();
            console.log('servertime', serverTimeData.data);
            serverTime.current = serverTimeData.data;
            createTime.current = data.createTime;
            const leftTime =
                dayjs(data.createTime).add(time.current, 'm').valueOf() - dayjs(serverTime.current).valueOf();
            setLeftTime(leftTime);
            console.log('getChat-leftTime', leftTime);

            // 对话时间结束，关闭对话
            if (data.done) {
                addLog({
                    level: 'trace',
                    message: '对话已完成'
                });
                cancel();
                setLeftTime(undefined);
                onLoaded();
                isTimeout.current = true;
                if (showVoicePageRef.current) {
                    // 语音的情况
                    finishVoice();
                }
                if (from === 'shuziren') {
                    setChatActionSheetType(ChatActionSheetType.COMPLETE);
                    setShowActionSheet(true);
                }

                return;
            } else if (leftTime <= 0) {
                addLog({
                    level: 'trace',
                    message: '倒计时结束了'
                });
                cancel();
                onLoaded();
                isTimeout.current = true;
                const res = await doneChat(chatId, false);
                if (res.data.code === 200) {
                    if (showVoicePageRef.current) {
                        // 语音的情况
                        finishVoice();
                    }
                    chatData.current.done = true;
                    setChatActionSheetType(ChatActionSheetType.OFF);
                    setShowActionSheet(true);
                }
            } else {
                addLog({
                    level: 'trace',
                    message: '还有剩余时间'
                });

                const current = data.qaList.findIndex((item) => !item.done);
                addLog({
                    level: 'debug',
                    message: '找到哪题是没回答的',
                    data: {
                        current
                    }
                });
                if (current === -1) {
                    // 全部答完了
                    onLoaded();
                    await doneChat(chatId, false);
                    chatData.current.done = true;

                    cancel();
                    setChatActionSheetType(ChatActionSheetType.COMPLETE);
                    setShowActionSheet(true);
                } else {
                    // 未答完的是第几题

                    const nowQIndex = current;
                    setQuestionNow(nowQIndex);
                    questionNowRef.current = nowQIndex;

                    if (from === 'shuziren') {
                        // 数字人情况，文字界面
                        onLoaded();
                        if (historyRes.data.length === 0) {
                            // 如果未答过，保存第一题问题
                            await saveQuestion(0);
                            nextQuestion(0);
                        } else {
                            initQuestionTimer(questionNowRef.current);
                        }
                    } else {
                        if (!isHarmony) {
                            handleShowVoicePage();
                            initVoice();
                            changeStatus(VoiceStatus.Waiting);
                        } else {
                            onLoaded();
                        }

                        if (historyRes.data.length === 0) {
                            // 如果未答过，保存第一题问题
                            await saveQuestion(0);
                            nextQuestion(0);
                        } else {
                            setTimeout(() => {
                                initQuestionTimer(questionNowRef.current);
                                const nextQuestion = questions.current[questionNowRef.current];
                                if (!isHarmony) {
                                    speakStart();
                                    speakProcess(nextQuestion.question); // 传给语音合成
                                }
                            }, 500);
                        }
                    }
                    // if (interrupt === '1') {
                    // 打断后，语音重新开始

                    // initQuestion();
                    // } else {
                    //     if (dayjs(serverTime.current).valueOf() - dayjs(data.createTime).valueOf() > 60000) {
                    //         onLoaded();
                    //         setChatActionSheetType(ChatActionSheetType.INTERRUPT);
                    //         setShowActionSheet(true);
                    //     } else {
                    //         initQuestion();
                    //     }
                    // }
                }
            }
        } catch (error) {
            console.log('getAllChatHistory error', error);
        }
    });

    useUnmount(() => {
        finish();
        if (bubbleAnimate.current) {
            bubbleAnimate.current.destroy();
        }
        if (waveAnimate.current) {
            waveAnimate.current.destroy();
        }
        clearQuestionTimer();
        setKeepScreenOn({
            keepScreenOn: false,
            fail(res: any) {
                console.error('keepScreenOff error', res);
            }
        });
    });
    useEffect(() => {
        Taro.onKeyboardHeightChange((res) => {
            setKeyHeight(res.height); //  - 2 安卓机型出现了蓝线
            if (res.height) {
                const query = Taro.createSelectorQuery();
                query
                    .select('#chat')
                    .boundingClientRect()
                    .exec((rect) => {
                        const chat_height = rect[0].height;
                        const t1 = window_height - topBox_height - res.height - bottomContent_height;
                        const t2 = chat_height - 100;
                        console.log('window', window_height);
                        console.log('topBox', topBox_height);
                        console.log('keyboard', res.height);
                        console.log('bottomContent', bottomContent_height);
                        console.log('chat_height', chat_height);
                        console.log('t1', t1);
                        console.log('t2', t2);
                        // 总高度 - 头部 - 键盘 - 发送框 = 内容区域
                        if (t1 < t2) {
                            // 计算出应该上移多少距离
                            // 最多res.height 最少0 中间变量值为t2 - t1
                            setTranslateHeight(t2 - t1 < res.height ? t2 - t1 : res.height);
                        } else {
                            setTranslateHeight(0);
                        }
                    });
            }
        });
    }, []);

    const { run: onAiHelp } = useThrottleFn(
        useCallback(() => {
            if (isTimeout.current || chatData.current.done) {
                return;
            }
            if (assistantMessage) {
                Toast_.show({
                    message: 'AI说话中无法进行求助'
                });
            } else {
                setAiHelpShow(true);
            }
        }, [assistantMessage]),
        {
            wait: 100,
            leading: true,
            trailing: false
        }
    );

    const { run: handleAIHelp } = useThrottleFn(
        () => {
            if (isTimeout.current || chatData.current.done) {
                return;
            }
            if (statusRef.current === VoiceStatus.Speaking || statusRef.current === VoiceStatus.Waiting) {
                Toast_.show({
                    message: 'AI说话中无法进行求助'
                });
            } else {
                onAiHelp();
            }
        },
        {
            wait: 250,
            leading: true,
            trailing: false
        }
    );

    async function questionTimeoutNext() {
        addLog({
            level: 'debug',
            message: 'questionTimeoutNext',
            data: {
                questionNow: questionNowRef.current
            }
        });
        changeStatus(VoiceStatus.Waiting);
        try {
            const i = questionNowRef.current;
            const answer = '';
            questions.current[i].answer = answer;
            setHistory((history) => {
                const newHistory = [
                    ...history,
                    {
                        role: RoleEnum.USER,
                        content: answer,
                        avatar: userInfo?.avatar,
                        qaId: questions.current[i].qaId
                    }
                ];
                historyRef.current = newHistory;
                return newHistory;
            });
            formIt.resetFields();
            setCanSend(false);
            const param: any = {
                qaId: questions.current[questionNowRef.current].qaId,
                answer: ''
            };
            await qaCompletions(chatId, param);

            handleNext();
            onScrollView();
        } catch (error) {}
    }

    const questionTimerEnd = () => {
        addLog({
            level: 'trace',
            message: 'questionTimerEnd'
        });
        questionTimeout.current = true;
        clearQuestionTimer();
        // 判断是否语音
        if (showVoicePageRef.current) {
            // 语音的情况
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current === VoiceStatus.Listening) {
                    // 聆听状态，体制识别，等待识别结果出来后发送
                    stop();
                } else if (statusRef.current === VoiceStatus.Overtime) {
                    // 超时或者待机状态，停止识别，直接下一题
                    stop();
                    if (timerOvertime.current) {
                        clearTimeout(timerOvertime.current);
                    }
                    if (timerStandby.current) {
                        clearTimeout(timerStandby.current);
                    }
                    questionTimeoutNext();
                } else if (
                    statusRef.current === VoiceStatus.Standby ||
                    statusRef.current === VoiceStatus.BeforeListen
                ) {
                    if (timerOvertime.current) {
                        clearTimeout(timerOvertime.current);
                    }
                    if (timerStandby.current) {
                        clearTimeout(timerStandby.current);
                    }
                    questionTimeoutNext();
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                handleAiHelpClose();
                if (statusRef.current === VoiceStatus.Listening) {
                    changeStatus(VoiceStatus.Waiting);

                    stop();
                    setSayPressed(false);
                } else if (
                    statusRef.current === VoiceStatus.WaitListen ||
                    statusRef.current === VoiceStatus.BeforeListen
                ) {
                    questionTimeoutNext();
                } else if (statusRef.current === VoiceStatus.Cancel) {
                    stop();
                    setSayPressed(false);
                    questionTimeoutNext();
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.TapManual) {
                handleAiHelpClose();
                if (timerNoSentence.current) {
                    clearTimeout(timerNoSentence.current);
                }
                if (statusRef.current === VoiceStatus.WaitListen) {
                    questionTimeoutNext();
                } else if (statusRef.current === VoiceStatus.BeforeListen) {
                    questionTimeoutNext();
                } else if (statusRef.current === VoiceStatus.Listening) {
                    changeStatus(VoiceStatus.Waiting); // 切换到等待发送状态，等待识别文字和录音文件
                    stop();
                } else if (statusRef.current === VoiceStatus.AllowSend) {
                    changeStatus(VoiceStatus.Waiting);
                    handleSend('audio');
                }
            }
        } else {
            // 文字的情况;
            // 下一题
            const message = formIt.getFieldValue('message');
            // if (message !== null && message !== undefined && message !== '' && message.trim()) {
            //     onSend();

            handleSend('text', message === null || message === undefined ? '' : message);
            // } else {
            //     questionTimeoutNext();
            // }
        }
    };

    // 初始化问题倒计时
    function initQuestionTimer(qNow: number) {
        clearQuestionTimer();
        if (scriptQuestions.current && timeLimitType.current === 2) {
            const { timeLimit } = scriptQuestions.current[qNow];
            if (timeLimit !== null) {
                setQuestionTime(timeLimit);
                questionTimer.current = setInterval(() => {
                    setQuestionTime((prev) => {
                        // console.log('prev', prev);
                        if (prev !== undefined) {
                            if (prev === 0) {
                                // 倒计时结束，下一题
                                questionTimerEnd();
                                return 0;
                            }
                            return prev - 1;
                        } else {
                            return prev;
                        }
                    });
                }, 1000);
            }
        }
    }

    const leftQuestionTimeText = useMemo(() => {
        if (questionTime === undefined || questionTime === null) {
            return '';
        } else {
            return dayjs
                .duration({
                    minutes: Math.floor(questionTime / 60), // questionTime的分钟
                    seconds: questionTime % 60 // questionTime的秒
                })
                .format('mm:ss');
        }
    }, [questionTime]);

    const showQuestionCountDownTip = () => {
        Dialog_.alert({
            title: '作答时限',
            zIndex: 1000,
            message: '本题答题时间限制，倒计时结束，将自动跳到下一题',
            confirmButtonText: '我知道了',
            confirmButtonColor: '#4F66FF'
        }).then(() => {});
    };

    return (
        <Block>
            <View id='topBox' className={styles.topBox}>
                {/* style='padding-top: 88px;' */}
                <NavBar
                    title={customTitle.title}
                    safeAreaInsetTop
                    renderLeft={
                        <View className={styles.countDownBox}>
                            <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                            <View className={styles.countDown} onClick={goBack}>
                                {timeLeftText}
                            </View>
                        </View>
                    }
                />
                {leftQuestionTimeText && (
                    <View className={styles.question_time}>
                        <View onClick={showQuestionCountDownTip}>
                            本题作答倒计时 <Icon name='question' size={pxTransform(28)} />
                        </View>
                        <View>{leftQuestionTimeText}</View>
                    </View>
                )}
                <View className={styles.noticeBar}>对话为AI情景模拟，非真实场景</View>
            </View>
            <View id='chat' style={{ transform: `translateY(-${keyHeight ? translateHeight : keyHeight}px)` }}>
                <ChatHistory
                    hasCountdown={!!leftQuestionTimeText}
                    scene={data?.scene.scene as string}
                    scriptTime={data?.createTime as string}
                    history={history}
                    assistantMessage={assistantMessage}
                    eman={{
                        name: data?.eman.name as string,
                        avatar: data?.eman.avatar as string
                    }}
                />
            </View>
            <View
                id='footer'
                className={styles.footer}
                style={{ paddingBottom: history.length > 0 ? pxTransform(96 + 156) : 0, marginBottom: keyHeight }}
            />
            {/* paddingBottom: history.length > 0 ? pxTransform(156) : 0, */}
            <View
                className={styles.bottomContentAihelp}
                style={{
                    bottom: keyHeight,
                    background: aiHelpShow ? 'rgba(0, 0, 0, 0.3)' : '',
                    zIndex: aiHelpShow ? 2 : -1
                }}
            >
                <Aihelp
                    show={aiHelpShow}
                    keyHeight={keyHeight}
                    chatId={data?.id}
                    styleType={showVoicePage ? 'voice' : 'text'}
                    onClose={handleAiHelpClose}
                    canGenerate={canGenerate}
                />
            </View>
            <View id='bottomContent' className={styles.bottomContent} style={{ bottom: keyHeight }}>
                <View className={styles.Ai_help}>
                    {showScriptBtn && !aiHelpShow && (
                        <View className={styles.showScripts} onClick={onShowScript}>
                            <Image style={{ width: pxTransform(36), height: pxTransform(36) }} src={scriptImg} />
                            <Text>培训脚本</Text>
                        </View>
                    )}
                    {aiHelpEnable && !aiHelpShow && (
                        <View
                            className={classnames(styles.showScripts, assistantMessage ? styles.showScripts_Ai : '')}
                            onClick={onAiHelp}
                        >
                            <Image style={{ width: pxTransform(36), height: pxTransform(36) }} src={IconHelpBlue} />
                            <Text>AI求助</Text>
                        </View>
                    )}
                </View>
                <View className={styles.sendMessage}>
                    <View className={styles.sendMessage_tel_view} onClick={onVoiceClick}>
                        <Image className={styles.sendMessage_tel_image} src={TelImg} />
                    </View>

                    <Form form={formIt} className={classnames(styles.sendMessage_box, 'send_message_box')}>
                        <FormItem name='message' label=''>
                            <Textarea
                                autoHeight
                                className={styles.sendMessage_input}
                                placeholder={
                                    data?.script.disableTextInputFlag ? '本脚本已禁用文字输入对话' : '请输入消息'
                                }
                                placeholderStyle={`font-size: ${pxTransform(28)};line-height: 1}`}
                                maxlength={1500}
                                // value={message}
                                disabled={
                                    data?.script.disableTextInputFlag ||
                                    data.done ||
                                    questionLoading ||
                                    !!assistantMessage ||
                                    showVoicePage
                                }
                                confirmType='send'
                                adjustPosition={false}
                                controlled
                                onConfirm={(e: any) => {
                                    console.log('confirm', e);
                                    // setMessage(e.detail.value);
                                    onSend();
                                }}
                                onInput={(e: any) => {
                                    formIt.setFieldsValue('message', e.detail.value.substring(0, 1500));
                                    setCanSend(!(e.detail.value.trim() === ''));
                                }}
                            />
                        </FormItem>
                        <Button
                            onClick={onSend}
                            className={styles.sendMessage_send}
                            style={{
                                backgroundImage: `url(${canSend ? send_1 : send_0})`
                            }}
                        />
                    </Form>
                </View>
            </View>
            <ActionSheet
                show={showActionSheet}
                zIndex={110}
                onClose={() => setShowActionSheet(false)}
                title={actionSheet?.title}
                className={classnames(styles.actionSheet, {
                    [styles.actionSheet_hide_close]: actionSheet?.closeAction
                })}
                closeOnClickOverlay={false}
                // @ts-ignore
                style={{ '--action-sheet-header-height': pxTransform(124) }}
            >
                <Button.Group className={styles.actionSheet_group} direction='vertical'>
                    {(actionSheet?.actions ?? [])
                        .filter((action) => {
                            if (action.noChatHistoryHide) {
                                if (history.length === 0 || history.length === 1) {
                                    return false;
                                } else {
                                    return true;
                                }
                            } else {
                                return true;
                            }
                        })
                        .map((action) => {
                            return (
                                // eslint-disable-next-line taro/no-spread-in-props
                                <Button block round {...action.buttonProps} key={action.text}>
                                    {action.text}
                                </Button>
                            );
                        })}
                </Button.Group>
            </ActionSheet>
            <ActionMore
                show={showMoreActionSheet}
                onClose={() => setShowMoreActionSheet(false)}
                onScript={() => {
                    setShowMoreActionSheet(false);
                    onShowScript();
                }}
                onSetting={showActionSheetVoiceSetting}
                onFeedback={handleFeedback}
            />
            <ActionVoice
                show={showVoiceActionSheet}
                type='QUESTION_INTERACTION_TYPE'
                onClose={() => setShowVoiceActionSheet(false)}
                onConfirm={voicePopupConfirm}
            />

            {/* <VoiceDialogueQuestion
                show={showVoicePage}
                ref={voicePageRef}
                chat={data}
                minutes={leftMinutes !== undefined ? leftMinutes : minutes}
                seconds={leftSeconds !== undefined ? leftSeconds : seconds}
                questionNow={questionNow}
                questionList={questionList}
                onClose={() => {
                    setShowVoicePage(false);
                    showVoicePageRef.current = false;
                    onScrollView();
                }}
                onSentence={handleSend}
                onSentenceRecognize={handleSentenceRecognize}
                onCompleteAnswer={showCompleteAnswer}
                onFinish={goBack}
                showMore={showMore}
                onLoaded={onLoaded}
            /> */}

            <View
                className={voiceStyle.container}
                style={{ transform: showVoicePage ? 'translateX(0)' : 'translateX(100%)' }}
            >
                <Image src={backgroundBlur} mode='aspectFill' className={voiceStyle.bg} />
                {isWework === '1' ? <View className={voiceStyle.blur} /> : <View className={voiceStyle.blurbg} />}
                <NavBar
                    className={voiceStyle.navbar}
                    title={`第${questionNow + 1}/${questionList.length}题`}
                    safeAreaInsetTop
                    renderLeft={
                        voiceInteraction === VoiceInteraction.Manual && (
                            <Icon name='cross' size={pxTransform(40)} color='#ffffff' />
                        )
                    }
                    onClickLeft={() => {
                        if (voiceInteraction === VoiceInteraction.Manual) {
                            addLog({
                                level: 'trace',
                                message: '手动模式，点击左上角关闭'
                            });
                            handleFinish();
                        }
                    }}
                />

                <View className={voiceStyle.content}>
                    {leftQuestionTimeText && (
                        <View className={voiceStyle.question_time}>
                            <View onClick={showQuestionCountDownTip}>
                                本题作答倒计时 <Icon name='question' size={pxTransform(28)} />
                            </View>
                            <View>{leftQuestionTimeText}</View>
                        </View>
                    )}
                    <View className={voiceStyle.avatar_content}>
                        <View className={voiceStyle.avatar_box}>
                            <Canvas
                                className={classnames(voiceStyle.wave, {
                                    [voiceStyle.wave_active]: status === VoiceStatus.Speaking
                                })}
                                id='wave'
                                type='2d'
                            />
                            <Image
                                src={data?.eman?.avatar ?? AvatarDefault}
                                mode='aspectFit'
                                className={classnames(voiceStyle.avatar_img, {
                                    [voiceStyle.avatar_img_active]:
                                        status === VoiceStatus.Waiting || status === VoiceStatus.Loading,
                                    [voiceStyle.avatar_img_active_b]: status === VoiceStatus.Speaking
                                })}
                            />
                            <Canvas
                                className={classnames(voiceStyle.bubble, {
                                    [voiceStyle.bubble_active]:
                                        status === VoiceStatus.Waiting || status === VoiceStatus.Loading
                                })}
                                id='bubble'
                                type='2d'
                            />
                        </View>
                    </View>
                    {/* <View className={voiceStyle.scene}>{data?.scene.scene}</View> */}
                    {/* 字幕部分 */}
                    <View className={voiceStyle.subtitle_box_container}>
                        <View className={voiceStyle.minHeight}>
                            {status !== VoiceStatus.Start &&
                                status !== VoiceStatus.Complete &&
                                status !== VoiceStatus.Waiting &&
                                !isOver.current && <View className={voiceStyle.subtitle_box}>{subtitleText}</View>}
                        </View>
                        <View className={voiceStyle.status_box} onClick={statusTextClick}>
                            {status === VoiceStatus.Speaking ? (
                                <Block>
                                    <View className={voiceStyle.animation}>{statusLoading}</View>
                                    {voiceInteraction === VoiceInteraction.Auto && (
                                        <View className={voiceStyle.status_tip}>点击可打断</View>
                                    )}
                                </Block>
                            ) : (
                                <Block>
                                    {status === VoiceStatus.Listening ? (
                                        <Block>
                                            <VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />
                                        </Block>
                                    ) : (
                                        <Block>
                                            <View className={voiceStyle.animation}>{statusLoading}</View>
                                        </Block>
                                    )}

                                    <View
                                        className={
                                            status === VoiceStatus.Speaking || status === VoiceStatus.Cancel
                                                ? voiceStyle.status_lite
                                                : voiceStyle.status
                                        }
                                    >
                                        {/* 我在听等等状态 */}
                                        {statusText?.map((item) => (
                                            <View key={item} className={voiceStyle.status_text}>
                                                {item}{' '}
                                                {voiceInteraction === VoiceInteraction.Auto &&
                                                status === VoiceStatus.Listening
                                                    ? `${recognizerTime}s`
                                                    : ''}
                                            </View>
                                        ))}
                                    </View>
                                </Block>
                            )}
                        </View>
                    </View>
                </View>
                <View className={voiceStyle.footer} id='actionfooter'>
                    <View className={voiceStyle.time}>{timeLeftText}</View>
                    <View className={voiceStyle.actions}>
                        <View className={voiceStyle.action_chat} onClick={handleClose}>
                            <Image
                                src={
                                    status === VoiceStatus.BeforeListen ||
                                    status === VoiceStatus.Listening ||
                                    status === VoiceStatus.Overtime ||
                                    status === VoiceStatus.WaitSend ||
                                    status === VoiceStatus.AllowSend ||
                                    status === VoiceStatus.Waiting
                                        ? IconChatDisabled
                                        : IconChat
                                }
                                className={voiceStyle.action_chat_icon}
                            />
                        </View>
                        {/* 字幕按钮 */}
                        {/* <View className={voiceStyle.action_chat} onClick={toggleSubtitle}>
                            <Image
                                src={subtitleShow === SubtitleVisible.Show ? IconSubtitleOn : IconSubtitleOff}
                                className={voiceStyle.action_chat_icon}
                            />
                        </View> */}
                        {aiHelpEnable && (
                            <View className={voiceStyle.action_chat} onClick={handleAIHelp}>
                                <Image
                                    src={
                                        status === VoiceStatus.Waiting || status === VoiceStatus.Speaking
                                            ? IconHelpOff
                                            : IconHelp
                                    }
                                    className={voiceStyle.action_chat_icon}
                                />
                            </View>
                        )}
                        {data?.eman?.show3dFlag && data?.eman.zipFileUrl && (
                            <View className={voiceStyle.action_chat} onClick={handleChatMode}>
                                <Image
                                    src={showVideoClose === ChatMode.AUDIO ? IconVideoClose : IconVideo}
                                    className={voiceStyle.action_chat_icon}
                                />
                            </View>
                        )}
                        <View className={voiceStyle.action_chat} onClick={showMore}>
                            <Image src={IconMore} className={voiceStyle.action_chat_icon} />
                        </View>
                    </View>
                    {status === VoiceStatus.Complete ? (
                        <View className={voiceStyle.action_say_box}>
                            <View
                                className={classnames(
                                    voiceStyle.action_say,
                                    voiceStyle.action_say_active,
                                    voiceStyle.action_say_complete
                                )}
                                onClick={handleComplete}
                            >
                                完成
                            </View>
                        </View>
                    ) : (
                        <Block>
                            {voiceInteraction === VoiceInteraction.Auto && (
                                <View
                                    className={voiceStyle.action_close}
                                    onClick={() => {
                                        addLog({
                                            level: 'debug',
                                            message: '自动模式，点击挂断',
                                            data: {
                                                status: statusRef.current
                                            }
                                        });
                                        handleFinish();
                                    }}
                                >
                                    <Image src={IconHangup} className={voiceStyle.action_close_icon} />
                                </View>
                            )}
                            {voiceInteraction === VoiceInteraction.Manual && (
                                <View className={voiceStyle.action_say_box}>
                                    <Image
                                        src={IconHangupMini}
                                        className={voiceStyle.action_finish_icon_mini}
                                        onClick={() => {
                                            addLog({
                                                level: 'debug',
                                                message: '手动模式，点击挂断',
                                                data: {
                                                    status: statusRef.current
                                                }
                                            });
                                            handleFinish();
                                        }}
                                    />
                                    <View
                                        className={classnames(voiceStyle.action_say, {
                                            [voiceStyle.action_say_disabled]: status === VoiceStatus.Start,
                                            [voiceStyle.action_say_active]:
                                                status === VoiceStatus.WaitListen || status === VoiceStatus.Speaking,

                                            [voiceStyle.action_say_pressed]:
                                                sayPressed || status === VoiceStatus.Listening,
                                            [voiceStyle.action_say_cancel]: status === VoiceStatus.Cancel
                                        })}
                                        onTouchStart={handleTouchStart}
                                        onTouchMove={handleTouchMove}
                                        onTouchEnd={handleTouchEnd}
                                    >
                                        <Image
                                            src={
                                                status === VoiceStatus.Start || status === VoiceStatus.Waiting
                                                    ? IconSpeakDisabled
                                                    : IconSpeak
                                            }
                                            className={voiceStyle.action_say_icon_speak}
                                        />
                                        按住说话
                                    </View>
                                </View>
                            )}
                            {voiceInteraction === VoiceInteraction.TapManual && (
                                <View className={voiceStyle.action_say_box}>
                                    {status === VoiceStatus.AllowSend ? (
                                        <Block>
                                            <Image
                                                src={IconDrop}
                                                className={voiceStyle.action_finish_icon_mini}
                                                onClick={handleTapDrop}
                                            />
                                            <View
                                                className={classnames(
                                                    voiceStyle.action_say,
                                                    voiceStyle.action_say_active
                                                )}
                                                onClick={handleTapSend}
                                            >
                                                <Image className={voiceStyle.action_say_icon_speak} src={IconFly} />
                                                发送
                                            </View>
                                        </Block>
                                    ) : (
                                        <Block>
                                            <Image
                                                src={IconHangupMini}
                                                className={voiceStyle.action_finish_icon_mini}
                                                onClick={handleFinish}
                                            />
                                            <View
                                                className={classnames(voiceStyle.action_say, {
                                                    [voiceStyle.action_say_disabled]:
                                                        status === VoiceStatus.Start || status === VoiceStatus.WaitSend,
                                                    [voiceStyle.action_say_active]:
                                                        status === VoiceStatus.WaitListen ||
                                                        status === VoiceStatus.BeforeListen ||
                                                        status === VoiceStatus.Speaking ||
                                                        status === VoiceStatus.Listening
                                                })}
                                                onClick={handleTapSay}
                                            >
                                                <Image
                                                    src={
                                                        status === VoiceStatus.Listening
                                                            ? IconStop
                                                            : status === VoiceStatus.Start ||
                                                              status === VoiceStatus.Waiting ||
                                                              status === VoiceStatus.WaitSend
                                                            ? IconSpeakDisabled
                                                            : IconSpeak
                                                    }
                                                    className={voiceStyle.action_say_icon_speak}
                                                />
                                                {status === VoiceStatus.Listening ? '停止录音' : '开始录音'}
                                            </View>
                                        </Block>
                                    )}
                                </View>
                            )}
                        </Block>
                    )}

                    <View className={voiceStyle.tip}>对话为AI情景模拟，非真实场景</View>
                </View>

                <Aihelp
                    show={aiHelpShow}
                    keyHeight={keyHeight}
                    chatId={chatId}
                    styleType='voice'
                    canGenerate={canGenerate}
                    onClose={handleAiHelpClose}
                />
            </View>

            <FeedbackDialog
                show={feedbackShow}
                keyHeight={keyHeight}
                onClose={() => setFeedbackShow(false)}
                onConfirm={submitFeedback}
            />
            <FeedbackSucess show={feedbackSuccessShow} onClose={() => setFeedbackSuccessShow(false)} />
            <RecordDialog show={recordDialogShow} onClose={() => setRecordDialogShow(false)} onConfirm={handleDrop} />
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='A' />
            <Dialog_ />
        </Block>
    );
};

export default Index;
