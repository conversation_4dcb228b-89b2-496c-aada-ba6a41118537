@import '@/styles/index.less';

page {
    background-color: #fff;
    --form-background-color: transparent;
    --form-label-width: 0;
    --form-controll-margin-left: 0;
}
:global {
    .van-cell  {
      border-radius: 32px;
      background: #FAFAFA;
      margin-bottom: 24px;
    }
    .van-popup__close-icon  {
      color: #272C47;
      top: 62px;
      right: 42px;
      width: 26px;
      height: 26px;
    }
  }
.page {
    height: 100vh;
}
// .test{
//     position: fixed;
//     z-index: 999;
//     background: #333;
//     width: 100px;
//     height: env(safe-area-inset-top);
// }
.topBox{
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #fff;
    z-index: 1;
}
.navbar {
    z-index: 0;
}
.noticeBar {
    .flex-center;
    .font(24px,#67686F,400);

    // position: fixed;
    // top: 0;
    left: 0;
    z-index: 99;
    width: 100%;
    height: 70px;
    background: #FAFCFF;

}
.question_time {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #F0F6FF;
    color: #4F66FF;
    height: 60px;
    width: 100%;
    z-index: 99;
    box-sizing: border-box;
    font-size: 28px;
    padding: 0 32px;
}
.countDownBox{
    .flex-center;
    .countDown{
        margin-left: 20px;
        width: 120px;
        height: 56px;
        flex-shrink: 0;
        border-radius: 180px;
        background: #EDEDED;
        text-align: center;
        color: #333;
        font-size: 24px;
        line-height: 56px;
    }
}
.bottomContentAihelp{
    position: fixed;
    top: 0;
    // padding-bottom: env(safe-area-inset-bottom);
    z-index: 2;
 
    height: 100vh;
    width: 100%;
}
.footer{
    height: env(safe-area-inset-bottom);
}
.bottomContent{
    position: fixed;
    bottom: 0;
    // padding-bottom: env(safe-area-inset-bottom);
    left: 0;
    z-index: 2;
    width: 100%;
    // background-color: #fff;
}


.showScripts{
    width: 204px;
    height: 64px;
    flex-shrink: 0;
    border-radius: 98px;
    border: 1px solid #EDEDED;
    background: #FFF;
    box-shadow: 0 4px 8px 0 #0000000d;
    margin: 0 0 28px 32px;
    .flex-center;
    text{
        margin-left: 10px;
        color: #323233;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
    }
}

.showScripts_Ai
{
    background: #dddddd;
    text{
        color: #ffffff;
    }
}
.sendMessage {
    .wrap-box(32px 32px 32px 32px);

    // padding-bottom: 20px; 
    // padding-bottom: calc(20px + constant(safe-area-inset-bottom)); 
    // padding-bottom: calc(20px + env(safe-area-inset-bottom)); 
    flex:1;
    background-color: #fff;
    // border: 1px solid #000;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.05);
    &_tel_view{
        height: 108px;
        width: 88px;
       .flex-center;
    }
    &_tel_image{
        width: 44px;
        height: 44px;
        // width: 68px;
        // height: 68px;
        flex: 0 0 auto;
    }
    &_box {
        .wrap-box(10px 20px);

        flex:1;
        margin-left: 10px;
        align-items: center;
        background: #f6f6f6;
        border-radius: 47px;
        padding: 20px;
        // position: relative;
        &_disabled {
            background: #ececec;
        }
    }

    &_input {
        // .font(28px,#272c47,400);
        // min-height: 88px - 40px;
        // line-height: 42px;
        // max-height: 42 * 5px;
        font-size: 28px;
        color:#272c47;
        font-weight: 400;
        align-items: center;
        width: 465px;
        // flex: 1;
        line-height: 35px; // 安卓上textarea的placeholder的行高固定1.25无法改变
        max-height: 175px; // 展示五行

       
    }
    &_send {
        display: inline-block;
        width: 68px;
        height: 68px;
        background-color: transparent;
        vertical-align: middle;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
    }
    &_send_image {
        width: 68px;
        height: 68px;
    }
}

.actionSheet {
    &_hide_close {
        :global {
            .van-action-sheet__close {
                display: none !important;
            }
        }
    }
    &_group {
        .wrap-box(12px 62px 54px 62px);
    }
}
.complete_icon {
    width: 88px;
    height: 88px;
}

.cancel_icon {
    width: 56px;
    height: 56px;
}
.Ai_help{display: flex;
}

