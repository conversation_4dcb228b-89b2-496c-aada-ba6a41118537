import { Storage, useRequest, useStorage } from '@/common';
import Aihel<PERSON> from '@/components/aihelp';
import ChatHistory from '@/components/chatHistory';
import FeedbackDialog from '@/components/feedbackDialog';
import FeedbackSucess from '@/components/feedbackSucess';
import HarmonyDialog from '@/components/HarmonyDialog';
import config from '@/config';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import { ChatMode, RecognizeStatus, VoiceInteraction, VoiceStatus } from '@/constants/voicetype';
import { useChatStream } from '@/hooks';
import { doneChat, doneGenerateReport, generateReport, getAllChatHistory, saveIntroduction } from '@/services/chat';
import { getServerTime, uploadLog } from '@/services/common';
import { audioUploadComplete, cosUpload } from '@/services/cos';
import type { ChatVO, HistoryVO } from '@/types/chat';
import { ChatActionSheetType, RoleEnum } from '@/types/chat';
import type { FeedbackData, LogInfo } from '@/types/common';
import { checkHarmony } from '@/utils/permission';
import { ActionSheet, Dialog, Form, FormItem, Icon, Image, Loading, NavBar, Overlay, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Text, Textarea, View } from '@tarojs/components';
import Taro, { getCurrentPages, pxTransform, useRouter } from '@tarojs/taro';
import { useCountDown, useMount, useThrottleFn, useUnmount } from 'ahooks';
import classnames from 'classnames';
import dayjs from 'dayjs';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import '../../../assets/vant.global.less';
import ActionMore from '../actionMore/index';
import ActionVoice from '../actionVoice/index';
import styles from '../dialog.less';
import VoiceDialogueSkill from '../voiceDialogueSkill';
const send_0 = `${config.cdnPrefix}dialogue/send_0.png`;
const send_1 = `${config.cdnPrefix}dialogue/send_1.png`;
const TelImg = `${config.cdnPrefix}dialogue/tel.png`;
const scriptImg = `${config.cdnPrefix}sceneExercise/script.png`;
const IconHelpBlue = `${config.cdnPrefix}aihelp/icon_help_blue.svg`;
const Toast_ = Toast.createOnlyToast();
type Props = {
    chatId: string;
    data?: ChatVO;
    interrupt: string;
    from?: string;
    mode?: string;
    introductionType: number;
    introductionDelay: number;
    introduction: string;
    onShowScript: () => void;
    onLoaded: () => void;
    texturl: string;
};
const Dialog_ = Dialog.createOnlyDialog();

const Index: React.FC<Props> = (props) => {
    const {
        chatId,
        interrupt,
        from,
        data,
        onShowScript,
        onLoaded,
        introductionType,
        introductionDelay,
        introduction,
        texturl
    } = props;
    const userInfo = Storage.get(StorageEnvKey.USERINFO);
    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);

    const time = useRef(0); // 便于开发对话页
    const [history, setHistory] = useState<HistoryVO[]>([]);
    const historyRef = useRef<HistoryVO[]>([]);
    const [assistantMessage, setAssistantMessage] = useState<string>();
    const [canSend, setCanSend] = useState(false);
    const [chatActionSheetType, setChatActionSheetType] = useState<ChatActionSheetType>();
    const [showActionSheet, setShowActionSheet] = useState(false);
    const [leftTime, setLeftTime] = useState<number>();
    const [keyHeight, setKeyHeight] = useState(0);
    const [translateHeight, setTranslateHeight] = useState(0);
    const [showScriptBtn, setShowScriptBtn] = useState(true);
    const [aiHelpEnable, setAiHelpEnable] = useState(true);
    const [aiHelpShow, setAiHelpShow] = useState<boolean>(false);
    const [canGenerate, setCanGenerate] = useState<boolean>(true);
    const [customTitle, setCustomTitle] = useState({ title: '' });
    const voicePageRef = useRef<any>();
    const chatData = useRef({ chatReportId: '', done: false }); // 或者直接用chat里的数据，不需要chatData
    const [showVoicePage, setShowVoicePage] = useState(false); // 语音界面显示
    const showVoicePageRef = useRef<boolean>(false);
    const createTime = useRef('');
    const serverTime = useRef('');
    const [showMoreActionSheet, setShowMoreActionSheet] = useState(false);
    const [showVoiceActionSheet, setShowVoiceActionSheet] = useState(false);
    const isTimeout = useRef<boolean>(false);
    const helloTimer = useRef<any>();
    const voiceInteractionRef = useRef<VoiceInteraction>(VoiceInteraction.Manual);

    const uploadData = useRef<{
        ossConfig?: {
            audioFileName: string;
            path: string;
            uploadUrl: string;
            mimeType: string;
        };
        userSentence?: {
            text: string;
            filePath?: string;
            fileSize?: number;
        };
    }>({
        ossConfig: undefined,
        userSentence: undefined
    });

    const [feedbackShow, setFeedbackShow] = useState<boolean>(false);
    const [feedbackSuccessShow, setFeedbackSuccessShow] = useState<boolean>(false);
    const feedbackData = useRef<FeedbackData>({
        client: 'wechat',
        version: '',
        brand: '',
        wxVersion: '',
        SDKVersion: '',
        model: '',
        system: '',
        platform: '',
        environment: '',
        microphoneAuthorized: undefined,
        appId: '',
        name: '',
        phone: '',
        company: '',
        path: '',
        chatId: '',
        description: '',
        logs: []
    });

    const audioUploading = useRef<boolean>(false);
    const isHarmonyRef = useRef(false);
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    const logs = useRef<{ action: string; content: string; time: string }[]>([]);

    const formIt = Form.useForm();
    let bottomContent_height = 0;
    let topBox_height = 0;
    let window_height = 0;

    const onScrollView = () => {
        Taro.nextTick(() => {
            Taro.pageScrollTo({
                selector: '#footer'
            });
        });
    };

    const saveLogs = (log: { action: string; content: string }) => {
        logs.current.push({ ...log, time: dayjs().format('HH:mm:ss.SSS') });
    };

    const addLog = (log: LogInfo) => {
        const params = {
            ...log,
            time: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
        };
        console.log('log', params);
        feedbackData.current.logs.push(params);
    };

    const handleFeedback = () => {
        setShowMoreActionSheet(false);
        setFeedbackShow(true);
    };
    const submitFeedback = async (values: { description: string }) => {
        feedbackData.current.description = values.description;
        try {
            const res = await uploadLog(JSON.stringify(feedbackData.current));
            if (res.data.data) {
                feedbackData.current.logs = [];
                feedbackData.current.description = '';
                setFeedbackSuccessShow(true);
            }
        } catch (error) {
            Taro.showToast({
                title: '提交失败',
                icon: 'none'
            });
        }
    };

    function getTimeout(): boolean {
        return isTimeout.current;
    }

    async function doneActionSheet() {
        try {
            const res = await doneChat(chatId, false);
            if (res.data.code === 200) {
                if (showVoicePageRef.current) {
                    // 语音的情况
                    voicePageRef.current?.finish();
                }
                chatData.current.done = true;
                setChatActionSheetType(ChatActionSheetType.OFF);
                setShowActionSheet(true);
            }
        } catch (error) {
            addLog({
                level: 'error',
                message: 'countdown end request doneChat error',
                data: {
                    error
                }
            });
        }
    }

    async function handleTimeOut() {
        addLog({
            level: 'debug',
            message: 'handleTimeOut',
            data: {
                showVoicePage: showVoicePageRef.current,
                voiceInteraction: voiceInteractionRef.current
            }
        });
        setCanGenerate(false);
        if (showVoicePageRef.current) {
            voicePageRef.current?.handleTimeOut();
        } else {
            doneActionSheet();
        }
    }

    const { run: runPollingServerTime, cancel } = useRequest(() => getServerTime(), {
        pollingInterval: 30000, // 每隔30s更新一次当前时间，避免篡改手机本地时间导致倒计时错误
        manual: true,
        onSuccess: async (data) => {
            serverTime.current = data;
            // console.log('getServerTime-createTime', createTime.current);
            if (createTime.current) {
                const leftTime =
                    dayjs(createTime.current).add(time.current, 'm').valueOf() - dayjs(serverTime.current).valueOf();
                // const leftTime = Math.floor(Math.random() * (60000 - 10000 + 1) + 10000);
                addLog({
                    level: 'debug',
                    message: 'getServerTime-leftTime',
                    data: {
                        createTime: createTime.current,
                        serverTime: serverTime.current,
                        leftTime
                    }
                });
                setLeftTime(leftTime);
                if (leftTime <= 0) {
                    cancel();
                    if (!isTimeout.current) {
                        isTimeout.current = true;
                        handleTimeOut();
                    }
                }
            }
        }
    });

    const [, formattedRes] = useCountDown({
        // 此处默认且只能使用当前系统时间，如何拿服务器时间去做纠正 => 使用leftTime代替targetDate
        leftTime,
        // targetDate,
        onEnd: async () => {
            addLog({
                level: 'trace',
                message: 'countdown end'
            });
            if (!isTimeout.current) {
                isTimeout.current = true;
                handleTimeOut();
            }
        }
    });

    const { minutes, seconds } = formattedRes;
    // console.log(minutes, seconds);

    // 为实现从历史页进入对话页时候能滚动底部
    useEffect(() => {
        onScrollView();
    }, [history]);

    let assistantMessageVal = '';
    let startTimeArr: number[] = [];
    let endTime = 0;
    const {
        loading: chatStreamLoading,
        run: chatStreamRun,
        resetSegmentCount
    } = useChatStream({
        onProcess: (text) => {
            console.log('onProcess-text', text, new Date().toLocaleString());
            if (isTimeout.current) {
                return;
            }
            if (showVoicePageRef.current) {
                if (assistantMessageVal.length === 0) {
                    voicePageRef.current.speakStart();
                }
                saveLogs({
                    action: 'sse接收',
                    content: text
                });
                voicePageRef.current?.speakProcess(text); // 传给语音合成
                assistantMessageVal += text;
            } else {
                assistantMessageVal += text;
                startTimeArr.push(new Date().getTime());
                setShowScriptBtn(false);
            }
            setAssistantMessage((message) => {
                const content = message ?? '';
                // console.log('setAssistantMessage', content, text);
                return content + text;
            });
            Taro.nextTick(() => {
                onScrollView();
            });
        },
        onEnd: () => {
            addLog({
                level: 'debug',
                message: 'useChatStream onEnd',
                data: {
                    assistantMessageVal,
                    showVoicePage: showVoicePageRef.current
                }
            });
            saveLogs({
                action: 'sse结束',
                content: ''
            });
            if (isTimeout.current) {
                return;
            }
            if (showVoicePageRef.current) {
                voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                assistantMessageVal = '';
                startTimeArr = [];
                endTime = 0;
                setAssistantMessage((assistantMessage) => {
                    setHistory((history) => {
                        const newHistory = [
                            ...history,
                            { role: RoleEnum.ASSISTANT, content: assistantMessage as string }
                        ];
                        historyRef.current = newHistory;
                        return newHistory;
                    });
                    return undefined;
                });
                setShowScriptBtn(true);
            } else {
                // time = 50ms*总字长 - 第一次onProcess时间点到onEnd时间点的时间差
                endTime = new Date().getTime();
                const timeUsed = endTime - startTimeArr[0];
                // console.log('timeUsed', timeUsed);
                const timeNeed = assistantMessageVal.split('').length * 50;
                // console.log('timeNeed', timeNeed);
                const interval = setInterval(() => {
                    onScrollView();
                }, 50 * 10); // 50ms*10个字一行
                // todo useInterval useTimeout
                setTimeout(() => {
                    // console.log('assistantMessageVal', assistantMessageVal);
                    assistantMessageVal = '';
                    startTimeArr = [];
                    endTime = 0;
                    clearInterval(interval);
                    setAssistantMessage((assistantMessage) => {
                        setHistory((history) => {
                            const newHistory = [
                                ...history,
                                { role: RoleEnum.ASSISTANT, content: assistantMessage as string }
                            ];
                            historyRef.current = newHistory;
                            return newHistory;
                        });
                        return undefined;
                    });
                }, timeNeed - timeUsed + 1000);
                setTimeout(() => {
                    setShowScriptBtn(true);
                }, timeNeed - timeUsed + 1000);
            }
            setCustomTitle({
                title: data?.eman?.name ?? ''
            });
        },
        onHeadersReceived: (res: any) => {
            console.log('onHeadersReceived', res);
            const uploadUrl = res.header['upload-url'];
            const { mimeType } = res.header;
            const path = res.header['key'];
            if (uploadData.current.ossConfig) {
                uploadData.current.ossConfig.uploadUrl = uploadUrl;
                uploadData.current.ossConfig.path = path;
                uploadData.current.ossConfig.mimeType = mimeType;
            }
            if (uploadData.current.ossConfig && uploadData.current.ossConfig.audioFileName) {
                if (uploadData.current.userSentence?.filePath) {
                    const userSentence_n = { ...uploadData.current.userSentence };
                    const ossConfig_n = { ...uploadData.current.ossConfig };
                    sentenceRecognize(userSentence_n, ossConfig_n);
                }
            }
            uploadData.current.ossConfig = undefined;
            uploadData.current.userSentence = undefined;
        },
        onError: (error) => {
            if (error.errMsg) {
                if (error.errMsg.includes('timeout')) {
                    Taro.showToast({
                        title: '当前访问人数过多，响应有点慢',
                        icon: 'none'
                    });
                } else if (error.errMsg.includes('limit')) {
                    Taro.showToast({
                        title: '当前访问人数过多，请稍后重试',
                        icon: 'none'
                    });
                } else if (error.errMsg.includes('401')) {
                    Taro.showToast({
                        title: '登录已失效，请重新登录',
                        icon: 'none'
                    });
                }
            } else {
                Taro.showToast({
                    title: '对话错误，请重试',
                    icon: 'none'
                });
            }
            if (showVoicePageRef.current) {
                voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                voicePageRef.current?.startListen();
            }
            assistantMessageVal = '';
            startTimeArr = [];
            endTime = 0;
            setShowScriptBtn(true);
            setAssistantMessage(undefined);

            addLog({
                level: 'error',
                message: 'chatStreamRun',
                data: {
                    error
                }
            });
        }
    });

    async function sentenceRecognize(userSentence: any, ossConfig: any) {
        const { text, filePath, fileSize } = userSentence;
        addLog({
            level: 'debug',
            message: 'sentenceRecognize',
            data: {
                text,
                filePath,
                fileSize
            }
        });
        // changeStatus(VoiceStatus.Loading);
        if (ossConfig.path) {
            audioUploading.current = true;
            cosUpload(ossConfig, filePath)
                .then(async (data: any) => {
                    // 上传成功
                    console.info('cosUpload Success', data);
                    addLog({
                        level: 'trace',
                        message: 'cosUpload Success'
                    });
                    try {
                        await audioUploadComplete(ossConfig.path);
                        audioUploading.current = false;
                    } catch (error) {
                        audioUploading.current = false;
                        addLog({
                            level: 'error',
                            message: 'audioUploadComplete',
                            data: {
                                error
                            }
                        });
                    }
                })
                .catch((error: any) => {
                    audioUploading.current = false;
                    addLog({
                        level: 'error',
                        message: 'cosUpload',
                        data: {
                            error
                        }
                    });
                });
        } else {
            addLog({
                level: 'warn',
                message: 'cosUpload no text'
            });
        }
    }

    // 取消开场白
    const cancelHello = () => {
        addLog({
            level: 'trace',
            message: 'cancelHello'
        });
        if (helloTimer.current) {
            clearTimeout(helloTimer.current);
            helloTimer.current = undefined;
        }
    };

    const handleSend = async (m: string, text?: string) => {
        console.log('userInfo', userInfo);
        const content = m === 'text' ? text : uploadData.current.userSentence?.text;
        addLog({
            level: 'debug',
            message: 'handleSend',
            data: {
                m,
                content
            }
        });
        setHistory((history) => {
            const newHistory = [...history, { role: RoleEnum.USER, content, avatar: userInfo?.avatar }];
            return newHistory;
        });
        formIt.resetFields();
        setCanSend(false);
        setCustomTitle({
            title: '对方正在输入...'
        });
        const params: any = { chatId, message: text };
        if (isWework === '1') {
            params.responseId = chatId + Date.now();
        }
        if (m === 'audio') {
            const audioFileName = `${chatId}_${Date.now()}`;
            const ossConfig = {
                audioFileName,
                path: '',
                uploadUrl: '',
                mimeType: ''
            };
            console.log('ossConfig', ossConfig, uploadData.current);
            uploadData.current.ossConfig = ossConfig;
            params['audioFileName'] = audioFileName;
        }
        chatStreamRun(params);
        onScrollView();
    };

    const onSend = () => {
        const message = formIt.getFieldValue('message');
        addLog({
            level: 'debug',
            message: 'onSend',
            data: {
                message
            }
        });
        if (!message.trim()) {
            // Toast_.show未出现
            // console.log('请输入内容');
            // Toast_.show({
            //     message: '请输入内容'
            // });
            return;
        }
        cancelHello();
        handleSend('text', message);
    };

    const chatEnd = async () => {
        addLog({
            level: 'trace',
            message: 'chatEnd'
        });
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
        const recognizerStatus = voicePageRef.current.getRecognizerStatus();
        if (recognizerStatus === RecognizeStatus.SentenceBegin || recognizerStatus === RecognizeStatus.SentenceChange) {
            voicePageRef.current.stop();
        }
        /* try {
            const res = await doneChat(chatId, false);
            if (res.data.code === 200) {
                chatData.current.done = true;
                setShowActionSheet(false);
                Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                if (texturl && texturl == 'challenge') {
                    Taro.switchTab({
                        url: '/pages/mini/index'
                    });
                } else {
                    const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                    if (homePath) {
                        Taro.switchTab({ url: homePath });
                    } else {
                        Taro.redirectTo({
                            url: HomePath.MIDDLE
                        });
                    }
                }
            }
        } catch (error) {
            addLog({
                level: 'error',
                message: 'chatEnd doneChat',
                data: {
                    error
                }
            });
        }*/

        const checkRewriteTimer = setInterval(async () => {
            console.log('checkRewriteTimer', audioUploading.current);
            if (!audioUploading.current) {
                clearInterval(checkRewriteTimer);
                console.info('结束，并生成报告');

                try {
                    const res = await doneChat(chatId, false);
                    Taro.hideLoading();
                    if (res.data.code === 200) {
                        chatData.current.done = true;
                        setShowActionSheet(false);
                        Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                        if (texturl && texturl == 'challenge') {
                            Taro.switchTab({
                                url: '/pages/mini/index'
                            });
                        } else {
                            const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                            if (homePath) {
                                Taro.switchTab({ url: homePath });
                            } else {
                                Taro.redirectTo({
                                    url: HomePath.MIDDLE
                                });
                            }
                        }
                    }
                } catch (error) {
                    addLog({
                        level: 'error',
                        message: 'chatEnd doneChat',
                        data: {
                            error
                        }
                    });
                    Taro.hideLoading();
                    Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                    if (texturl && texturl == 'challenge') {
                        Taro.switchTab({
                            url: '/pages/mini/index'
                        });
                    } else {
                        const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                        if (homePath) {
                            Taro.switchTab({ url: homePath });
                        } else {
                            Taro.redirectTo({
                                url: HomePath.MIDDLE
                            });
                        }
                    }
                }
            }
        }, 100);
    };

    const openReport = async () => {
        // addLog({
        //     level: 'trace',
        //     message: 'openReport'
        // });
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
        const recognizerStatus = voicePageRef.current.getRecognizerStatus();
        if (recognizerStatus === RecognizeStatus.SentenceBegin || recognizerStatus === RecognizeStatus.SentenceChange) {
            voicePageRef.current.stop();
        }
        /* try {
            if (chatData.current.done) {
                const res = await generateReport(chatId, true);
                if (res.data.code === 200) {
                    Taro.redirectTo({
                        url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                    });
                }
            } else {
                const res = await doneGenerateReport(chatId, true);
                if (res.data.code === 200) {
                    Taro.redirectTo({
                        url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                    });
                }
            }
        } catch (error) {
            addLog({
                level: 'error',
                message: 'openReport doneChat',
                data: {
                    error
                }
            });
        }*/

        const checkRewriteTimer = setInterval(async () => {
            console.log('checkRewriteTimer', audioUploading.current);

            if (!audioUploading.current) {
                clearInterval(checkRewriteTimer);
                console.info('结束，并生成报告');
                try {
                    Taro.hideLoading();
                    if (chatData.current.done) {
                        const res = await generateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    } else {
                        const res = await doneGenerateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    }
                } catch (error) {
                    addLog({
                        level: 'error',
                        message: 'openReport doneChat',
                        data: {
                            error
                        }
                    });
                    console.log(error);
                    Taro.hideLoading();
                    if (chatData.current.done) {
                        const res = await generateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    } else {
                        const res = await doneGenerateReport(chatId, true);
                        if (res.data.code === 200) {
                            Taro.redirectTo({
                                url: `/pages/chat/report/index?id=${chatData.current.chatReportId}&to=home`
                            });
                        }
                    }
                }
            }
        }, 100);
    };

    function goBack() {
        setChatActionSheetType(ChatActionSheetType.FINISH);
        setShowActionSheet(true);
    }

    Taro.useUnload(() => {
        resetSegmentCount();
        feedbackData.current.description = '自动上传日志';
        Taro.eventCenter.trigger('uploadLogs', JSON.stringify(feedbackData.current));
        Storage.set(StorageEnvKey.REFRESH_HOME, 1);
    });

    const actionSheet = useMemo(() => {
        if (chatActionSheetType === ChatActionSheetType.FINISH) {
            return {
                title: '确认结束练习吗？',
                actions: [
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.COMPLETE) {
            return {
                title: '练习结束，对话已关闭',
                actions: [
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.INTERRUPT) {
            return {
                title: '练习中断，是否继续？',
                actions: [
                    {
                        text: '继续对话',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: () => {
                                setShowActionSheet(false);
                                // 打断后，语音重新开始
                                handleVoicePage();
                                startListen();
                            }
                        }
                    },
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: '#F6F6F6',
                            style: { color: '#777777' },
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
        if (chatActionSheetType === ChatActionSheetType.OFF) {
            return {
                title: '练习时间结束，对话已关闭',
                actions: [
                    {
                        text: '生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
    }, [chatActionSheetType]);

    // 显示语音界面
    function handleVoicePage() {
        setShowVoicePage(true);
        showVoicePageRef.current = true;
        console.log('endTime', Date.now());

        addLog({
            level: 'trace',
            message: 'handleVoicePage'
        });
    }

    function startListen() {
        voicePageRef.current?.startListen();
        addLog({
            level: 'trace',
            message: 'startListen'
        });
    }

    const onVoiceClick = useCallback(() => {
        // if (!chatData.current.done && !(minutes <= 0 && seconds <= 0)) {

        // }
        console.log(
            'onVoiceClick',
            data?.eman.zipFileUrl,
            data?.eman.show3dFlag,
            isHarmonyRef.current,
            Storage.get(StorageEnvKey.CHAT_MODE)
        );
        addLog({
            level: 'trace',
            message: 'onVoiceClick'
        });
        if (data?.eman.zipFileUrl && data?.eman.show3dFlag) {
            if (
                isHarmonyRef.current ||
                Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null
            ) {
                const url = `/pages/shuziren/index?chatId=${data?.id}&from=skill`;
                Taro.redirectTo({
                    url
                });
            } else {
                handleVoicePage();
                startListen();
            }
        } else {
            if (isHarmonyRef.current) {
                setShowHarmonyDialog(true);
            } else {
                handleVoicePage();
                startListen();
            }
        }
    }, [data]);

    const showMore = () => {
        setShowMoreActionSheet(true);
    };

    const showActionSheetVoiceSetting = () => {
        setShowMoreActionSheet(false);
        setShowVoiceActionSheet(true);
    };

    const voicePopupConfirm = () => {
        Dialog_.alert({
            title: '修改成功',
            message: '下次练习时生效',
            confirmButtonText: '关闭',
            confirmButtonColor: '#4F66FF'
        }).then(() => {});
    };

    // 语音一句话回调
    const handleVoiceSentence = (usc: { text: string; filePath?: string; fileSize?: number }) => {
        // saveLogs({
        //     action: '发送识别文字',
        //     content: sentence
        // });
        uploadData.current.userSentence = usc;
        handleSend('audio', usc.text);
        if (isTimeout.current) {
            doneActionSheet();
        }
    };

    const saveIntro = async (chatId: string) => {
        addLog({
            level: 'debug',
            message: 'saveIntro',
            data: {
                introduce: introduction
            }
        });
        try {
            voicePageRef.current.changeStatus(VoiceStatus.Waiting);
            const saveIntroRes = await saveIntroduction(chatId);
            if (saveIntroRes.data) {
                if (showVoicePageRef.current) {
                    // 语音状态，播放并保存到对话历史
                    voicePageRef.current.speakStart();
                    voicePageRef.current?.speakProcess(introduction); // 传给语音合成
                    voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                    assistantMessageVal = '';
                    startTimeArr = [];
                    endTime = 0;
                    setHistory((history) => {
                        const newHistory = [...history, { role: RoleEnum.ASSISTANT, content: introduction }];
                        historyRef.current = newHistory;
                        return newHistory;
                    });
                } else {
                    // 非语音状态
                    assistantMessageVal = introduction;

                    startTimeArr.push(new Date().getTime());
                    setShowScriptBtn(false);
                    setAssistantMessage(introduction);
                    Taro.nextTick(() => {
                        onScrollView();
                    });
                    endTime = new Date().getTime();
                    const timeUsed = endTime - startTimeArr[0];
                    // console.log('timeUsed', timeUsed);
                    const timeNeed = assistantMessageVal.split('').length * 50;
                    // console.log('timeNeed', timeNeed);
                    const interval = setInterval(() => {
                        onScrollView();
                    }, 50 * 10); // 50ms*10个字一行
                    // todo useInterval useTimeout
                    setTimeout(() => {
                        // console.log('assistantMessageVal', assistantMessageVal);
                        assistantMessageVal = '';
                        startTimeArr = [];
                        endTime = 0;
                        clearInterval(interval);
                        setAssistantMessage((assistantMessage) => {
                            setHistory((history) => {
                                const newHistory = [
                                    ...history,
                                    { role: RoleEnum.ASSISTANT, content: assistantMessage as string }
                                ];
                                historyRef.current = newHistory;
                                return newHistory;
                            });
                            return undefined;
                        });
                    }, timeNeed - timeUsed + 1000);
                    setTimeout(() => {
                        setShowScriptBtn(true);
                    }, timeNeed - timeUsed + 1000);
                }
            } else {
                startListen();
            }
        } catch (error) {
            startListen();
        }
    };

    useMount(async () => {
        const sysinfo = Taro.getSystemInfoSync();
        console.log('-sysinfo-', sysinfo);
        try {
            const pages = getCurrentPages();
            feedbackData.current.path = pages[pages.length - 1].$taroPath || pages[pages.length - 1].route;
            try {
                const { appId, version } = Taro.getAccountInfoSync().miniProgram;
                feedbackData.current.appId = appId;
                feedbackData.current.version = version;
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'getAccountInfoSync',
                    data: {
                        error
                    }
                });
            }
            Taro.getSystemInfo({
                success(res: any) {
                    console.log('getSystemInfo', res);
                    feedbackData.current.brand = res.brand;
                    feedbackData.current.model = res.model;
                    feedbackData.current.platform = res.platform;
                    feedbackData.current.system = res.system;
                    feedbackData.current.wxVersion = res.version;
                    feedbackData.current.SDKVersion = res.SDKVersion;
                    feedbackData.current.environment = res.environment;
                    feedbackData.current.microphoneAuthorized = res.microphoneAuthorized;
                    feedbackData.current.name = userInfo.name;
                    feedbackData.current.company = userInfo.companyName;
                    feedbackData.current.phone = userInfo.phoneNumber;
                }
            });
        } catch (error) {
            addLog({
                level: 'error',
                message: 'getAccountInfoSync',
                data: {
                    error
                }
            });
        }
        window_height = sysinfo.windowHeight;
        setTimeout(() => {
            const query1 = Taro.createSelectorQuery();
            query1
                .select('#bottomContent')
                .boundingClientRect()
                .exec((rect) => {
                    bottomContent_height = rect[0].height;
                });
            const query2 = Taro.createSelectorQuery();
            query2
                .select('#topBox')
                .boundingClientRect()
                .exec((rect) => {
                    topBox_height = rect[0].height;
                });
        }, 1000);
        const isHarmony = await checkHarmony();
        console.log('isHarmony', isHarmony);
        isHarmonyRef.current = isHarmony;
        if (!data) return;
        time.current = data.script.timeLimit;
        const voice_interaction_type = Storage.get(StorageEnvKey.SKILL_INTERACTION_TYPE) || VoiceInteraction.Manual;
        voiceInteractionRef.current = voice_interaction_type;
        try {
            getAllChatHistory(chatId).then(({ data: historyRes }) => {
                if (historyRes.code === 200) {
                    setHistory(historyRes.data);
                    historyRef.current = historyRes.data;
                }
            });

            setCustomTitle({ title: data.eman.name });
            chatData.current = data;
            setAiHelpEnable(data.script.aiHelpFlag);
            const { data: serverTimeData } = await getServerTime();
            console.log('servertime', serverTimeData.data);
            serverTime.current = serverTimeData.data;
            createTime.current = data.createTime;
            const leftTime =
                dayjs(data.createTime).add(time.current, 'm').valueOf() - dayjs(serverTime.current).valueOf();
            setLeftTime(leftTime);
            console.log('getChat-leftTime', leftTime);

            // 对话时间结束，关闭对话
            if (data.done) {
                addLog({
                    level: 'trace',
                    message: '对话已完成'
                });
                onLoaded();
                isTimeout.current = true;
                if (showVoicePageRef.current) {
                    // 语音的情况
                    voicePageRef.current?.finish();
                }
                setChatActionSheetType(ChatActionSheetType.OFF);
                setShowActionSheet(true);
                return;
            } else if (leftTime <= 0) {
                addLog({
                    level: 'trace',
                    message: '倒计时结束了'
                });
                isTimeout.current = true;
                onLoaded();
                const res = await doneChat(chatId, false);
                if (res.data.code === 200) {
                    if (showVoicePageRef.current) {
                        // 语音的情况
                        voicePageRef.current?.finish();
                    }
                    chatData.current.done = true;
                    setChatActionSheetType(ChatActionSheetType.OFF);
                    setShowActionSheet(true);
                }
            } else {
                // 当前时间比对话生成时间 大于一分钟。说明有中断的情况
                if (dayjs(serverTime.current).valueOf() - dayjs(data.createTime).valueOf() > 60000) {
                    addLog({
                        level: 'trace',
                        message: '还有剩余时间  > 60000'
                    });

                    if (from === 'shuziren') {
                        onLoaded();
                    } else {
                        // if (interrupt === '1') {
                        // 打断后，语音重新开始
                        addLog({
                            level: 'trace',
                            message: 'interrupt 1'
                        });
                        handleVoicePage();
                        startListen();
                        // } else {
                        //     onLoaded();
                        //     setChatActionSheetType(ChatActionSheetType.INTERRUPT);
                        //     setShowActionSheet(true);
                        // }
                    }

                    // return; // 继续倒计时
                } else {
                    addLog({
                        level: 'trace',
                        message: '还有剩余时间'
                    });

                    if (from === 'history' || from === 'shuziren') {
                        onLoaded();
                    } else {
                        if (isHarmony) {
                            onLoaded();
                        } else {
                            handleVoicePage();
                        }

                        addLog({
                            level: 'debug',
                            message: '初始化开场白',
                            data: {
                                introductionType,
                                introductionDelay,
                                introduction
                            }
                        });
                        if (introduction) {
                            // 历史记录是空的
                            if (introductionType === 0) {
                                // 没有开场白
                                /* addLog({
                                    level: 'debug',
                                    message: 'initIntro 没有开场白',
                                    data: {
                                        introductionType,
                                        introductionDelay,
                                        introduction
                                    }
                                }); */
                                startListen();
                            } else if (introductionType === 1) {
                                // 立即开场白，保存开场白，并语音播放
                                /* addLog({
                                    level: 'debug',
                                    message: 'initIntro 立即开场白',
                                    data: {
                                        introductionType,
                                        introductionDelay,
                                        introduction
                                    }
                                }); */
                                saveIntro(data.id);
                            } else if (introductionType === 2) {
                                // 延迟开场白，增加倒计时，倒计时结束，保存开场白，语音播放；
                                // 手动模式语音，点击取消倒计时；
                                // 自动模式语音，识别到话，取消倒计时，没识别到话，停止聆听，保存开场白，语音播放；
                                /* addLog({
                                    level: 'debug',
                                    message: 'initIntro 延迟开场白',
                                    data: {
                                        introductionType,
                                        introductionDelay,
                                        introduction
                                    }
                                }); */
                                startListen();
                                helloTimer.current = setTimeout(async () => {
                                    addLog({
                                        level: 'trace',
                                        message: '延迟开场白结束'
                                    });
                                    if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                                        voicePageRef.current.cancelListen();
                                    }
                                    saveIntro(data.id);
                                    // saveIntroTest();
                                }, introductionDelay * 1000);
                            }
                        } else {
                            /* addLog({
                                level: 'trace',
                                message: '没开场白'
                            }); */
                            startListen();
                        }
                    }
                }
            }
        } catch (error) {
            console.log('getAllChatHistory error', error);
        }
        runPollingServerTime();
    });

    useUnmount(() => {
        // Taro.setClipboardData({
        //     data: JSON.stringify(logs.current)
        // });
    });

    useEffect(() => {
        Taro.onKeyboardHeightChange((res) => {
            setKeyHeight(res.height); //  - 2 安卓机型出现了蓝线
            if (res.height) {
                const query = Taro.createSelectorQuery();
                query
                    .select('#chat')
                    .boundingClientRect()
                    .exec((rect) => {
                        const chat_height = rect[0].height;
                        const t1 = window_height - topBox_height - res.height - bottomContent_height;
                        const t2 = chat_height - 100;
                        console.log('window', window_height);
                        console.log('topBox', topBox_height);
                        console.log('keyboard', res.height);
                        console.log('bottomContent', bottomContent_height);
                        console.log('chat_height', chat_height);
                        console.log('t1', t1);
                        console.log('t2', t2);
                        // 总高度 - 头部 - 键盘 - 发送框 = 内容区域
                        if (t1 < t2) {
                            // 计算出应该上移多少距离
                            // 最多res.height 最少0 中间变量值为t2 - t1
                            setTranslateHeight(t2 - t1 < res.height ? t2 - t1 : res.height);
                        } else {
                            setTranslateHeight(0);
                        }
                    });
            }
        });
    }, []);

    const { run: onAiHelp } = useThrottleFn(
        useCallback(() => {
            if (isTimeout.current || chatData.current.done) {
                return;
            }
            if (chatStreamLoading || !!assistantMessage) {
                Toast_.show({
                    message: 'AI说话中无法进行求助'
                });
            } else {
                console.log('sjnsnjn');
                setAiHelpShow(true);
            }
        }, [chatStreamLoading, assistantMessage]),
        {
            wait: 100,
            leading: true,
            trailing: false
        }
    );

    const handleAiHelpClose = () => {
        setAiHelpShow(false);
    };

    return (
        <Block>
            {/* 聊天对话框———————————————————————— */}
            <View id='topBox' className={styles.topBox}>
                {/* style='padding-top: 88px;' */}
                <NavBar
                    title={customTitle.title}
                    safeAreaInsetTop
                    zIndex={0}
                    renderLeft={
                        <View className={styles.countDownBox}>
                            <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                            <View className={styles.countDown} onClick={goBack}>
                                {minutes >= 10 ? minutes : `0${minutes}`}:{seconds >= 10 ? seconds : `0${seconds}`}
                            </View>
                        </View>
                    }
                />
                {/* fixed */}
                <View className={styles.noticeBar}>对话为AI情景模拟，非真实场景</View>
                {/* style={`top:${pxTransform(navBarHeight)}`} */}
            </View>
            <View id='chat' style={{ transform: `translateY(-${keyHeight ? translateHeight : keyHeight}px)` }}>
                <ChatHistory
                    scene={data?.scene.scene as string}
                    scriptTime={data?.createTime as string}
                    history={history}
                    assistantMessage={assistantMessage}
                    eman={{
                        name: data?.eman.name as string,
                        avatar: data?.eman.avatar as string
                    }}
                />
            </View>
            <View
                id='footer'
                className={styles.footer}
                style={{ paddingBottom: history.length > 0 ? pxTransform(96 + 156) : 0, marginBottom: keyHeight }}
            />
            {/* paddingBottom: history.length > 0 ? pxTransform(156) : 0, */}
            <View
                className={styles.bottomContentAihelp}
                style={{
                    background: aiHelpShow ? 'rgba(0, 0, 0, 0.3)' : '',
                    zIndex: aiHelpShow ? 2 : -1
                }}
            >
                <Aihelp
                    show={aiHelpShow}
                    keyHeight={keyHeight}
                    chatId={data?.id}
                    styleType='text'
                    onClose={handleAiHelpClose}
                    canGenerate={canGenerate}
                />
            </View>

            <View id='bottomContent' className={styles.bottomContent} style={{ bottom: keyHeight }}>
                <View className={styles.Ai_help}>
                    {showScriptBtn && !aiHelpShow && (
                        <View className={styles.showScripts} onClick={onShowScript}>
                            <Image width={pxTransform(36)} height={pxTransform(36)} src={scriptImg} />
                            <Text>培训脚本</Text>
                        </View>
                    )}
                    {aiHelpEnable && !aiHelpShow && (
                        <View
                            className={classnames(
                                styles.showScripts,
                                chatStreamLoading || !!assistantMessage ? styles.showScripts_Ai : ''
                            )}
                            onClick={onAiHelp}
                        >
                            <Image width={pxTransform(36)} height={pxTransform(36)} src={IconHelpBlue} />
                            <Text>AI求助</Text>
                        </View>
                    )}
                </View>

                <View className={styles.sendMessage}>
                    <View className={styles.sendMessage_tel_view} onClick={onVoiceClick}>
                        <Image className={styles.sendMessage_tel_image} src={TelImg} />
                    </View>
                    <Form form={formIt} className={classnames(styles.sendMessage_box, 'send_message_box')}>
                        <FormItem name='message' label=''>
                            <Textarea
                                autoHeight
                                className={styles.sendMessage_input}
                                placeholder={
                                    data?.script.disableTextInputFlag ? '本脚本已禁用文字输入对话' : '请输入消息'
                                }
                                placeholderStyle={`font-size: ${pxTransform(28)};line-height: 1}`}
                                maxlength={1000}
                                // value={message}
                                disabled={
                                    data?.script.disableTextInputFlag ||
                                    chatStreamLoading ||
                                    !!assistantMessage ||
                                    showVoicePage
                                }
                                confirmType='send'
                                adjustPosition={false}
                                controlled
                                onConfirm={(e: any) => {
                                    console.log('confirm', e);
                                    // setMessage(e.detail.value);
                                    onSend();
                                }}
                                onInput={(e: any) => {
                                    formIt.setFieldsValue('message', e.detail.value);
                                    setCanSend(!(e.detail.value.trim() === ''));
                                }}
                            />
                        </FormItem>
                        <Button
                            onClick={onSend}
                            className={styles.sendMessage_send}
                            style={{
                                backgroundImage: `url(${canSend ? send_1 : send_0})`
                            }}
                        />
                    </Form>
                </View>
            </View>
            <ActionSheet
                show={showActionSheet}
                zIndex={110}
                onClose={() => setShowActionSheet(false)}
                title={actionSheet?.title}
                className={classnames(styles.actionSheet, {
                    [styles.actionSheet_hide_close]: actionSheet?.closeAction
                })}
                closeOnClickOverlay={false}
                // @ts-ignore
                style={{ '--action-sheet-header-height': pxTransform(124) }}
            >
                <Button.Group className={styles.actionSheet_group} direction='vertical'>
                    {(actionSheet?.actions ?? [])
                        .filter((action) => {
                            if (action.noChatHistoryHide) {
                                return history.length;
                            } else {
                                return true;
                            }
                        })
                        .map((action) => {
                            return (
                                // eslint-disable-next-line taro/no-spread-in-props
                                <Button block round {...action.buttonProps} key={action.text}>
                                    {action.text}
                                </Button>
                            );
                        })}
                </Button.Group>
            </ActionSheet>
            {/* 更多———————————————————————— */}
            <ActionMore
                show={showMoreActionSheet}
                onClose={() => setShowMoreActionSheet(false)}
                onScript={() => {
                    setShowMoreActionSheet(false);
                    onShowScript();
                }}
                onSetting={showActionSheetVoiceSetting}
                onFeedback={handleFeedback}
            />
            <ActionVoice
                show={showVoiceActionSheet}
                type='SKILL_INTERACTION_TYPE'
                onClose={() => setShowVoiceActionSheet(false)}
                onConfirm={voicePopupConfirm}
            />
            <Dialog_ />
            {/* e人语音页面———————————————————————— */}
            <VoiceDialogueSkill
                aiHelpShow={aiHelpShow}
                canGenerate={canGenerate}
                onCloseAi={handleAiHelpClose}
                onAiHelp={onAiHelp}
                aiHelpEnable={aiHelpEnable}
                handleAiHelpClose={handleAiHelpClose}
                keyHeight={keyHeight}
                chatId={data?.id}
                styleType={showVoicePage ? 'voice' : 'text'}
                show={showVoicePage}
                ref={voicePageRef}
                chat={data}
                minutes={minutes}
                seconds={seconds}
                onClose={() => {
                    setShowVoicePage(false);
                    showVoicePageRef.current = false;
                    onScrollView();
                }}
                onSentence={handleVoiceSentence}
                doneActionSheet={doneActionSheet}
                showScript={onShowScript}
                onFinish={goBack}
                showMore={showMore}
                onLoaded={onLoaded}
                cancelHello={cancelHello}
                addLog={addLog}
                getTimeout={getTimeout}
                saveLogs={saveLogs}
            />
            <FeedbackDialog
                show={feedbackShow}
                keyHeight={keyHeight}
                onClose={() => setFeedbackShow(false)}
                onConfirm={submitFeedback}
            />
            <FeedbackSucess show={feedbackSuccessShow} onClose={() => setFeedbackSuccessShow(false)} />
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='A' />
        </Block>
    );
};

export default Index;
