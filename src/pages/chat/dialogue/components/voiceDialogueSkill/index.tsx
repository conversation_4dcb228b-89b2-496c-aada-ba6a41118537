import IconHelpOff from '@/assets/icon_help_off.svg';
import IconVideo from '@/assets/icon_video.svg';
import IconVideoClose from '@/assets/icon_video_close.svg';
import { Storage, useEvent, useStorage } from '@/common';
import Aihelp from '@/components/aihelp';
import VoiceLoading from '@/components/voiceLoading';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import { StorageEnvKey } from '@/constants/storage';
import {
    ChatMode,
    getVoiceStatusKey,
    RecognizeStatus,
    SubtitleVisible,
    VoiceInteraction,
    VoiceStatus
} from '@/constants/voicetype';
import { ttsErrorHandler } from '@/pages/chat/components/utils';
import VoiceWave from '@/pages/practicePPT/practice/components/practicePPTDialog/generateBars';
import { ttsVoice } from '@/services/common';
import type { ChatVO } from '@/types/chat';
import type { LogInfo } from '@/types/common';
import { blurImage } from '@/utils/imageUtils';
import { removeDuplicateText, removeMarkdown } from '@/utils/sentenseUtils';
import AudioSpeechRecognizer from '@/utils/speechRecognizer/AudioSpeechRecognizer';
import { Dialog, Icon, NavBar, Toast } from '@antmjs/vantui';
import { Block, Canvas, Image, View } from '@tarojs/components';
import type { NodesRef } from '@tarojs/taro';
import Taro, {
    createInnerAudioContext,
    createSelectorQuery,
    getFileSystemManager,
    offNetworkWeakChange,
    onNetworkWeakChange,
    pxTransform,
    setInnerAudioOption,
    setKeepScreenOn
} from '@tarojs/taro';
import { useMount, useThrottleFn, useUnmount } from 'ahooks';
import classNames from 'classnames';
import lottie from 'lottie-miniprogram';
import type { ReactNode } from 'react';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useRef, useState } from 'react';
import styles from './index.less';

interface Props {
    show: boolean;
    chat: ChatVO | undefined;
    minutes: number;
    seconds: number;
    onClose: () => void;
    onSentence: (sentence: { text: string; filePath?: string; fileSize?: number }) => void;
    showScript: () => void;
    onFinish: () => void;
    showMore: () => void;
    onLoaded: () => void;
    cancelHello: () => void;
    addLog: (log: LogInfo) => void;
    doneActionSheet: () => void;
    getTimeout: () => boolean;
    aiHelpEnable: boolean;
    onAiHelp: () => void;
    handleAiHelpClose: () => void;
    canGenerate: boolean;
    saveLogs: (log: { action: string; content: string }) => void;
}
const IconChat = `${config.cdnPrefix}chat/icon_chat.svg`;
const IconChatDisabled = `${config.cdnPrefix}chat/icon_chat_disabled.svg`;
const errorImg = `${config.cdnPrefix}svg/error.svg`;
const IconCancel = `${config.cdnPrefix}svg/icon_cancel.svg`;

const IconHangup = `${config.cdnPrefix}svg/icon_hangup.svg`;
const IconHangupMini = `${config.cdnPrefix}svg/icon_hangup_mini.svg`;
const IconMore = `${config.cdnPrefix}svg/icon_more.svg`;
const IconSpeak = `${config.cdnPrefix}svg/icon_speak.svg`;
const IconSpeakDisabled = `${config.cdnPrefix}svg/icon_speak_disabled.svg`;
const IconSubtitleOff = `${config.cdnPrefix}svg/icon_subtitle_off.svg`;
const IconSubtitleOn = `${config.cdnPrefix}svg/icon_subtitle_on.svg`;
const IconHelp = `${config.cdnPrefix}aihelp/icon_help.svg`;

const enum SpeechStatus {
    PlayStart,
    Playing,
    PlayEnd
}
const defaultTone = {
    name: '',
    speed: 0.4, // [-2，2]，分别对应不同语速：-2代表0.6倍，-1代表0.8倍，0代表1.0倍（默认），1代表1.2倍，2代表1.5倍。如果需要更细化的语速，可以保留小数点后一位，例如0.5 1.1 1.8等
    volume: 0, // [0，10]
    voiceType: 301035 // 音色,爱小梅-多情感女声
};
const overtime = 5000; // 不说话超时时间
const standbyTime = 5000; // 超时后待机时间
const speechLength = 100; // 文本转换长度
const Dialog_ = Dialog.createOnlyDialog();
const ToastVoiceDialog_ = Toast.createOnlyToast();
const audio = createInnerAudioContext();
const Index: React.FC<Props> = (props: any, ref) => {
    const {
        show,
        onClose,
        chat,
        onSentence,
        minutes,
        seconds,
        onFinish,
        showMore,
        onLoaded,
        cancelHello,
        addLog,
        doneActionSheet,
        getTimeout,
        aiHelpEnable,
        onAiHelp,
        handleAiHelpClose,
        aiHelpShow,
        keyHeight,
        chatId,
        styleType,
        onCloseAi,
        canGenerate,
        saveLogs
    } = props;
    const speechRecognizerManager = useRef<AudioSpeechRecognizer>(); // 获取实时录音识别管理器
    const AvatarDefault = `${config.cdnPrefix}avatar_default.png`;
    const statusRef = useRef<VoiceStatus>();
    const [status, setStatus] = useState<VoiceStatus>();
    const [statusText, setStatusText] = useState<string[]>();
    const [statusLoading, setStatusLoading] = useState<ReactNode>();
    const [decibelPlay, setdecibelPlay] = useState<any>('');
    const [isZore, setisZore] = useState<any>('');
    let isadd = 1;
    const aiSentenceStatus = useRef<SpeechStatus>(SpeechStatus.PlayEnd);
    const aiSentence = useRef<string[]>([]); // AI语音回复的文字
    const [voiceInteraction, setVoiceInteraction] = useState(VoiceInteraction.Manual);
    const voiceInteractionRef = useRef<VoiceInteraction>(VoiceInteraction.Manual);
    const [showVideoClose, setShowVideoClose] = useState(ChatMode.SHUZIREN);

    const userSentence = useRef<{
        text: string;
        filePath?: string;
        fileSize?: number;
    }>(); // 用户语音识别出来的文字
    const tempAiSentence = useRef<string>('');
    const aiVoiceList = useRef<{ id: number; text: string; url: string }[]>([]);
    const aiVoiceNow = useRef<{
        id: number;
        text: string;
        url: string;
        status: SpeechStatus;
    } | null>();
    const timerOvertime = useRef<any>(); // 5s不说话倒计时
    const timerStandby = useRef<any>(); // 待机倒计时
    const timerNoSentence = useRef<any>();
    const intervalSpeech = useRef<any>(); // 定时检查语音播放
    const intervalText = useRef<any>(); // 定时检查ai文本
    const isFinish = useRef<boolean>(false);
    const isFirst = useRef<boolean>(false);
    const tone = useRef({ ...defaultTone });
    const bubbleAnimate = useRef<any>();
    const waveAnimate = useRef<any>();
    const [sayPressed, setSayPressed] = useState<boolean>(false);
    // const [duration, setDuration] = useState(0);
    const touchTop = useRef<number>(660);

    const [recognizerTime, setRecognizerTime] = useState<number>(60);
    const recognizeTimeRef = useRef<any>();

    const [subtitleShow, setSubtitleShow] = useState<SubtitleVisible>(SubtitleVisible.Show);
    const [subtitleText, setSubtitleText] = useState<string>();

    const recognizeStatusRef = useRef<RecognizeStatus>();
    const [isQnq, setIsQnq] = useState<boolean>(false);
    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);
    const audioDurationTimer = useRef<any>();

    function clearAudioDurationTimer() {
        if (audioDurationTimer.current) {
            clearTimeout(audioDurationTimer.current);
            audioDurationTimer.current = null;
        }
    }
    const backgroundBlur = useMemo(() => {
        const bg = chat?.eman.background || '';
        if (bg && isWework === '0') {
            const url = blurImage(bg, isQnq ? 'huawei' : 'tencent');
            return url;
        } else {
            return bg;
        }
    }, [chat, isWework]);
    const changeStatus = (status: VoiceStatus) => {
        addLog({
            level: 'debug',
            message: 'changeStatus',
            data: {
                status
            }
        });
        statusRef.current = status;
        setStatus(status);
        switch (status) {
            case VoiceStatus.Listening:
                setStatusText(['我在听，你说']);
                setStatusLoading(<VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />);
                break;
            case VoiceStatus.WaitListen:
                setStatusText(['']);
                setStatusLoading(<VoiceLoading type='barStatic' />);
                break;
            case VoiceStatus.Waiting:
                setStatusText(['稍等，让我想想']);
                setStatusLoading(<VoiceLoading type='dot' />);
                break;
            case VoiceStatus.Speaking:
                setStatusText(['点击可打断']);
                setStatusLoading(<VoiceLoading type='shortBar' />);
                break;
            case VoiceStatus.Overtime:
                setStatusText(['我好像没听清', '请用普通话大声一点']);
                setStatusLoading(null);
                break;
            case VoiceStatus.Standby:
                setStatusText(['点击屏幕重试']);
                setStatusLoading(<VoiceLoading type='repeat' />);
                break;
            case VoiceStatus.Cancel:
                setStatusText(['松手取消发送']);
                setStatusLoading(<Image src={IconCancel} className={styles.cancel_icon} />);
                break;
            default:
                setStatusText(['']);
                setStatusLoading(null);
                break;
        }
    };

    const clearRecognizerTimer = () => {
        addLog({
            level: 'trace',
            message: 'clearRecognizerTimer'
        });
        if (recognizeTimeRef.current) {
            clearInterval(recognizeTimeRef.current);
            recognizeTimeRef.current = undefined;
        }
    };

    /* const handleErrorDialog = (msg: string) => {
        addLog({
            level: 'error',
            message: 'handleErrorDialog'
        });
        Dialog_.confirm({
            message: (
                <Block>
                    <Image style={{ width: pxTransform(240), height: pxTransform(240) }} src={errorImg} />
                    <View
                        style={{
                            fontSize: pxTransform(32),
                            textAlign: 'center',
                            marginTop: pxTransform(56),
                            marginBottom: pxTransform(56)
                        }}
                    >
                        {msg}
                    </View>
                </Block>
            ),
            confirmButtonText: '刷新',
            confirmButtonColor: '#4F66FF',
            onConfirm() {
                Taro.reLaunch({
                    url: '/pages/home/<USER>'
                });
            }
        });
    };*/

    // 文本转语音;
    function textToSpeech(t: string) {
        addLog({
            level: 'debug',
            message: 'textToSpeech',
            data: {
                t
            }
        });
        let text = t.replace(/[\(（][^）]*[\)）]/g, '');
        text = removeMarkdown(text);
        const id = new Date().getTime();
        aiVoiceList.current.push({
            id,
            text,
            url: ''
        });

        // saveLogs({
        //     action: 'tts',
        //     content: text
        // })
        ttsVoice({
            name: tone.current.name,
            type: chat?.eman.gender,
            text // 去除括号内容
        })
            .then((res) => {
                const audioPath = `${Taro.env.USER_DATA_PATH}/chat/voice_${id}.mp3`;

                // console.log('生成语音', res, new Date().toLocaleString());
                // saveLogs({
                //     action: 'tts返回',
                //     content: text
                // })
                if (res.data.data) {
                    const fs = Taro.getFileSystemManager();
                    const dir = `${Taro.env.USER_DATA_PATH}/chat`;
                    try {
                        fs.accessSync(dir);
                    } catch (e) {
                        addLog({
                            level: 'error',
                            message: `no ${dir}`,
                            data: {
                                id,
                                text
                            }
                        });
                        try {
                            fs.mkdirSync(dir, false);
                        } catch (error) {
                            addLog({
                                level: 'error',
                                message: `mkdir error ${dir}`,
                                data: {
                                    id,
                                    text
                                }
                            });
                        }
                    }
                    fs.writeFile({
                        filePath: audioPath,
                        data: res.data.data,
                        encoding: 'base64',
                        success: (e: any) => {
                            // console.log('文件保存成功', e, new Date().toLocaleString());
                            // saveLogs({
                            //     action: 'tts音频保存成功',
                            //     content: text
                            // })
                            aiVoiceList.current.forEach((item) => {
                                if (item.id === id) {
                                    item.url = audioPath;
                                }
                            });
                        },
                        fail(error) {
                            addLog({
                                level: 'error',
                                message: 'tts ai语音文件保存失败',
                                data: {
                                    id,
                                    text,
                                    error
                                }
                            });
                            const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                            if (fi !== -1) {
                                if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                                    aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                                }

                                aiVoiceList.current.splice(fi, 1);
                            }
                        }
                    });
                } else {
                    addLog({
                        level: 'warn',
                        message: 'tts no data',
                        data: {
                            id,
                            text
                        }
                    });
                    Taro.showToast({
                        title: '语音生成错误',
                        icon: 'none'
                    });
                    const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                    if (fi !== -1) {
                        if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                            aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                        }

                        aiVoiceList.current.splice(fi, 1);
                    }
                }
            })
            .catch((error) => {
                const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                if (fi !== -1) {
                    if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                        aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                    }

                    aiVoiceList.current.splice(fi, 1);
                }
                ttsErrorHandler(error);
                addLog({
                    level: 'error',
                    message: 'tts error',
                    data: {
                        id,
                        text,
                        error
                    }
                });
            });
        /*   plugin.textToSpeech({
            content: text, // 中文最大支持150个汉字（全角标点符号算一个汉字）；英文最大支持500个字母（半角标点符号算一个字母）
            ...tone.current,
            emotionCategory: 'neutral',
            projectId: 0,
            sampleRate: 16000, // 音频采样率：16000：16k（默认），8000：8k
            success(data: any) {
                const url = data.result.filePath;
                if (url && url.length > 0) {
                    // console.log('语音', text, url, new Date().getTime());
                    aiVoiceList.current.forEach((item) => {
                        if (item.id === id) {
                            item.url = url;
                        }
                    });
                } else {
                    const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                    if (fi !== -1) {
                        if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                            aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                        }
                        aiVoiceList.current.splice(fi, 1);
                    }

                    console.log('没有语音', fi);
                }
            },
            fail(error: any) {
                const fi = aiVoiceList.current.findIndex((item) => item.id === id);
                if (fi !== -1) {
                    if (aiVoiceNow.current && aiVoiceNow.current.text === aiVoiceList.current[fi].text) {
                        aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                    }

                    aiVoiceList.current.splice(fi, 1);
                }
                console.log('textToSpeech', error);
            }
        }); */
    }
    const finish = () => {
        addLog({
            level: 'trace',
            message: 'finish'
        });
        stop();
        stopAISpeaking();
        changeStatus(VoiceStatus.Stop);
    };
    const finishManual = () => {
        addLog({
            level: 'trace',
            message: 'finishManual'
        });
        stop();
        stopAISpeaking();
        changeStatus(VoiceStatus.WaitListen);
    };

    const handleClose = () => {
        // finish();
        addLog({
            level: 'trace',
            message: 'handleClose',
            data: {
                status: statusRef.current
            }
        });
        if (
            statusRef.current === VoiceStatus.BeforeListen ||
            statusRef.current === VoiceStatus.Listening ||
            statusRef.current === VoiceStatus.Overtime ||
            statusRef.current === VoiceStatus.Waiting
        ) {
            Taro.showToast({
                title: '不支持录音时切换',
                icon: 'none'
            });
        } else {
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                finish();
            } else {
                finishManual();
            }
            onClose();
        }
    };

    // 打断ai语音播放
    function stopAISpeaking() {
        addLog({
            level: 'trace',
            message: 'stopAISpeaking'
        });

        if (timerOvertime.current) {
            clearTimeout(timerOvertime.current);
            timerOvertime.current = null;
        }
        if (timerStandby.current) {
            clearTimeout(timerStandby.current);
            timerStandby.current = null;
        }
        if (intervalSpeech.current) {
            clearInterval(intervalSpeech.current);
            intervalSpeech.current = null;
        }
        if (intervalText.current) {
            clearInterval(intervalText.current);
            intervalText.current = null;
        }
        clearAudioDurationTimer();
        setSubtitleText('');
        tempAiSentence.current = '';
        aiSentence.current = [];
        aiVoiceList.current = [];
        aiSentenceStatus.current = SpeechStatus.PlayEnd;
        audio.stop();
        audio.offPlay();
        audio.offTimeUpdate();
        audio.offEnded();
        audio.offError();
        aiVoiceNow.current = null;
    }

    const statusTextClick = () => {
        addLog({
            level: 'trace',
            message: 'statusTextClick',
            data: {
                voiceInteraction: voiceInteractionRef.current,
                status: statusRef.current
            }
        });
        if (voiceInteractionRef.current === VoiceInteraction.Auto) {
            // 打断
            if (statusRef.current === VoiceStatus.Speaking) {
                stopAISpeaking();
                start();
            }
            // 待机重试
            if (statusRef.current === VoiceStatus.Standby) {
                start();
            }
        } else {
            if (statusRef.current === VoiceStatus.Speaking) {
                stopAISpeaking();
                changeStatus(VoiceStatus.WaitListen);
            }
            if (statusRef.current === VoiceStatus.Standby) {
                start();
            }
        }
    };

    const toggleSubtitle = () => {
        setSubtitleShow((prev) => {
            const s = prev === SubtitleVisible.Hide ? SubtitleVisible.Show : SubtitleVisible.Hide;
            Storage.set(StorageEnvKey.SUBTITLE_VISIBLE, s);
            return s;
        });
    };

    const handleChatMode = useEvent(() => {
        const mode = showVideoClose === ChatMode.AUDIO ? ChatMode.SHUZIREN : ChatMode.AUDIO;
        setShowVideoClose(mode);
        Storage.set(StorageEnvKey.CHAT_MODE, mode);
        if (mode === ChatMode.SHUZIREN) {
            Dialog_.alert({
                title: '3D动态效果已开启',
                message: '下次练习时生效，为您带来更沉浸的互动体验',
                confirmButtonText: '我知道了',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    });

    const { run: handleAIHelp } = useThrottleFn(
        () => {
            if (statusRef.current === VoiceStatus.Speaking || statusRef.current === VoiceStatus.Waiting) {
                ToastVoiceDialog_.show({
                    message: 'AI说话中无法进行求助'
                });
            } else {
                onAiHelp();
            }
        },
        {
            wait: 250,
            leading: true,
            trailing: false
        }
    );

    function stop() {
        addLog({
            level: 'trace',
            message: 'stop 停止识别'
        });
        clearRecognizerTimer();
        if (timerOvertime.current) {
            clearTimeout(timerOvertime.current);
            timerOvertime.current = null;
        }
        if (timerStandby.current) {
            clearTimeout(timerStandby.current);
            timerStandby.current = null;
        }
        speechRecognizerManager.current?.stop();
    }

    function start() {
        addLog({
            level: 'trace',
            message: 'start 启动识别'
        });
        if (timerNoSentence.current) {
            clearTimeout(timerNoSentence.current);
        }
        clearRecognizerTimer();
        setRecognizerTime(60);
        // saveLogs({
        //     action: '开始识别',
        //     content: ''
        // })
        speechRecognizerManager.current?.start();
    }

    const handleFinish = () => {
        addLog({
            level: 'trace',
            message: 'handleFinish'
        });
        // stop();
        onFinish();
    };

    const splitSentence = (text: string) => {
        if (text.length <= speechLength) {
            aiSentence.current.push(text);
        } else {
            // 每speechLength长度的文本push到aiSentence.current
            const sentenceSplit = text.split(/[?!;。？！；]/g);
            for (let i = 0; i < sentenceSplit.length; i++) {
                const sp = sentenceSplit[i];
                if (sp.length <= speechLength) {
                    if (sp !== '') {
                        aiSentence.current.push(sp);
                    }
                } else {
                    for (let j = 0; j < sp.length; j += speechLength) {
                        aiSentence.current.push(sp.substring(j, j + speechLength));
                    }
                }
            }
        }
    };

    const handleSentence = (text: string) => {
        if (text !== undefined && text !== '') {
            if (isFirst.current && text.length >= 30) {
                // 如果是第一句，就简短，提升回复速度
                isFirst.current = false;
                const firstPunctuationIndex = text.search(/[,?!，。？！]/);
                if (firstPunctuationIndex !== -1 && firstPunctuationIndex < text.length - 1) {
                    const firstPart = text.slice(0, firstPunctuationIndex);
                    const secondPart = text.slice(firstPunctuationIndex + 1);

                    console.log('分割后的两个数组:', firstPart, secondPart, new Date().toLocaleString());
                    if (firstPart !== '') {
                        if (firstPart.length <= 10) {
                            aiSentence.current.push(firstPart);
                        } else {
                            splitSentence(firstPart);
                        }
                    }
                    if (secondPart !== '') {
                        splitSentence(secondPart);
                    }
                } else {
                    console.log('未找到指定的标点符号');
                    splitSentence(text);
                }
            } else {
                splitSentence(text);
            }
            // splitSentence(text);
        }
    };

    const handleVoicePlay = () => {
        // console.log('aiVoiceList', aiVoiceList.current);
        // console.log('aiVoiceNow', aiVoiceNow.current);
        if (aiVoiceList.current && aiVoiceList.current.length > 0) {
            // 还有未播放的语音，接下去播放第一条
            const [currentSpeech] = aiVoiceList.current;
            if (!aiVoiceNow.current || aiVoiceNow.current.status === SpeechStatus.PlayEnd) {
                aiVoiceNow.current = { ...currentSpeech, status: SpeechStatus.PlayStart };
            }

            if (aiVoiceNow.current.status === SpeechStatus.PlayStart && currentSpeech.url) {
                aiVoiceNow.current.status = SpeechStatus.Playing;
                aiVoiceNow.current.url = currentSpeech.url;
                setSubtitleText(aiVoiceNow.current?.text);
                audio.src = aiVoiceNow.current.url;
                // audio.seek(0.1);
                const fs = getFileSystemManager();
                try {
                    fs.accessSync(aiVoiceNow.current.url);
                    audio.onPlay(() => {
                        addLog({
                            level: 'debug',
                            message: '语音onPlay',
                            data: {
                                ...aiVoiceNow.current
                            }
                        });
                        // saveLogs({
                        //     action: '音频onPlay',
                        //     content: aiVoiceNow.current?.text
                        // })
                    });
                    audio.onTimeUpdate(() => {
                        console.log('duration', audio.duration);
                        if (
                            (audio.duration > 0 && audioDurationTimer.current === undefined) ||
                            audioDurationTimer.current === null
                        ) {
                            audioDurationTimer.current = setTimeout(() => {
                                clearAudioDurationTimer();
                                addLog({
                                    level: 'debug',
                                    message: '主动播放onEnded',
                                    data: {
                                        ...aiVoiceNow.current,
                                        aiVoiceLength: aiVoiceList.current.length
                                    }
                                });
                                // setSubtitleText('');
                                audio.stop();
                                audio.offPlay();
                                audio.offEnded();
                                audio.offError();
                                if (aiVoiceNow.current) {
                                    if (currentSpeech.id === aiVoiceNow.current.id) {
                                        aiVoiceList.current.shift();
                                        aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                                        console.log('剩余语音列表', aiVoiceList.current);
                                    }
                                }
                            }, audio.duration * 1000 + 500);
                        }
                    });

                    audio.onEnded(() => {
                        clearAudioDurationTimer();
                        // 播放完，删除第一条
                        addLog({
                            level: 'debug',
                            message: '播放onEnded',
                            data: {
                                ...aiVoiceNow.current,
                                aiVoiceLength: aiVoiceList.current.length
                            }
                        });
                        // saveLogs({
                        //     action: '音频onEnded',
                        //     content: aiVoiceNow.current?.text
                        // })
                        // setSubtitleText('');
                        audio.offPlay();
                        audio.offEnded();
                        audio.offError();
                        if (aiVoiceNow.current) {
                            if (currentSpeech.id === aiVoiceNow.current.id) {
                                try {
                                    const res = getFileSystemManager().unlinkSync(aiVoiceNow.current.url);
                                    console.log('删除音频文件成功', res);
                                } catch (e) {
                                    addLog({
                                        level: 'error',
                                        message: '删除音频文件失败',
                                        data: {
                                            error: e,
                                            aiVoiceNow: aiVoiceNow.current
                                        }
                                    });
                                }
                                aiVoiceList.current.shift();
                                aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                                console.log('剩余语音列表', aiVoiceList.current);
                            }
                        }
                    });

                    audio.onError((res) => {
                        clearAudioDurationTimer();
                        if (aiVoiceNow.current) {
                            if (currentSpeech.id === aiVoiceNow.current.id) {
                                aiVoiceList.current.shift();
                                aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                            }
                        }
                        setSubtitleText('');
                        audio.offPlay();
                        audio.offEnded();
                        audio.offError();
                        Taro.showToast({
                            title: '语音播放错误',
                            icon: 'none'
                        });
                        addLog({
                            level: 'error',
                            message: 'audio error',
                            data: {
                                error: res,
                                ...aiVoiceNow.current
                            }
                        });
                    });
                    addLog({
                        level: 'debug',
                        message: '开始播放语音',
                        data: {
                            ...aiVoiceNow.current
                        }
                    });
                    clearAudioDurationTimer();
                    changeStatus(VoiceStatus.Speaking);
                    // console.log(`开始播放语音`, aiVoiceNow.current,  new Date().toLocaleString());
                    // saveLogs({
                    //     action: '执行播放音频',
                    //     content: aiVoiceNow.current?.text
                    // })
                    audio.play();
                } catch (e) {
                    clearAudioDurationTimer();
                    if (aiVoiceNow.current) {
                        if (currentSpeech.id === aiVoiceNow.current.id) {
                            aiVoiceList.current.shift();
                            aiVoiceNow.current.status = SpeechStatus.PlayEnd;
                        }
                    }
                    setSubtitleText('');
                    audio.offPlay();
                    audio.offEnded();
                    audio.offError();
                    Taro.showToast({
                        title: '语音播放错误',
                        icon: 'none'
                    });
                    addLog({
                        level: 'error',
                        message: 'audio error',
                        data: {
                            error: e,
                            ...aiVoiceNow.current
                        }
                    });
                }
            }
        }
        if (
            aiSentence.current.length === 0 &&
            aiVoiceList.current.length === 0 &&
            aiVoiceNow.current?.status === SpeechStatus.PlayEnd &&
            aiSentenceStatus.current === SpeechStatus.PlayEnd
        ) {
            // ai回复完毕，继续聆听
            addLog({
                level: 'trace',
                message: 'ai语音播报完毕,继续聆听'
            });
            // saveLogs({
            //     action: '音频播放完毕',
            //     content: ''
            // })
            stopAISpeaking();
            if (!isFinish.current) {
                if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                    changeStatus(VoiceStatus.WaitListen);
                } else {
                    changeStatus(VoiceStatus.Listening);
                    start();
                }
            }
        }
    };

    const { run: handleTouchStart } = useThrottleFn(
        (e: any) => {
            addLog({
                level: 'debug',
                message: 'handleTouchStart',
                data: {
                    status: statusRef.current
                }
            });

            if (statusRef.current === VoiceStatus.WaitListen) {
                cancelHello();
                setSayPressed(true);
                Taro.vibrateShort({ type: 'heavy' });
                changeStatus(VoiceStatus.BeforeListen);
                start();
            }
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    const handleTouchEnd = (e: any) => {
        console.log('handleTouchEnd', getVoiceStatusKey(statusRef.current), e.touches, e.changedTouches);
        addLog({
            level: 'debug',
            message: 'handleTouchEnd',
            data: {
                status: statusRef.current
            }
        });
        // saveLogs({
        //     action: '手指抬起',
        //     content: ''
        // })
        if (statusRef.current === VoiceStatus.Listening) {
            changeStatus(VoiceStatus.Waiting);

            stop();
            setSayPressed(false);
        } else if (statusRef.current === VoiceStatus.Cancel) {
            changeStatus(VoiceStatus.WaitListen);
            setSayPressed(false);
            stop();
        } else if (statusRef.current === VoiceStatus.BeforeListen) {
            changeStatus(VoiceStatus.WaitListen);
            setSayPressed(false);
        } else if (statusRef.current === VoiceStatus.WaitListen) {
            setSayPressed(false);
        }
    };

    /* const { run: handleTouchEnd } = useThrottleFn(
        (e: any) => {
            console.log('handleTouchEnd', getVoiceStatusKey(statusRef.current), e.touches, e.changedTouches);
            addLog({
                level: 'debug',
                message: 'handleTouchEnd',
                data: {
                    status: statusRef.current
                }
            });
            if (statusRef.current === VoiceStatus.Listening) {
                changeStatus(VoiceStatus.Waiting);

                stop();
                setSayPressed(false);
            } else if (statusRef.current === VoiceStatus.Cancel || statusRef.current === VoiceStatus.BeforeListen) {
                changeStatus(VoiceStatus.WaitListen);
                setSayPressed(false);
                stop();
            }
        },
        {
            wait: 500,
            leading: false,
            trailing: true
        }
    ); */

    const { run: handleTouchMove } = useThrottleFn(
        (e: any) => {
            console.log('handleTouchMove', getVoiceStatusKey(statusRef.current), e.touches, e.changedTouches);
            if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Cancel) {
                if (touchTop.current) {
                    if (e.changedTouches[0].clientY < touchTop.current) {
                        // 超出区域
                        changeStatus(VoiceStatus.Cancel);
                    } else {
                        changeStatus(VoiceStatus.Listening);
                    }
                } else {
                    createSelectorQuery()
                        .select('#actionfooter')
                        .boundingClientRect((res: NodesRef.BoundingClientRectCallbackResult) => {
                            touchTop.current = res.top;
                            console.log('handleTouchMove', e.changedTouches, e.touches, res);
                            if (e.changedTouches[0].clientY < res.top) {
                                // 超出区域
                                changeStatus(VoiceStatus.Cancel);
                            } else {
                                changeStatus(VoiceStatus.Listening);
                            }
                        })
                        .exec();
                }
            }
        },
        {
            wait: 100
        }
    );
    function validateBuffer(buffer: any) {
        const { byteLength } = buffer;

        // 确保长度是2的倍数（每个样本占2字节）
        if (byteLength % 2 !== 0) {
            buffer = buffer.slice(0, byteLength - 1);
        }

        return new Int16Array(buffer);
    }
    // 音量计算函数
    function calculateDecibel(buffer: any) {
        // 转换为16位整型数组（微信小程序默认PCM格式）
        const int16Array = validateBuffer(buffer);

        // 计算均方根值（RMS）
        let sum = 0;
        for (const sample of int16Array) {
            sum += sample * sample;
        }
        const rms = Math.sqrt(sum / int16Array.length);

        // 转换为分贝值（参考公式：dB = 20 * log10(rms / 32768))
        const decibel = 20 * Math.log10(rms / 32768);

        // 标准化到0-100区间（静音≈30dB，正常说话≈60dB）
        const minDb = -90; // 最小分贝值
        const maxDb = 0; // 最大分贝值
        return Math.max(0, Math.min(100, ((decibel - minDb) / (maxDb - minDb)) * 100));
    }
    useMount(() => {
        console.log('mount');
        try {
            const { appId } = Taro.getAccountInfoSync().miniProgram;
            setIsQnq(appId === AppIdConsts.qnq);
        } catch (error) {}
        // 请在页面onLoad时初始化好下列函数并确保腾讯云账号信息已经设置
        const voice_interaction_type = Storage.get(StorageEnvKey.SKILL_INTERACTION_TYPE) || VoiceInteraction.Manual;
        const chatCurrentMode = Storage.get(StorageEnvKey.CHAT_MODE) || ChatMode.SHUZIREN;
        setShowVideoClose(chatCurrentMode);
        const subtitle_visible = Storage.get(StorageEnvKey.SUBTITLE_VISIBLE) || SubtitleVisible.Hide;
        voiceInteractionRef.current = voice_interaction_type;
        setVoiceInteraction(voice_interaction_type);
        setSubtitleShow(subtitle_visible);

        const voiceParams = {
            // 用户参数
            secretkey: config.QCloudAIVoice.secretKey,
            secretid: config.QCloudAIVoice.secretId,
            appid: config.QCloudAIVoice.appId, // 腾讯云账号appid（非微信appid）
            engine_model_type: '16k_zh_medical',
            needvad: 1, // 0：关闭 vad，1：开启 vad，默认为0。如果语音分片长度超过60秒，用户需开启 vad（人声检测切分功能）
            vad_silence_time: 1500, // 语音断句检测阈值，静音时长超过该阈值会被认为断句（多用在智能客服场景，需配合 needvad = 1 使用），取值范围：240-2000（默认1000），单位 ms
            noise_threshold: 0.2, // 噪音参数阈值，默认为0，取值范围：[-1,1]，对于一些音频片段，取值越大，判定为噪音情况越大。取值越小，判定为人声情况越大。慎用：可能影响识别效果
            hotword_id: config.QCloudAIVoice.hotword_id,
            customization_id: config.QCloudAIVoice.customization_id,
            convert_num_mode: 3 // 0：不转换，直接输出中文数字，1：根据场景智能转换为阿拉伯数字，3: 打开数学相关数字转换。默认值为1
        };
        if (voiceInteractionRef.current === VoiceInteraction.Manual) {
            // 如果是按住说话的，取消说话检测
            voiceParams.needvad = 0;
        }

        speechRecognizerManager.current = new AudioSpeechRecognizer(voiceParams, false);
        // 开始识别
        speechRecognizerManager.current.OnRecognitionStart = (res: any) => {
            // 开始识别之后
            recognizeStatusRef.current = RecognizeStatus.Start;
            addLog({
                level: 'debug',
                message: 'handleRecognitionStart',
                data: {
                    res,
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current
                }
            });
            // saveLogs({
            //     action: '识别start',
            //     content: ''
            // })
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current === VoiceStatus.Canceled) return;

                changeStatus(VoiceStatus.Listening);

                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
                if (timerNoSentence.current) {
                    clearTimeout(timerNoSentence.current);
                }
                recognizeTimeRef.current = setInterval(() => {
                    setRecognizerTime((prev) => {
                        if (prev === 0) {
                            clearRecognizerTimer();
                            return 0;
                        }
                        return prev - 1;
                    });
                }, 1000);
                timerOvertime.current = setTimeout(() => {
                    // 超过5s没说话，进入超时阶段
                    addLog({
                        level: 'trace',
                        message: '超时'
                    });
                    changeStatus(VoiceStatus.Overtime);
                    if (timerStandby.current) {
                        clearTimeout(timerStandby.current);
                    }
                    timerStandby.current = setTimeout(() => {
                        addLog({
                            level: 'trace',
                            message: '待机'
                        });
                        if (timerNoSentence.current) {
                            clearTimeout(timerNoSentence.current);
                        }
                        if (timerOvertime.current) {
                            clearTimeout(timerOvertime.current);
                        }
                        if (timerStandby.current) {
                            clearTimeout(timerStandby.current);
                        }
                        userSentence.current = undefined;
                        changeStatus(VoiceStatus.Standby);
                        stop();
                    }, standbyTime);
                }, overtime);
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.WaitListen) {
                    stop();
                }
                if (statusRef.current === VoiceStatus.BeforeListen) {
                    changeStatus(VoiceStatus.Listening);
                }
            }
        };
        // 一句话开始
        speechRecognizerManager.current.OnSentenceBegin = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.SentenceBegin;
            addLog({
                level: 'debug',
                message: '一句话开始',
                data: {
                    res,
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current
                }
            });
            // saveLogs({
            //     action: '一句话开始',
            //     content: ''
            // })
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (status === VoiceStatus.Canceled) return;
                cancelHello(); // 取消开场白
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
                if (statusRef.current === VoiceStatus.Overtime) {
                    changeStatus(VoiceStatus.Listening);
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.WaitListen) {
                    // stop();
                }
            }
        };
        // 识别变化时
        speechRecognizerManager.current.OnRecognitionResultChange = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.SentenceChange;
            // addLog({
            //     level: 'debug',
            //     message: '识别变化时',
            //     data: {
            //         res,
            //         voiceInteraction: voiceInteractionRef.current,
            //         status: statusRef.current
            //     }
            // });
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (timerNoSentence.current) {
                    clearTimeout(timerNoSentence.current);
                }
                timerNoSentence.current = setTimeout(() => {
                    stop();
                    changeStatus(VoiceStatus.Standby);
                    Taro.showToast({
                        icon: 'none',
                        title: '未识别到说话'
                    });
                    addLog({
                        level: 'warn',
                        message: '长时间没有识别到'
                    });
                }, 10000);
            }
        };

        // 一句话结束
        speechRecognizerManager.current.OnSentenceEnd = (res: any) => {
            if (res.result.word_size === 0) {
                recognizeStatusRef.current = RecognizeStatus.SentenceEnd;
                // 超过1.5s不说话，停止语音识别，发给ai
                const resSentence = res.result.voice_text_str;
                addLog({
                    level: 'debug',
                    message: '一句话结束',
                    data: {
                        res,
                        voiceInteraction: voiceInteractionRef.current,
                        status: statusRef.current
                    }
                });
                // saveLogs({
                //     action: '一句话结束',
                //     content: resSentence
                // });
                if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                    if (statusRef.current === VoiceStatus.Canceled) return;
                    if (timerNoSentence.current) {
                        clearTimeout(timerNoSentence.current);
                    }
                    if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Overtime) {
                        if (timerOvertime.current) {
                            clearTimeout(timerOvertime.current);
                        }
                        if (timerStandby.current) {
                            clearTimeout(timerStandby.current);
                        }
                        if (resSentence.trim()) {
                            console.warn('一句话结束', userSentence.current, resSentence);
                            if (userSentence.current === null || userSentence.current === undefined) {
                                addLog({
                                    level: 'trace',
                                    message: '自动模式，文字先出'
                                });
                                userSentence.current = { text: resSentence };

                                stop();
                                changeStatus(VoiceStatus.Waiting);
                            } else {
                                addLog({
                                    level: 'debug',
                                    message: '自动模式，已有录音,进行保存',
                                    data: {
                                        text: userSentence.current.text,
                                        fileSize: userSentence.current?.fileSize
                                    }
                                });
                                userSentence.current.text = resSentence;
                                stop();
                                onSentence({ ...userSentence.current });
                                userSentence.current = undefined;
                            }
                        } else {
                            if (userSentence.current === null || userSentence.current === undefined) {
                                // 录音先出
                                userSentence.current = { text: '' };
                            } else {
                                userSentence.current = undefined;
                            }
                            changeStatus(VoiceStatus.Standby);
                            stop();
                            if (getTimeout()) {
                                doneActionSheet();
                            }
                            const systemInfo = Taro.getSystemInfoSync();
                            const { platform } = systemInfo;
                            if (platform === 'ios') {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字或麦克风被占用'
                                });
                            } else {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字'
                                });
                            }
                            addLog({
                                level: 'trace',
                                message: '自动模式，未识别到文字,等待'
                            });
                        }
                    }
                } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                    if (statusRef.current === VoiceStatus.Waiting) {
                        if (timerNoSentence.current) {
                            clearTimeout(timerNoSentence.current);
                        }
                        if (resSentence.trim()) {
                            handleAiHelpClose();
                            const rdt = removeDuplicateText(resSentence);
                            if (userSentence.current === null || userSentence.current === undefined) {
                                userSentence.current = { text: rdt };
                                addLog({
                                    level: 'trace',
                                    message: '手动模式，文字先出'
                                });
                            } else {
                                userSentence.current.text = rdt;
                                addLog({
                                    level: 'debug',
                                    message: '手动模式,进行保存',
                                    data: {
                                        text: userSentence.current.text,
                                        fileSize: userSentence.current?.fileSize
                                    }
                                });
                                onSentence({ ...userSentence.current });
                                userSentence.current = undefined;
                            }
                        } else {
                            if (userSentence.current === null || userSentence.current === undefined) {
                                userSentence.current = { text: '' };
                            } else {
                                userSentence.current = undefined;
                            }
                            changeStatus(VoiceStatus.WaitListen);
                            if (getTimeout()) {
                                doneActionSheet();
                            }
                            const systemInfo = Taro.getSystemInfoSync();
                            const { platform } = systemInfo;
                            if (platform === 'ios') {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字或麦克风被占用'
                                });
                            } else {
                                Taro.showToast({
                                    icon: 'none',
                                    title: '未识别到文字'
                                });
                            }
                            addLog({
                                level: 'trace',
                                message: '手动模式，未识别到文字,等待'
                            });
                        }
                    } else if (statusRef.current === VoiceStatus.WaitListen) {
                        // stop();
                    } else if (statusRef.current === VoiceStatus.Listening) {
                        // 概率性，手指抬起前，有文字识别出来

                        // 如果是等待AI回复状态
                        if (userSentence.current) {
                            userSentence.current = undefined;
                        }
                        changeStatus(VoiceStatus.WaitListen);
                        if (getTimeout()) {
                            doneActionSheet();
                        }
                        const systemInfo = Taro.getSystemInfoSync();
                        const { platform } = systemInfo;
                        if (platform === 'ios') {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字或麦克风被占用'
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: '未识别到文字'
                            });
                        }

                        addLog({
                            level: 'trace',
                            message: '手动模式，未抬起按钮，就有文字识别出来'
                        });
                    }
                }
            } else {
                addLog({
                    level: 'warn',
                    message: '一句话重复',
                    data: {
                        res
                    }
                });
            }
        };
        // 识别结束
        speechRecognizerManager.current.OnRecognitionComplete = (res: any) => {
            recognizeStatusRef.current = RecognizeStatus.Complete;
            addLog({
                level: 'debug',
                message: '识别结束',
                data: {
                    res,
                    status: statusRef.current
                }
            });
        };
        // 识别错误
        speechRecognizerManager.current.OnError = (res: any) => {
            addLog({
                level: 'error',
                message: '识别错误',
                data: {
                    res
                }
            });
            // code为6001时，国内站用户请检查是否使用境外代理，如果使用请关闭。境外调用需开通国际站服务
            console.info('识别失败', res, getVoiceStatusKey(statusRef.current));
            recognizeStatusRef.current = RecognizeStatus.Error;
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
                changeStatus(VoiceStatus.Standby);
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                changeStatus(VoiceStatus.WaitListen);
            }
            userSentence.current = undefined;
            // handleErrorDialog('语音识别异常，请刷新后重试');
            if (res.code) {
                if (res.code === '4006' || res.code === 4006) {
                    Taro.showToast({
                        icon: 'none',
                        title: '当前访问人数过多，\r\n请稍后重试'
                    });
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '说话时间太短'
                    });
                }
            } else {
                if (res.errMsg && res.errMsg.includes('system permission denied')) {
                    Taro.showToast({
                        icon: 'none',
                        title: `请开启${isWework === '0' ? '微信' : '企业微信'}麦克风权限`
                    });
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '录音错误'
                    });
                }
            }
            // stop();
        };
        // 录音结束（最长10分钟）时回调
        speechRecognizerManager.current.OnRecorderStop = (res: any) => {
            addLog({
                level: 'debug',
                message: '录音结束',
                data: {
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current,
                    fileSize: res.length,
                    filePath: res.tempFilePath,
                    userSentence: userSentence.current
                }
            });
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (userSentence.current === undefined || userSentence.current === null) {
                    // 如果文字还没识别，先创建，文字识别出来后改写
                    addLog({
                        level: 'trace',
                        message: 'RecorderStop 自动模式，录音先出来'
                    });
                    if (recognizeStatusRef.current === RecognizeStatus.Start) {
                        // 如果没有回调sentence
                        if (getTimeout()) {
                            doneActionSheet();
                        }
                    } else {
                        userSentence.current = {
                            text: '',
                            filePath: res.tempFilePath,
                            fileSize: res.fileSize
                        };
                    }
                } else {
                    // 如果文字先识别，再去识别改写
                    addLog({
                        level: 'trace',
                        message: 'RecorderStop 自动模式，文字已有，得到录音，进行保存'
                    });
                    userSentence.current.filePath = res.tempFilePath;
                    userSentence.current.fileSize = res.fileSize;
                    if (userSentence.current.text.trim()) {
                        onSentence({ ...userSentence.current });
                    }
                    userSentence.current = undefined;
                }
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                    timerOvertime.current = null;
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                    timerStandby.current = null;
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.Waiting) {
                    if (userSentence.current === undefined || userSentence.current === null) {
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动模式录音先出来'
                        });
                        userSentence.current = {
                            text: '',
                            filePath: res.tempFilePath,
                            fileSize: res.fileSize
                        };
                        if (timerNoSentence.current) {
                            clearTimeout(timerNoSentence.current);
                        }
                        timerNoSentence.current = setTimeout(() => {
                            if (
                                (!userSentence.current || !userSentence.current.text) &&
                                statusRef.current === VoiceStatus.Waiting
                            ) {
                                // 读取文件并转换成base64
                                const fs = getFileSystemManager();
                                fs.readFile({
                                    filePath: res.tempFilePath,
                                    encoding: 'base64',
                                    success: (fileRes: any) => {
                                        addLog({
                                            level: 'error',
                                            message: '未识别的音频',
                                            data: {
                                                fileRes
                                            }
                                        });
                                    }
                                });
                                userSentence.current = undefined;
                                const systemInfo = Taro.getSystemInfoSync();
                                const { platform } = systemInfo;
                                if (platform === 'ios') {
                                    Taro.showToast({
                                        icon: 'none',
                                        title: '未识别到文字或麦克风被占用'
                                    });
                                } else {
                                    Taro.showToast({
                                        icon: 'none',
                                        title: '未识别到文字'
                                    });
                                }
                                addLog({
                                    level: 'warn',
                                    message: 'RecorderStop 手动模式，sdk没有返回识别到的文字，切换到等待聆听状态'
                                });
                                changeStatus(VoiceStatus.WaitListen);
                            }
                        }, 5000);
                    } else {
                        userSentence.current.filePath = res.tempFilePath;
                        userSentence.current.fileSize = res.fileSize;

                        onSentence({ ...userSentence.current });
                        userSentence.current = undefined;
                        addLog({
                            level: 'trace',
                            message: 'RecorderStop 手动模式，文字已有，得到录音，进行保存'
                        });
                    }
                } else if (
                    statusRef.current === VoiceStatus.WaitListen ||
                    statusRef.current === VoiceStatus.Listening
                ) {
                    userSentence.current = undefined;
                }
            }

            recognizeStatusRef.current = RecognizeStatus.RecorderStop;
            // if (statusRef.current !== VoiceStatus.Stop) {
            //     start();
            // }

            // 小程序目前支持最大时长为10分钟，此参数提供给用户自定义设置，若不传，则为10分钟，超过10分钟，录音自动停止，插件也会关闭连接。
            // 若需要超过10分钟的场景，需要用户自己处理。可在 OnRecorderStop 回调中判断，重新开启录音，建立连接。
        };
        speechRecognizerManager.current.OnRecorderPause = () => {
            addLog({
                level: 'debug',
                message: '录音暂停',
                data: {
                    voiceInteraction: voiceInteractionRef.current,
                    status: statusRef.current
                }
            });

            Taro.showToast({
                icon: 'none',
                title: '录音被打断，请重新开始'
            });
            if (timerNoSentence.current) {
                clearTimeout(timerNoSentence.current);
            }
            userSentence.current = undefined;
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                changeStatus(VoiceStatus.Standby);
                if (timerNoSentence.current) {
                    clearTimeout(timerNoSentence.current);
                }
                if (timerOvertime.current) {
                    clearTimeout(timerOvertime.current);
                }
                if (timerStandby.current) {
                    clearTimeout(timerStandby.current);
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                changeStatus(VoiceStatus.WaitListen);
            }
        };

        speechRecognizerManager.current.OnFrameRecorded = (res: any) => {
            const { frameBuffer } = res;

            // 2. 计算音量分贝值
            const decibel = calculateDecibel(frameBuffer);
            if (isadd == 1) {
                setdecibelPlay(decibel + 10);
                setisZore(decibel);
                isadd = 2;
                // setisadd(2);
            } else {
                setdecibelPlay(decibel + 40);
                setisZore(decibel);
                // setisadd(1);
                isadd = 1;
            }
        };

        speechRecognizerManager.current.OnLog = (log: LogInfo) => {
            addLog(log);
        };

        const systemInfo = Taro.getSystemInfoSync();
        // const isIOS18 = (systemInfo.platform === 'ios' || systemInfo.platform === 'iOS') && systemInfo.system.split(' ')[1].split('.')[0] === '18';
        // console.log('isIOS18', isIOS18);

        if (isWework === '0') {
            Taro.request({ url: `${config.cdnPrefix}chat/lottie_bubble.json` }).then(({ data }) => {
                createSelectorQuery()
                    .select('#bubble')
                    .node((res) => {
                        const canvas = res.node;
                        lottie.setup(canvas);
                        bubbleAnimate.current = lottie.loadAnimation({
                            loop: true,
                            autoplay: true,
                            rendererSettings: {
                                context: canvas.getContext('2d')
                            },
                            animationData: data
                        });
                    })
                    .exec();
            });
            Taro.request({ url: `${config.cdnPrefix}chat/lottie_wave.json` }).then(({ data }) => {
                createSelectorQuery()
                    .select('#wave')
                    .node((res) => {
                        const canvas = res.node;
                        const context = canvas.getContext('2d');
                        const dpr = systemInfo.pixelRatio;
                        canvas.width = canvas.width * dpr;
                        canvas.height = canvas.height * dpr;
                        context.scale(dpr, dpr);

                        lottie.setup(canvas);
                        waveAnimate.current = lottie.loadAnimation({
                            loop: true,
                            autoplay: true,
                            rendererSettings: {
                                context: canvas.getContext('2d')
                            },
                            animationData: data
                        });
                    })
                    .exec();
            });
        }

        onLoaded();
    });
    useUnmount(() => {
        console.log('unMount');
        if (voiceInteractionRef.current === VoiceInteraction.Auto) {
            finish();
        } else {
            finishManual();
        }
        if (bubbleAnimate.current) {
            bubbleAnimate.current.destroy();
        }
        if (waveAnimate.current) {
            waveAnimate.current.destroy();
        }
        setKeepScreenOn({
            keepScreenOn: false,
            fail(res: any) {
                console.log('keepScreenOff error', res);
            }
        });
    });

    useEffect(() => {
        if (chat && chat.eman.tone) {
            try {
                const parseTone = JSON.parse(chat.eman.tone);
                console.log('parTone', parseTone);
                if (parseTone.speed !== null && parseTone.speed !== undefined) {
                    tone.current.speed = parseTone.speed;
                }
                if (parseTone.volume !== null && parseTone.volume !== undefined) {
                    tone.current.volume = parseTone.volume;
                }
                if (parseTone.voiceType !== null && parseTone.voiceType !== undefined) {
                    tone.current.voiceType = parseTone.voiceType;
                }
                if (parseTone.name !== null && parseTone.name !== undefined) {
                    tone.current.name = parseTone.name;
                }
                console.log('tone', tone.current);
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'tone格式错误',
                    data: {
                        error
                    }
                });
            }
        }
    }, [chat]);

    const networkWeakListener = (res: { weakNet: boolean; networkType: string }) => {
        console.log('networkWeakListener', res);
        if (res.weakNet) {
            ToastVoiceDialog_.show({
                message: '当前网络状况不佳',
                position: 'top'
            });

            addLog({
                level: 'warn',
                message: '当前网络状况不佳',
                data: {
                    networkType: res.networkType,
                    weakNet: res.weakNet
                }
            });
        }
    };

    useEffect(() => {
        console.log('show', show);
        if (show) {
            createSelectorQuery()
                .select('#actionfooter')
                .boundingClientRect((res: any) => {
                    if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Cancel) {
                        console.log('footerheight', res);
                        touchTop.current = res.top;
                    }
                })
                .exec();
            if (isWework === '0') {
                onNetworkWeakChange(networkWeakListener);
            }
            setKeepScreenOn({
                keepScreenOn: true,
                fail(res: any) {
                    console.log('keepScreenOn error', res);
                }
            });
            setInnerAudioOption({ obeyMuteSwitch: false });
            console.log('eman', chat?.eman);
        } else {
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                finish();
            } else {
                finishManual();
            }
            if (isWework === '0') {
                offNetworkWeakChange(networkWeakListener);
            }
            setKeepScreenOn({
                keepScreenOn: false,
                fail(res: any) {
                    console.log('keepScreenOff error', res);
                }
            });
        }
    }, [show]);

    useImperativeHandle(ref, () => ({
        // ai回复中
        speakProcess: (text: string) => {
            if (!show || isFinish.current) return;
            console.log('ai回复中', text);
            aiSentenceStatus.current = SpeechStatus.Playing;

            // 优化语音断句问题
            const regexBrackets = /（/;
            if (!regexBrackets.test(text) && text.match(/[?!'";)。？！’”；)》……——]$/)) {
                if (tempAiSentence.current) {
                    const fullSentence = tempAiSentence.current + text;
                    tempAiSentence.current = '';
                    handleSentence(fullSentence);
                } else {
                    handleSentence(text);
                }
            } else {
                console.log('拼接');
                if (tempAiSentence.current) {
                    tempAiSentence.current += text;
                } else {
                    tempAiSentence.current = text;
                }
            }
        },
        // ai开始回复
        speakStart() {
            if (!show || isFinish.current) return;
            addLog({
                level: 'trace',
                message: 'ai开始回复'
            });

            isFirst.current = true;

            if (intervalSpeech.current) {
                clearInterval(intervalSpeech.current);
            }

            if (intervalText.current) {
                clearInterval(intervalText.current);
            }
            intervalText.current = setInterval(() => {
                if (aiSentence.current && aiSentence.current.length > 0) {
                    const nextText = aiSentence.current.shift();
                    console.log('nextText', nextText);
                    console.log('剩余文本', aiSentence.current);
                    if (nextText !== undefined && nextText.trim() !== '') {
                        textToSpeech(nextText);
                    }
                }
            }, 30);

            // 定时检查ai回复的语音列表
            intervalSpeech.current = setInterval(() => {
                handleVoicePlay();
            }, 30);
        },
        speakEnd() {
            addLog({
                level: 'trace',
                message: 'ai结束回复',
                data: {
                    tempAiSentence: tempAiSentence.current
                }
            });
            if (tempAiSentence.current) {
                textToSpeech(tempAiSentence.current);
                tempAiSentence.current = '';
            }
            aiSentenceStatus.current = SpeechStatus.PlayEnd;
            isFirst.current = false;
        },
        finish() {
            isFinish.current = true;
            finish();
        },
        startListen() {
            addLog({
                level: 'trace',
                message: 'startListen'
            });
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                start();
            } else {
                changeStatus(VoiceStatus.WaitListen);
            }
        },
        changeStatus,
        cancelListen() {
            addLog({
                level: 'trace',
                message: 'cancelListen'
            });
            if (timerOvertime.current) {
                clearTimeout(timerOvertime.current);
            }
            if (timerStandby.current) {
                clearTimeout(timerStandby.current);
            }
            changeStatus(VoiceStatus.Canceled);
            stop();
        },
        handleTimeOut() {
            if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                if (statusRef.current === VoiceStatus.Listening || statusRef.current === VoiceStatus.Overtime) {
                    // 语音识别中，停止识别
                    stop();
                } else {
                    doneActionSheet();
                }
            } else if (voiceInteractionRef.current === VoiceInteraction.Manual) {
                if (statusRef.current === VoiceStatus.Listening) {
                    // 语音识别中，停止识别
                    changeStatus(VoiceStatus.Waiting);
                    stop();
                    setSayPressed(false);
                } else if (statusRef.current === VoiceStatus.BeforeListen) {
                    changeStatus(VoiceStatus.Canceled);
                    setSayPressed(false);
                    doneActionSheet();
                } else {
                    doneActionSheet();
                }
            }
        },
        getRecognizerStatus() {
            return recognizeStatusRef.current;
        }
    }));

    return (
        // <PageContainer
        //     show={show}
        //     position='right'
        //     duration={duration}
        //     onBeforeLeave={handleClose}
        //     onAfterEnter={() => {
        //         if (duration === 0) {
        //             setDuration(300);
        //         }
        //     }}
        // >
        <View
            className={styles.container}
            catch-move
            style={{ transform: show ? 'translateX(0)' : 'translateX(100%)' }}
        >
            <Image src={backgroundBlur} mode='aspectFill' className={styles.bg} />
            {isWework === '1' ? <View className={styles.blur} /> : <View className={styles.blurbg} />}
            <NavBar
                className={styles.navbar}
                title={chat?.eman?.name}
                renderLeft={
                    voiceInteraction === VoiceInteraction.Manual && (
                        <Icon name='cross' size={pxTransform(40)} color='#ffffff' />
                    )
                }
                safeAreaInsetTop
                onClickLeft={() => {
                    if (voiceInteraction === VoiceInteraction.Manual) {
                        handleFinish();
                    }
                }}
            />

            <View className={styles.content}>
                <View className={styles.avatar_content}>
                    <View className={styles.avatar_box}>
                        <Canvas
                            className={classNames(styles.wave, {
                                [styles.wave_active]: status === VoiceStatus.Speaking && show
                            })}
                            id='wave'
                            type='2d'
                        />
                        <Image
                            src={chat?.eman?.avatar ?? AvatarDefault}
                            mode='aspectFill'
                            className={classNames(styles.avatar_img, {
                                [styles.avatar_img_active]: status === VoiceStatus.Waiting,
                                [styles.avatar_img_active_b]: status === VoiceStatus.Speaking
                            })}
                        />
                        <Canvas
                            className={classNames(styles.bubble, {
                                [styles.bubble_active]: status === VoiceStatus.Waiting && show
                            })}
                            id='bubble'
                            type='2d'
                        />
                    </View>
                    <View className={styles.scene}>{chat?.scene.scene}</View>
                </View>

                <View className={styles.status_box} onClick={statusTextClick}>
                    {subtitleShow === SubtitleVisible.Show && status === VoiceStatus.Speaking ? (
                        <View className={styles.subtitle_box}>{subtitleText}</View>
                    ) : (
                        <Block>
                            {status == VoiceStatus.Listening ? (
                                <View>
                                    <VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />
                                </View>
                            ) : (
                                <View className={styles.animation}>{statusLoading}</View>
                            )}
                            {/* 字幕 */}
                            <View
                                className={
                                    status === VoiceStatus.Speaking || status === VoiceStatus.Cancel
                                        ? styles.status_lite
                                        : styles.status
                                }
                            >
                                {statusText?.map((item) => (
                                    <View key={item} className={styles.status_text}>
                                        {item}
                                        {voiceInteraction === VoiceInteraction.Auto && status === VoiceStatus.Listening
                                            ? `${recognizerTime}s`
                                            : ''}
                                    </View>
                                ))}
                            </View>
                        </Block>
                    )}
                </View>
            </View>
            <View className={styles.footer} id='actionfooter'>
                <View className={styles.time}>
                    {minutes >= 10 ? minutes : `0${minutes}`}:{seconds >= 10 ? seconds : `0${seconds}`}
                </View>
                <View className={styles.actions}>
                    <View className={styles.action_chat} onClick={handleClose}>
                        <Image
                            src={
                                status === VoiceStatus.BeforeListen ||
                                status === VoiceStatus.Listening ||
                                status === VoiceStatus.Overtime ||
                                status === VoiceStatus.Waiting
                                    ? IconChatDisabled
                                    : IconChat
                            }
                            className={styles.action_chat_icon}
                        />
                    </View>
                    <View className={styles.action_chat} onClick={toggleSubtitle}>
                        <Image
                            src={subtitleShow === SubtitleVisible.Show ? IconSubtitleOn : IconSubtitleOff}
                            className={styles.action_chat_icon}
                        />
                    </View>
                    {aiHelpEnable && (
                        <View className={styles.action_chat} onClick={handleAIHelp}>
                            <Image
                                src={
                                    status === VoiceStatus.Waiting || status === VoiceStatus.Speaking
                                        ? IconHelpOff
                                        : IconHelp
                                }
                                className={styles.action_chat_icon}
                            />
                        </View>
                    )}
                    {/*  */}

                    {chat?.eman?.show3dFlag && chat?.eman.zipFileUrl && (
                        <View className={styles.action_chat} onClick={handleChatMode}>
                            <Image
                                src={showVideoClose === ChatMode.AUDIO ? IconVideoClose : IconVideo}
                                className={styles.action_chat_icon}
                            />
                        </View>
                    )}
                    <View className={styles.action_chat} onClick={showMore}>
                        <Image src={IconMore} className={styles.action_chat_icon} />
                    </View>
                </View>
                {voiceInteraction === VoiceInteraction.Auto ? (
                    <View
                        className={styles.action_close}
                        onClick={() => {
                            addLog({
                                level: 'debug',
                                message: '自动模式，点击挂断',
                                data: {
                                    status: statusRef.current
                                }
                            });
                            handleFinish();
                        }}
                    >
                        <Image src={IconHangup} className={styles.action_close_icon} />
                    </View>
                ) : (
                    <View className={styles.action_say_box}>
                        <Image
                            src={IconHangupMini}
                            className={styles.action_finish_icon_mini}
                            onClick={() => {
                                addLog({
                                    level: 'debug',
                                    message: '手动模式，点击挂断',
                                    data: {
                                        status: statusRef.current
                                    }
                                });
                                handleFinish();
                            }}
                        />
                        <View
                            className={classNames(styles.action_say, {
                                [styles.action_say_disabled]: status === VoiceStatus.Speaking,
                                [styles.action_say_active]: status === VoiceStatus.WaitListen,
                                [styles.action_say_pressed]: sayPressed || status === VoiceStatus.Listening,
                                [styles.action_say_cancel]: status === VoiceStatus.Cancel
                            })}
                            onTouchStart={handleTouchStart}
                            onTouchMove={handleTouchMove}
                            onTouchEnd={handleTouchEnd}
                        >
                            <Image
                                src={
                                    status === VoiceStatus.Waiting || status === VoiceStatus.Speaking
                                        ? IconSpeakDisabled
                                        : IconSpeak
                                }
                                className={styles.action_say_icon_speak}
                            />
                            按住说话
                        </View>
                    </View>
                )}

                <View className={styles.tip}>对话为AI情景模拟，非真实场景</View>
            </View>

            <Aihelp
                show={aiHelpShow}
                keyHeight={keyHeight}
                canGenerate={canGenerate}
                chatId={chatId}
                styleType={styleType}
                onClose={onCloseAi}
            />
            <Dialog_ />
        </View>
        // </PageContainer>
    );
};

export default forwardRef(Index);
