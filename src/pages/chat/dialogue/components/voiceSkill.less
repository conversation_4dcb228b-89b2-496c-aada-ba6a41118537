.container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 100;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    transition: all 0.3s;

    --nav-bar-title-text-color: #fff;
}
.bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
    width: 100%;
    height: 100%;
    background-color: #000;
}
.blur {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(80px);
}
.blurbg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    // backdrop-filter: blur(80px);
}
.no_blur {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0);
}
.navbar {
    position: relative;
    z-index: 2;
    background-color: transparent;
    &::after {
        display: none;
    }
}
.content {
    position: relative;
    z-index: 2;
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: space-evenly;
}
.scene {
    margin-top: 60px;
    margin-right: 82px;
    margin-left: 81px;
    color: rgba(255, 255, 255, 0.4);
    font-size: 28px;
    text-align: center;
}
.status_box {
    width: 100%;
    height: 250px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.subtitle_box {
    background: rgba(0, 0, 0, 0.5);
    border-radius: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 28px;
    padding: 16px 32px;
    color: white;
    width: 80%;
    box-sizing: border-box;
    min-height: 120px;
    max-height: 240px;
    flex-shrink: 0;
    flex-grow: 0;
}
.animation {
    margin-bottom: 20px;
}

.status {
    height: 110px;
    color: #fff;
    font-weight: 600;
    font-size: 36px;
    text-align: center;

    &_lite {
        height: 110px;
        color: rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 24px;
        text-align: center;
    }

    &_text {
        line-height: 50px;
    }
}

.tip {
    margin-top: 48px;
    margin-bottom: 28px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 400;
    font-size: 24px;
    text-align: center;
}

.avatar {
    &_content {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    &_box {
        position: relative;
        width: 320px;
        height: 320px;
        overflow: visible;
    }
    &_img {
        position: relative;
        z-index: 1;
        width: 300px;
        height: 300px;
        border-color: rgba(255, 255, 255, 0);
        border-style: solid;
        border-width: 10px;
        border-radius: 100%;
        transition: all 0.1s ease-in-out;
        &_active {
            border-color: #fff;
            box-shadow: 0 0 14px 8px rgba(255, 255, 255, 0.8);
        }
        &_active_b {
            border: 10px solid #fff;
        }
    }
}

.bubble {
    position: absolute;
    top: -85px;
    right: -168px;
    z-index: 2;
    //display: none;
    opacity: 0;
    width: 336px;
    height: 179px;

    &_active {
        //display: block;
        opacity: 1;
    }
}
.wave {
    position: absolute;
    top: -43px;
    left: -243px;
    z-index: 0;
    //display: none;
    opacity: 0;
    width: 800px;
    height: 400px;

    &_active {
        //display: block;
        opacity: 1;
    }
}

.footer {
    position: relative;
    z-index: 2;
    box-sizing: border-box;
}

.time {
    margin-bottom: 24px;
    padding-top: 30px;
    color: #fff;
    font-weight: 400;
    font-size: 28px;
    text-align: center;
}

.actions {
    display: flex;
    gap: 24px;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
}
.action_close {
    width: 112px;
    height: 112px;
    margin: 0 auto;
    &_icon {
        width: 112px;
        height: 112px;
    }
}
.action_chat {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 108px;
    height: 108px;
    background: rgba(0, 0, 0, 0.5);
    border-radius: 108px;
    &_icon {
        width: 48px;
        height: 48px;
    }
}
.action_finish_icon_mini {
    width: 130px;
    height: 90px;
}
.action_say_box {
    position: relative;
    z-index: 1;
    display: flex;
    gap: 20px;
    padding-right: 48px;
    padding-left: 48px;
    padding-top: 22px;
}
.action_say {
    position: relative;
    z-index: 1;
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: center;
    height: 90px;
    color: rgba(243, 243, 243, 0.5);
    font-size: 28px;
    background-color: rgba(0, 0, 0, 0.2);
    border-radius: 96px;


    &_disabled {
        color: rgba(243, 243, 243, 0.5);
        background-color: rgba(0, 0, 0, 0.2);
    }
    &_active {
        color: #fff;
        background: linear-gradient(270deg, #6742ff 0%, #3d83ff 100%);
    }
    &_pressed {
        color: #fff;
        background: linear-gradient(270deg, #5237cc 0%, #3269cc 100%);

        &::after {
            position: absolute;
            top: -10px;
            left: -3%;
            z-index: 0;
            width: 106%;
            height: 110px;
            background: linear-gradient(270deg, rgba(103, 66, 255, 0.3) 0%, rgba(61, 131, 255, 0.3) 100%);
            border-radius: 130px;
            content: '';
        }
    }
    &_cancel {
        color: #fff;
        background: #ff4545;
        &::after {
            display: none;
        }
  
    }
    &_complete {
        margin-left: 53px;
        margin-right: 53px;
    }

    &_icon_speak {
        width: 48px;
        height: 48px;
        margin-right: 8px;
    }
}

.cancel_icon {
    width: 56px;
    height: 56px;
}

.error_img {
    width: 120px;
    height: 120px;
}
.error_tip {
    font-size: 24px;
    text-align: center;
}
