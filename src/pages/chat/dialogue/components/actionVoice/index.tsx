import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { VoiceInteraction } from '@/constants/voicetype';
import { Cell, Image, Popup, RadioGroup } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';
type Props = {
    show: boolean;
    type: 'QUESTION_INTERACTION_TYPE' | 'SKILL_INTERACTION_TYPE' | 'INTELLIGENCE_INTERACTION_TYPE';
    onClose: () => void;
    onConfirm: () => void;
};

const IconCheckDefault = `${config.cdnPrefix}svg/check_default.svg`;

const Index: React.FC<Props> = (props) => {
    const { show, type, onClose, onConfirm } = props;
    const IconCheckActive = `${config.cdnPrefix}check_active.svg`;
    const [voiceInteractionType, setVoiceInteractionType] = useState<VoiceInteraction>();
    const voiceInteractionRef = useRef();
    const handleVoiceItemClick = (item: VoiceInteraction) => {
        setVoiceInteractionType(item);
    };
    const voicePopupCancel = () => {
        setVoiceInteractionType(voiceInteractionRef.current);
        onClose();
    };
    const voicePopupConfirm = () => {
        Storage.set(StorageEnvKey[type], voiceInteractionType);
        onClose();
        onConfirm();
    };

    useEffect(() => {
        setVoiceInteractionType(Storage.get(StorageEnvKey[type]) || VoiceInteraction.Manual);
    }, [show, type]);

    return (
        <Popup position='bottom' show={show} closeable onClose={onClose} safeAreaInsetBottom round zIndex={110}>
            <View className={styles.voicetype_popup_title}>语音互动设置</View>
            <View className={styles.voicetype_popup_tip}>修改成功后，将于下次练习生效</View>
            <View className={styles.voicetype_popup_list}>
                <RadioGroup direction='vertical' value={voiceInteractionType}>
                    <Cell
                        border={false}
                        renderTitle={
                            <View>
                                <View className={styles.voicetype_popup_head}>自动发送语音</View>
                                <View className={styles.voicetype_popup_subhead}>
                                    自动识别语音停顿，自动发送语音，此模式最长录音
                                    <Text className={styles.m}>60s</Text>
                                </View>
                            </View>
                        }
                        renderIcon={
                            <View className={styles.voicetype_popup_checkbox}>
                                <Image
                                    className={classNames(styles.voicetype_popup_check, {
                                        [styles.active]: voiceInteractionType === VoiceInteraction.Auto
                                    })}
                                    src={IconCheckActive}
                                />
                                <Image
                                    className={classNames(styles.voicetype_popup_check, {
                                        [styles.active]: voiceInteractionType !== VoiceInteraction.Auto
                                    })}
                                    src={IconCheckDefault}
                                />
                            </View>
                        }
                        key={VoiceInteraction.Auto}
                        clickable
                        onClick={() => handleVoiceItemClick(VoiceInteraction.Auto)}
                    />
                    <Cell
                        border={false}
                        renderTitle={
                            <View>
                                <View className={styles.voicetype_popup_head}>长按发送语音</View>
                                <View className={styles.voicetype_popup_subhead}>按住识别语音，松手发送语音</View>
                            </View>
                        }
                        renderIcon={
                            <View className={styles.voicetype_popup_checkbox}>
                                <Image
                                    className={classNames(styles.voicetype_popup_check, {
                                        [styles.active]: voiceInteractionType === VoiceInteraction.Manual
                                    })}
                                    src={IconCheckActive}
                                />
                                <Image
                                    className={classNames(styles.voicetype_popup_check, {
                                        [styles.active]: voiceInteractionType !== VoiceInteraction.Manual
                                    })}
                                    src={IconCheckDefault}
                                />
                            </View>
                        }
                        key={VoiceInteraction.Manual}
                        clickable
                        onClick={() => handleVoiceItemClick(VoiceInteraction.Manual)}
                    />
                    {type === 'QUESTION_INTERACTION_TYPE' && (
                        <Cell
                            border={false}
                            renderTitle={
                                <View>
                                    <View className={styles.voicetype_popup_head}>手动发送语音</View>
                                    <View className={styles.voicetype_popup_subhead}>
                                        点击开始录音，完成后再点击结束录音
                                    </View>
                                </View>
                            }
                            renderIcon={
                                <View className={styles.voicetype_popup_checkbox}>
                                    <Image
                                        className={classNames(styles.voicetype_popup_check, {
                                            [styles.active]: voiceInteractionType === VoiceInteraction.TapManual
                                        })}
                                        src={IconCheckActive}
                                    />
                                    <Image
                                        className={classNames(styles.voicetype_popup_check, {
                                            [styles.active]: voiceInteractionType !== VoiceInteraction.TapManual
                                        })}
                                        src={IconCheckDefault}
                                    />
                                </View>
                            }
                            key={VoiceInteraction.TapManual}
                            clickable
                            onClick={() => handleVoiceItemClick(VoiceInteraction.TapManual)}
                        />
                    )}
                </RadioGroup>
            </View>
            <View className={styles.voicetype_popup_actions}>
                <Button
                    onClick={voicePopupCancel}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96),
                        color: '#777777'
                    }}
                    round
                    block
                    color='#F6F6F6'
                >
                    取消
                </Button>
                <Button
                    onClick={voicePopupConfirm}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    确定
                </Button>
            </View>
        </Popup>
    );
};

export default Index;
