import { ActionSheet, Image } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import styles from './index.less';
import config from '@/config';
type Props = {
    show: boolean;
    showCleanContext?: boolean;
    onClose: () => void;
    onScript?: () => void;
    onSetting: () => void;
    onCleanContext?: () => void;
    onFeedback: () => void;
};

const IconCleanBlack = `${config.cdnPrefix}svg/icon_clean_black.svg`;
const IconFeedback = `${config.cdnPrefix}svg/icon_feedback.svg`;
const IconReport = `${config.cdnPrefix}svg/icon_report.svg`;
const IconVoice = `${config.cdnPrefix}svg/icon_voice.svg`;

const Index: React.FC<Props> = (props) => {
    const { show, showCleanContext = false, onClose, onScript, onSetting, onCleanContext, onFeedback } = props;
    return (
        <ActionSheet
            className={styles.action_more}
            zIndex={110}
            show={show}
            onClose={onClose}
            closeOnClickOverlay={false}
            style={{
                // @ts-ignore
                '--action-sheet-close-icon-color': '#272C47'
            }}
            title='更多'
        >
            <View className={styles.action_more_content}>
                {showCleanContext ? (
                    <View className={styles.action_more_item} onClick={onCleanContext}>
                        <Image src={IconCleanBlack} className={styles.action_more_item_icon} />

                        <View className={styles.action_more_item_text}>清除上下文</View>
                    </View>
                ) : (
                    <View className={styles.action_more_item} onClick={onScript}>
                        <Image src={IconReport} className={styles.action_more_item_icon} />

                        <View className={styles.action_more_item_text}>查看脚本</View>
                    </View>
                )}

                <View className={styles.action_more_item} onClick={onSetting}>
                    <Image src={IconVoice} className={styles.action_more_item_icon} />
                    <View className={styles.action_more_item_text}>语音交互设置</View>
                </View>
                <View className={styles.action_more_item} onClick={onFeedback}>
                    <Image src={IconFeedback} className={styles.action_more_item_icon} />
                    <View className={styles.action_more_item_text}>问题反馈</View>
                </View>
            </View>
        </ActionSheet>
    );
};

export default Index;
