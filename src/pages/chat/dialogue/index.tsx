import { Page } from '@/components';
import { ScriptType } from '@/constants/scriptType';
import { getChat, getScript } from '@/services/chat';
import { hideLoading, showLoading, useLoad, useReady, useRouter } from '@tarojs/taro';

import ScriptActionSheet from '@/components/scriptActionSheet';
import type { ChatVO } from '@/types/chat';

import { View } from '@tarojs/components';
import classNames from 'classnames';
import { useState } from 'react';
import DialogQuestion from './components/dialogQuestion/index';
import DialogSkill from './components/dialogSkill/index';
import styles from './index.less';

const App = () => {
    const { params } = useRouter<{
        chatId: string;
        interrupt: string;
        from: string;
        mode: string;
        introductionType: string;
        introductionDelay: string;
        introduction: string;
        text: string;
    }>();
    const { chatId, text, interrupt, from, mode, introductionType, introductionDelay, introduction } = params;
    console.log('pamra', params);
    const [data, setData] = useState<ChatVO>();
    const [scriptType, setScriptType] = useState<ScriptType>();
    const [showScript, setShowScript] = useState(false);
    const [loading, setLoading] = useState(true);
    const handleShowScript = () => {
        setShowScript(true);
    };

    const onLoaded = () => {
        console.log('onLoaded');
        setLoading(false);
        try {
            hideLoading();
        } catch (error) {}
    };

    useLoad(async () => {
        console.log('startTime', Date.now());

        showLoading({
            title: '加载中',
            mask: true
        });

        try {
            const res = await getChat(chatId);
            const { data } = res.data;
            // data.script.disableTextInputFlag = scriptRes.data.data.disableTextInputFlag;
            setData(data);
            if (data.type === 1) {
                setScriptType(ScriptType.SKILL);
            } else if (data.type === 2) {
                setScriptType(ScriptType.QUESTION);
            } else {
                setScriptType(ScriptType.SKILL);
            }
            getScript(res.data.data.script.id).then((scriptRes) => {
                setData(
                    (data) =>
                        ({
                            ...data,
                            script: {
                                ...data?.script,
                                disableTextInputFlag: scriptRes.data.data.disableTextInputFlag
                            }
                        } as any)
                );
            });
        } catch (error) {
            console.log(error);
        }
    });

    useReady(async () => {
        console.log('useReady');
    });

    return (
        <Page>
            <View
                className={classNames(styles.loading_box, {
                    [styles.loading_hide]: !loading
                })}
            />
            {scriptType === ScriptType.SKILL && (
                <DialogSkill
                    texturl={text}
                    chatId={chatId}
                    interrupt={interrupt}
                    from={from}
                    mode={mode}
                    data={data}
                    introductionType={Number(introductionType)}
                    introductionDelay={Number(introductionDelay)}
                    introduction={introduction}
                    onShowScript={handleShowScript}
                    onLoaded={onLoaded}
                />
            )}
            {scriptType === ScriptType.QUESTION && (
                <DialogQuestion
                    chatId={chatId}
                    interrupt={interrupt}
                    from={from}
                    mode={mode}
                    data={data}
                    onShowScript={handleShowScript}
                    onLoaded={onLoaded}
                />
            )}
            <ScriptActionSheet
                showScript={showScript}
                currentScript={data?.script}
                emanName={data?.eman.name}
                showScriptClose={() => setShowScript(false)}
            />
        </Page>
    );
};

export default App;
