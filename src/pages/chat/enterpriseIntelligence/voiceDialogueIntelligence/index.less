@import '../../dialogue/components/voice.less';
.status_box {
  margin-bottom: 46px;
}
.wave_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;
    .wave_bar {
      width: 8rpx;
      height: 70rpx;
      margin: 0 6px;
      background: linear-gradient(
        180deg,
        rgb(242, 243, 244)  0%,
        rgb(252, 249, 249) 50%,
         rgb(255, 255, 255)100%
      );
      border-radius: 25% !important;
      animation: shortBar 1s infinite;
      transform-origin: center center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      // 3D效果增强
      box-shadow: 
        0 4rpx 12rpx rgba(0,120,200,0.3),
        inset 0 -2rpx 4rpx rgba(255,255,255,0.2);
      
      // GPU加速
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }