// import { useStorage } from '@/common';
import { DateTimeFormatEnum } from '@/constants/dateTime';
// import { StorageKey } from '@/constants/storage';
import type { HistoryVO } from '@/types/chat';
import { RoleEnum } from '@/types/chat';
// import type { UserInfo } from '@/types/common';
import ReactRemarkable from '@/components/react-remarkable';
// import TaroParser from '@/components/taroParser/TaroParser';
// import towxml from '@/components/towxml/index.js';
import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import '@/styles/markdown.global.less';
import { Icon, Image } from '@antmjs/vantui';
import { Block, ScrollView, Text, View } from '@tarojs/components';
import { previewImage, pxTransform, setClipboardData } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useCallback, useState } from 'react';
import Typewriter from '../typewriter';
import styles from './index.less';

export interface ChatHistory {
    // navBarHeight: number;
    oldHistory: HistoryVO[];
    history: HistoryVO[];
    assistantMessage?: string;
    assistantId?: string;
    scriptTime?: string;
    scene?: string;
    eman: {
        avatar: string;
        name: string;
    };
    emanClick?: () => void;
    showNewChatTip?: boolean;
    onLoadHistory: () => void;
}

const Index: React.FC<ChatHistory> = (props) => {
    const {
        scene,
        history,
        assistantMessage,
        assistantId,
        scriptTime,
        eman,
        emanClick,
        showNewChatTip,
        oldHistory,
        onLoadHistory
    } = props;
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const regex = /https?:\/\/(.+\/)+.+(\.(png|jpg|jpeg))/g;
    const userInfo = Storage.get(StorageEnvKey.USERINFO);
    const [timeShowHistory, setTimeShowHistory] = useState<string>();
    const [historyThinkStates, setHistoryThinkStates] = useState<Record<string, boolean>>({});
    const [oldHistoryThinkStates, setOldHistoryThinkStates] = useState<Record<string, boolean>>({});
    const showTimeHistory = (id: string | undefined) => {
        setTimeShowHistory(timeShowHistory === id ? undefined : id);
    };
    const toggleThinkShow = useCallback(
        (type: 'thinking' | 'thinked', id: string, historyType?: string) => {
            console.log('toggleThinkShow', type, id, historyType, historyThinkStates, oldHistoryThinkStates);
            if (historyType === 'history') {
                // 在historyThinkStates中查找是否有对应id的记录，如果有则切换状态，如果没有则创建一个值为false新记录
                setHistoryThinkStates((state) => {
                    const newState = { ...state };
                    if (id in newState) {
                        newState[id as string] = !newState[id as string];
                    } else {
                        newState[id as string] = false;
                    }
                    return newState;
                });
            } else if (historyType === 'oldHistory') {
                setOldHistoryThinkStates((state) => {
                    const newState = { ...state };
                    if (id in newState) {
                        newState[id as string] = !newState[id as string];
                    } else {
                        newState[id as string] = false;
                    }
                    console.log('oldHistory newState', newState);
                    return newState;
                });
            }
        },
        [historyThinkStates, oldHistoryThinkStates]
    );
    const { run: handleScrollToLower } = useThrottleFn(
        (e) => {
            console.log('handleScrollToLower', e);
            onLoadHistory();
        },
        {
            wait: 1000,
            leading: true,
            trailing: false
        }
    );

    const handleImagePreview = (url: string, imgList: string[]) => {
        console.log('handleImagePreview', url, imgList);
        if(url) {
            previewImage({
                current: url,
                urls: imgList
            });
        }
    };

    const handleCopy = (content: string) => {
        if(content.trim()) {
            setClipboardData({
                data: content
            });
        }
    };

    return (
        <Block>
            <ScrollView
                className={classNames(styles.chat, styles.reverse_x)}
                scrollY
                onScrollToLower={handleScrollToLower}
            >
                <View className={styles.chat_box}>
                    {history.length > 0 ? (
                        <View className={classNames(styles.history, styles.reverse_x)}>
                            {history.map((item, index) => {
                                if (item.role === RoleEnum.ASSISTANT) {
                                    return (
                                        <View key={`${item.role}_${item.id}`} className={styles.history_assistant}>
                                            <Image
                                                className={styles.history_user_avatar}
                                                radius={pxTransform(14)}
                                                width={pxTransform(80)}
                                                height={pxTransform(80)}
                                                src={(eman.avatar as string) || AvatarDefault}
                                            />
                                            <View
                                                className={styles.history_assistant_message}
                                                onClick={() => showTimeHistory(item.id)}
                                                onLongPress={() => handleCopy(item.content)}
                                            >
                                                {item.reasoningContent && (
                                                    <Block>
                                                        <View
                                                            className={styles.think_ing}
                                                            onClick={() =>
                                                                toggleThinkShow('thinked', item.id, 'history')
                                                            }
                                                        >
                                                            <View>{item.content ? '已深度思考' : '思考中...'}</View>
                                                            <Icon
                                                                name={
                                                                    item.id in historyThinkStates
                                                                        ? historyThinkStates[item.id]
                                                                            ? 'arrow-down'
                                                                            : 'arrow'
                                                                        : 'arrow-down'
                                                                }
                                                                size='18px'
                                                            />
                                                        </View>
                                                        <View
                                                            className={classNames(styles.think_text, {
                                                                [styles.think_text_hide]:
                                                                    item.id in historyThinkStates
                                                                        ? !historyThinkStates[item.id]
                                                                        : false
                                                            })}
                                                        >
                                                            {item.reasoningContent.replace(/\\n/g, '\n')}
                                                        </View>
                                                    </Block>
                                                )}
                                                {assistantMessage && item.id === assistantId ? (
                                                    <Typewriter
                                                        text={assistantMessage.replace(regex, (match) => {
                                                            return match.replace(/\s/g, '');
                                                        })}
                                                        typingSpeed={50}
                                                    />
                                                ) : (
                                                    <ReactRemarkable
                                                        options={{ html: true }}
                                                        source={item.content.replace(regex, (match) => {
                                                            return match.replace(/\s/g, '');
                                                        })}
                                                        onImgClick={handleImagePreview}
                                                    />
                                                )}

                                                {/* <towxml nodes={towxml(item.content, 'markdown')} /> */}
                                            </View>
                                            <View
                                                className={classNames(styles.history_assistant_time, {
                                                    [styles.history_assistant_time_show]: timeShowHistory === item.id
                                                })}
                                            >
                                                {item.createTime && dayjs(item.createTime).isSame(dayjs(), 'day')
                                                    ? dayjs(item.createTime).format(DateTimeFormatEnum.HM)
                                                    : dayjs(item.createTime).isSame(dayjs(), 'year')
                                                    ? dayjs(item.createTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                                    : dayjs(item.createTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                                            </View>
                                        </View>
                                    );
                                }
                                if (item.role === RoleEnum.USER) {
                                    return (
                                        <View key={`${item.role}_${item.id}`} className={styles.history_user}>
                                            <View
                                                className={styles.history_user_message}
                                                onClick={() => showTimeHistory(item.id)}
                                                onLongPress={() =>handleCopy(item.content)}
                                            >
                                                <ReactRemarkable options={{ html: true }} source={item.content} />
                                            </View>
                                            <Image
                                                className={styles.history_user_avatar}
                                                radius={pxTransform(14)}
                                                width={pxTransform(80)}
                                                height={pxTransform(80)}
                                                src={(item?.avatar as string) || userInfo.avatar || AvatarDefault}
                                            />
                                            <View
                                                className={classNames(styles.history_user_time, {
                                                    [styles.history_user_time_show]: timeShowHistory === item.id
                                                })}
                                            >
                                                {item.createTime && dayjs(item.createTime).isSame(dayjs(), 'day')
                                                    ? dayjs(item.createTime).format(DateTimeFormatEnum.HM)
                                                    : dayjs(item.createTime).isSame(dayjs(), 'year')
                                                    ? dayjs(item.createTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                                    : dayjs(item.createTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                                            </View>
                                        </View>
                                    );
                                }
                            })}
                            {/* {assistantMessage && (
                                <View className={styles.history_assistant}>
                                    <Image
                                        className={styles.history_user_avatar}
                                        radius={pxTransform(14)}
                                        width={pxTransform(80)}
                                        height={pxTransform(80)}
                                        src={(eman.avatar as string) || AvatarDefault}
                                    />
                                    <View
                                        className={classNames(
                                            styles.history_assistant_message,
                                            styles.history_assistant_message_saying
                                        )}
                                    >
                                        <Typewriter text={assistantMessage} typingSpeed={50} />
                                    </View>
                                </View>
                            )} */}
                        </View>
                    ) : null}
                    <View
                        className={classNames(styles.split_line, styles.reverse_x, {
                            [styles.show_tip]: showNewChatTip
                        })}
                    >
                        <Text className={styles.split_line_text}>来聊点新话题吧</Text>
                    </View>
                    {showNewChatTip && (
                        <View className={classNames(styles.chat_time, styles.reverse_x)}>
                            {scriptTime && dayjs(scriptTime).isSame(dayjs(), 'day')
                                ? dayjs(scriptTime).format(DateTimeFormatEnum.HM)
                                : dayjs(scriptTime).isSame(dayjs().subtract(1, 'day'), 'day')
                                ? `昨天 ${dayjs(scriptTime).format(DateTimeFormatEnum.HM)}`
                                : dayjs(scriptTime).isSame(dayjs(), 'year')
                                ? dayjs(scriptTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                : dayjs(scriptTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                        </View>
                    )}
                    {showNewChatTip && scene && (
                        <View className={classNames(styles.chat_scene, styles.reverse_x)}>
                            <View className={styles.chat_scene_text}>{scene}</View>
                        </View>
                    )}
                    {oldHistory.map((item) => {
                        if (item.role === RoleEnum.ASSISTANT) {
                            return (
                                <View
                                    key={`${item.role}_${item.id}`}
                                    className={classNames(styles.history_assistant, styles.reverse_x)}
                                >
                                    <Image
                                        onClick={emanClick}
                                        className={styles.history_assistant_avatar}
                                        radius={pxTransform(14)}
                                        width={pxTransform(80)}
                                        height={pxTransform(80)}
                                        src={item.avatar ? item.avatar : eman.avatar}
                                    />
                                    <View
                                        className={styles.history_assistant_message}
                                        onClick={() => showTimeHistory(item.id)}
                                        onLongPress={() =>handleCopy(item.content)}
                                    >
                                        {/* <TaroParser
                                            type='markdown'
                                            theme='light'
                                            content={item.content}
                                            onImgClick={handleImagePreview}
                                        /> */}
                                        {item.reasoningContent && (
                                            <Block>
                                                <View
                                                    className={styles.think_ing}
                                                    onClick={() => toggleThinkShow('thinked', item.id, 'oldHistory')}
                                                >
                                                    <View>已深度思考</View>
                                                    <Icon
                                                        name={
                                                            item.id in oldHistoryThinkStates
                                                                ? oldHistoryThinkStates[item.id]
                                                                    ? 'arrow-down'
                                                                    : 'arrow'
                                                                : 'arrow-down'
                                                        }
                                                        size='18px'
                                                    />
                                                </View>
                                                <View
                                                    className={classNames(styles.think_text, {
                                                        [styles.think_text_hide]:
                                                            item.id in oldHistoryThinkStates
                                                                ? !oldHistoryThinkStates[item.id]
                                                                : false
                                                    })}
                                                >
                                                    {item.reasoningContent.replace(/\\n/g, '\n')}
                                                </View>
                                            </Block>
                                        )}
                                        <ReactRemarkable
                                            options={{ html: true, xhtmlOut: true }}
                                            source={item.content.replace(regex, (match) => {
                                                return match.replace(/\s/g, '');
                                            })}
                                            onImgClick={handleImagePreview}
                                        />
                                        {/* <towxml
                                            nodes={towxml(`${item.content}哈哈航啊哈`, 'markdown', {
                                                events: {
                                                    tap: (e) => {
                                                        console.log('tap', e);
                                                    }
                                                }
                                            })}
                                        /> */}
                                    </View>
                                    <View
                                        className={classNames(styles.history_assistant_time, {
                                            [styles.history_assistant_time_show]: timeShowHistory === item.id
                                        })}
                                    >
                                        {item.createTime && dayjs(item.createTime).isSame(dayjs(), 'day')
                                            ? dayjs(item.createTime).format(DateTimeFormatEnum.HM)
                                            : dayjs(item.createTime).isSame(dayjs(), 'year')
                                            ? dayjs(item.createTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                            : dayjs(item.createTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                                    </View>
                                </View>
                            );
                        }
                        if (item.role === RoleEnum.USER) {
                            return (
                                <View
                                    key={`${item.role}_${item.id}`}
                                    className={classNames(styles.history_user, styles.reverse_x)}
                                >
                                    <View
                                        className={styles.history_user_message}
                                        onClick={() => showTimeHistory(item.id)}
                                        onLongPress={() =>handleCopy(item.content)}
                                    >
                                        {/* <TaroParser
                                            type='markdown'
                                            theme='dark'
                                            content={item.content}
                                            onImgClick={handleImagePreview}
                                        /> */}
                                        <ReactRemarkable options={{ html: true }} source={item.content} />
                                    </View>
                                    <Image
                                        className={styles.history_user_avatar}
                                        radius={pxTransform(14)}
                                        width={pxTransform(80)}
                                        height={pxTransform(80)}
                                        src={(item?.avatar as string) || userInfo.avatar || AvatarDefault}
                                    />
                                    <View
                                        className={classNames(styles.history_user_time, {
                                            [styles.history_user_time_show]: timeShowHistory === item.id
                                        })}
                                    >
                                        {item.createTime && dayjs(item.createTime).isSame(dayjs(), 'day')
                                            ? dayjs(item.createTime).format(DateTimeFormatEnum.HM)
                                            : dayjs(item.createTime).isSame(dayjs(), 'year')
                                            ? dayjs(item.createTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                            : dayjs(item.createTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                                    </View>
                                </View>
                            );
                        }
                    })}

                    {!showNewChatTip && (
                        <View className={classNames(styles.chat_time, styles.reverse_x)}>
                            {scriptTime && dayjs(scriptTime).isSame(dayjs(), 'day')
                                ? dayjs(scriptTime).format(DateTimeFormatEnum.HM)
                                : dayjs(scriptTime).isSame(dayjs().subtract(1, 'day'), 'day')
                                ? `昨天 ${dayjs(scriptTime).format(DateTimeFormatEnum.HM)}`
                                : dayjs(scriptTime).isSame(dayjs(), 'year')
                                ? dayjs(scriptTime).format(DateTimeFormatEnum.MONTHTIMEMIN)
                                : dayjs(scriptTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                        </View>
                    )}

                    {!showNewChatTip && scene && (
                        <View className={classNames(styles.chat_scene, styles.reverse_x)}>
                            <View className={styles.chat_scene_text}>{scene}</View>
                        </View>
                    )}
                </View>
            </ScrollView>
        </Block>
    );
};

export default Index;
