import ReactRemarkable from '@/components/react-remarkable';
// import towxml from '@/components/towxml/index.js';
import { ruleHtmlImage, ruleMarkdownImage } from '@/utils/regexRules';
import { useEffect, useRef, useState } from 'react';
function Typewriter({ text, typingSpeed }: { text: string; typingSpeed: number }) {
    // console.log('Typewriter-text', text);

    const [displayText, setDisplayText] = useState('');
    const [currentIndex, setCurrentIndex] = useState(0);
    const tempImageRef = useRef<string>('');
    const [tempImage, setTempImage] = useState('');
    const regex = /!\[.+$/;
    const regexHtmlImg = /<img.+$/;
    useEffect(() => {
        // console.log('Typewriter-text', text);
        // 检测文本末尾是图片
        /* const m = text.replace(ruleMarkdownImage, '').match(regex);
        if (m) {
            clearInterval(interval);
            // 有部分图片，则提取存到临时变量中，等待下一次打字
            // const mf = text.match(ruleMarkdownImage);
            // console.log('Typewriter-m', m);
            // console.log('Typewriter-text', mf);

            if (m) {
                // 放入临时变量中
                tempImageRef.current = m[0];
                setTempImage(tempImageRef.current);
                // 去掉末尾图片
                setDisplayText(text.replace(m[0], ''));

                // setCurrentIndex(text.length);
            }
        } else {
            if (tempImageRef.current && regexFull.test(tempImageRef.current)) {
                // 图片接收完整了
                clearInterval(interval);

                setTempImage('');
                tempImageRef.current = '';
                setDisplayText(text);
                setCurrentIndex(text.length);
            } else {
                interval = setInterval(() => {
                    if (currentIndex < text.length) {
                        if (regexFull.test(text)) {
                            setTempImage('');
                            tempImageRef.current = '';
                            clearInterval(interval);
                            setDisplayText(text);
                            setCurrentIndex(text.length);
                        } else {
                            setDisplayText((prevText) => prevText + text[currentIndex]);
                            setCurrentIndex((prevIndex) => prevIndex + 1);
                        }
                    } else {
                        setDisplayText(text);
                        setCurrentIndex(text.length);
                        clearInterval(interval);
                    }
                }, typingSpeed);
            }
        } */
        const interval = setInterval(() => {
            if (currentIndex < text.length) {
                setDisplayText((prevText) => {
                    const m = text.replace(ruleMarkdownImage, '').match(regex);
                    if (m) {
                        // 末尾是图片
                        tempImageRef.current = m[0];
                        setTempImage(tempImageRef.current);
                        return text.replace(m[0], '');
                    } else {
                        if (ruleMarkdownImage.test(text) && tempImageRef.current) {
                            tempImageRef.current = '';
                            setTempImage('');
                            setCurrentIndex(text.length);

                            return text;
                        } else {
                            const mh = text.replace(ruleHtmlImage, '').match(regexHtmlImg);
                            // console.log('mh', text, mh);
                            if (mh) {
                                // 末尾是图片
                                tempImageRef.current = mh[0];
                                setTempImage(tempImageRef.current);
                                return text.replace(mh[0], '');
                            } else {
                                if (ruleHtmlImage.test(text) && tempImageRef.current) {
                                    tempImageRef.current = '';
                                    setTempImage('');
                                    setCurrentIndex(text.length);
                                    return text;
                                } else {
                                    const nextText = prevText + text[currentIndex];
                                    setCurrentIndex((prevIndex) => prevIndex + 1);
                                    return nextText;
                                }
                            }
                        }

                    }


                });
            } else {
                clearInterval(interval);
            }
        }, typingSpeed);

        return () => {
            clearInterval(interval);
        };
    }, [text, typingSpeed, currentIndex]);

    return (
        <ReactRemarkable options={{ html: true }}>
            {displayText}
            {/* {tempImage && '<span class="doting">●</span>'} */}
            {tempImage && '<div style="width:100%;height: 112px;margin: 8px 0;background-color: #ccc;"></div>'}
        </ReactRemarkable>
        // <towxml nodes={towxml(displayText + (tempImage && '<div class="img-block"></div>'), 'markdown')} />
    );
    // return <TaroParser type='markdown' theme='light' content={displayText} />;
}

export default Typewriter;
