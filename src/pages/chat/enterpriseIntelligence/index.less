@import '@/styles/index.less';

page {
    background-color: #fff;
    --form-background-color: transparent;
    --form-label-width: 0;
    --form-controll-margin-left: 0;
}
.page {
    height: 100vh;
    display: flex;
    flex-direction: column;
}
.loading {
    &_box {
        position: fixed;
        left: 0;
        top: 0;
        z-index: 100;
      width: 100%;
      height: 100vh;
      background-color: #fff;
  
    }
    &_hide {
      display: none;
    }
}

:global {
    //.vant-form-formItem {
    //    margin: 0;
    //    padding: 0;
    //}
    //.vant-form-formItem-controll {
    //    margin: 0!important;
    //}
    //.vant-form-formItem-wrapper {
    //    display: inline-block;
    //    vertical-align: middle;
    //}
    .van-cell  {
      border-radius: 32px;
      background: #FAFAFA;
      margin-bottom: 24px;
    }
    .van-popup__close-icon  {
      color: #272C47;
      top: 62px;
      right: 42px;
      width: 26px;
      height: 26px;
    }
    .van-loading__spinner--circular {
        border-width: 6px;
    }
  }

.topBox{
    // position: fixed;
    // top: 0;
    // width: 100%;
    background-color: #fff;
    z-index: 1;
}
.navbar {
    z-index: 0;
}
.noticeBar {
    .flex-center;
    .font(24px,rgb(25, 137, 250),400);

    // position: fixed;
    // top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 70px;
    background: rgb(236, 249, 255);
}
.chat_box {
    // padding-top: 200px; // 微信顶部高度+'内容由ai生成'tips栏
    // margin-top: 70px; // 系统顶部高度
    flex: 1;
    height: 0;
}
.footer{
    height: 0;
}
.bottomContent{
    // position: fixed;
    // bottom: 0;
    // padding-bottom: env(safe-area-inset-bottom);
    // left: 0;
    position: relative;
    width: 100%;
    // background-color: #fff;
}
.deepthink {
    padding: 0 20px;
    height: 56px;
    border-radius: 16px;
    position: absolute;
    left: 20px;
    top: -70px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #F5F6F8;
    color: #1b1b1b;

    &_active {
        background: #DCE0FF;
        color: #4F66FF;
    }

    &_icon {
        width: 36px;
        height: 36px;
    }
}
.clean_box {
    position: absolute;
    right: 52px;
    top: -100px;
    background: #F5F6F8;
    width: 68px;
    height: 68px;
    border-radius: 68px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.clean_icon {
    width: 36px;
    height: 36px;
}
.showScripts{
    width: 204px;
    height: 64px;
    flex-shrink: 0;
    border-radius: 98px;
    border: 1px solid #EDEDED;
    background: #FFF;
    box-shadow: 0 4px 8px 0 #0000000d;
    margin: 0 0 28px 32px;
    .flex-center;
    text{
        margin-left: 10px;
        color: #323233;
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 40px;
    }
}
.sendMessage {
    .wrap-box(32px 32px 32px 32px);

    // padding-bottom: 20px; 
    // padding-bottom: calc(20px + constant(safe-area-inset-bottom)); 
    // padding-bottom: calc(20px + env(safe-area-inset-bottom));
    flex:1;
    background-color: #fff;
    // border: 1px solid #000;
    display: flex;
    justify-content: space-between;
    box-shadow: 0 0 5px 1px rgba(0, 0, 0, 0.05);

    &_ios {
        padding-bottom: 52px;
    }

    &_tel_view{
        height: 108px;
        width: 88px;
       .flex-center;
    }
    &_tel_image{
        width: 44px;
        height: 44px;
        // width: 68px;
        // height: 68px;
        flex: 0 0 auto;
    }
    &_box {
        .wrap-box(10px 20px);
        flex:1;

        margin-left: 10px;
        align-items: center;
        background: #f6f6f6;
        border-radius: 47px;
        padding: 20px;
        // position: relative;
    }

    &_input {
        font-size: 28px;
        color:#272c47;
        font-weight: 400;
        align-items: center;
        width: 465px;
        line-height: 35px; // 安卓上textarea的placeholder的行高固定1.25无法改变
        max-height: 175px; // 展示五行
    }
    &_send {
        display: inline-block;
        width: 68px;
        height: 68px;
        background-color: transparent;
        vertical-align: middle;
        background-repeat: no-repeat;
        background-size: cover;
        background-position: center;
    }
    &_send_image {
        width: 68px;
        height: 68px;
    }
}

.actionSheet {
    &_hide_close {
        :global {
            .van-action-sheet__close {
                display: none !important;
            }
        }
    }
    &_group {
        .wrap-box(12px 62px 54px 62px);
    }
}

.clear_popup{
    &_title {
        font-size: 40px;
        font-weight: bold;
        text-align: center;
        margin-top: 48px;
    }
    &_list {
        margin-top: 56px;
        padding-left: 96px;
        padding-right: 96px;
        margin-bottom: 48px;
        font-size: 28px;
        font-weight: 400;
        color: #9597A0;
        text-align: center;
    }
    &_actions {
        display: flex;
        gap: 24px;
        padding: 0 32px;
        margin-bottom: 24px;
      }
  }

