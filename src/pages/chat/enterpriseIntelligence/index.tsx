import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import { StorageEnvKey } from '@/constants/storage';
import { useChatStream } from '@/hooks';
import { clearContext, getChat, getChatHistoryPage, saveIntroduction } from '@/services/chat';
import ChatHist<PERSON> from './chatHistory';

import type { ChatVO, HistoryVO } from '@/types/chat';
import { RoleEnum } from '@/types/chat';
import { Dialog, Form, FormItem, Icon, Image, NavBar, Popup } from '@antmjs/vantui';

import { Block, Textarea, View } from '@tarojs/components';
import Taro, { getCurrentPages, nextTick, pxTransform, useLoad, useRouter, useUnload } from '@tarojs/taro';
import { useMount } from 'ahooks';

import FeedbackDialog from '@/components/feedbackDialog';
import FeedbackSucess from '@/components/feedbackSucess';
import HarmonyDialog from '@/components/HarmonyDialog';
import config from '@/config';
import { ChatMode, DeepThinkStatus, VoiceInteraction, VoiceStatus } from '@/constants/voicetype';
import { uploadLog } from '@/services/common';
import { eManStatus } from '@/services/eman';
import type { FeedbackData, LogInfo } from '@/types/common';
import { checkHarmony, checkRecordPermission } from '@/utils/permission';
import { ruleMarkdownImage } from '@/utils/regexRules';
import { Button } from '@hygeia/ui';
import { default as classNames, default as classnames } from 'classnames';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef, useState } from 'react';
import '../assets/vant.global.less';
import AbortConfirm from '../components/abortConfirm';
import ActionMore from '../dialogue/components/actionMore';
import ActionVoice from '../dialogue/components/actionVoice';
import styles from './index.less';
import VoiceDialogueIntelligence from './voiceDialogueIntelligence';

const send_0 = `${config.cdnPrefix}dialogue/send_0.png`;
const send_1 = `${config.cdnPrefix}dialogue/send_1.png`;
const TelImg = `${config.cdnPrefix}dialogue/tel.png`;
const TelImgDisabled = `${config.cdnPrefix}dialogue/tel_disabled.png`;
const IconClear = `${config.cdnPrefix}svg/icon_clean_black.svg`;
const IconDeepThinkOff = `${config.cdnPrefix}chat/deep_think_off.svg`;
const IconDeepThinkOn = `${config.cdnPrefix}chat/deep_think_on.svg`;

const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    const { params } = useRouter<{
        chatId: string;
        introductionType: string;
        introductionDelay: string;
        introduction: string;
        from: string;
    }>();
    const { chatId, introduction, from } = params;
    const introductionType = Number(params.introductionType),
        introductionDelay = Number(params.introductionDelay);

    const userInfo = Storage.get(StorageEnvKey.USERINFO);
    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);
    const [data, setData] = useState<ChatVO>();
    const [history, setHistory] = useState<HistoryVO[]>([]);

    const [assistantThinking, setAssistantThinking] = useState<string>();
    const [assistantMessage, setAssistantMessage] = useState<string>();
    const [assistantId, setAssistantId] = useState<string>();
    const [canSend, setCanSend] = useState(false);
    const [keyHeight, setKeyHeight] = useState(0);
    // const [translateHeight, setTranslateHeight] = useState(0);

    const [customTitle, setCustomTitle] = useState('');
    const voicePageRef = useRef<any>();
    const chatData = useRef({ chatReportId: '', done: false }); // 或者直接用chat里的数据，不需要chatData
    const [showVoicePage, setShowVoicePage] = useState(false); // 语音界面显示
    const showVoicePageRef = useRef<boolean>(false);
    const [showNewChatTip, setShowNewChatTip] = useState(false); // 显示新对话提示
    const [oldHistory, setOldHistory] = useState<HistoryVO[]>([]);
    const oldHistoryRef = useRef<HistoryVO[]>([]);
    const page = useRef(1);
    const pageSize = useRef(8);
    const noMore = useRef(false);
    const [loading, setLoading] = useState(true);
    const [isEmanValid, setIsEmanValid] = useState(true);

    const [clearShow, setClearShow] = useState(false);
    const helloTimer = useRef<any>();
    const [showMoreActionSheet, setShowMoreActionSheet] = useState(false);
    const [showVoiceActionSheet, setShowVoiceActionSheet] = useState(false);
    const voiceInteractionRef = useRef<VoiceInteraction>(VoiceInteraction.Manual);
    const [feedbackShow, setFeedbackShow] = useState<boolean>(false);
    const [feedbackSuccessShow, setFeedbackSuccessShow] = useState<boolean>(false);
    const feedbackData = useRef<FeedbackData>({
        client: 'wechat',
        version: '',
        brand: '',
        wxVersion: '',
        SDKVersion: '',
        model: '',
        system: '',
        platform: '',
        environment: '',
        microphoneAuthorized: undefined,
        appId: '',
        name: '',
        phone: '',
        company: '',
        path: '',
        chatId: '',
        description: '',
        logs: []
    });
    const [deepthink, setDeepThink] = useState(DeepThinkStatus.Off);
    const deepthinkRef = useRef(DeepThinkStatus.Off);

    const [showAbortConfirm, setShowAbortConfirm] = useState(false);

    const formIt = Form.useForm();

    const [isIOS, setIsIOS] = useState(false);
    const isHarmonyRef = useRef(false);
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    let bottomContent_height = 0;
    let topBox_height = 0;
    let window_height = 0;

    const onScrollView = () => {
        // Taro.nextTick(() => {
        //     Taro.pageScrollTo({
        //         selector: '#footer'
        //     });
        // });
    };

    // 为实现从历史页进入对话页时候能滚动底部
    useEffect(() => {
        onScrollView();
    }, [history]);

    const assistantThinkingVal = useRef('');
    const assistantMessageVal = useRef('');
    const endTime = useRef(0);
    const assistantIdVal = useRef('');
    const startTimeArr = useRef<number>();
    const thinkTagRegex = /<think>(.*?)<\/think>/gs; // 修改: 添加 's' 标志以匹配多行文本，并确保匹配到空白字符
    const addLog = (log: LogInfo) => {
        const params = {
            ...log,
            time: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
        };
        console.log('log', params);
        feedbackData.current.logs.push(params);
    };

    const handleFeedback = () => {
        setShowMoreActionSheet(false);
        setFeedbackShow(true);
    };
    const submitFeedback = async (values: { description: string }) => {
        feedbackData.current.description = values.description;
        try {
            const res = await uploadLog(JSON.stringify(feedbackData.current));
            if (res.data.data) {
                feedbackData.current.logs = [];
                feedbackData.current.description = '';
                setFeedbackSuccessShow(true);
            }
        } catch (error) {
            Taro.showToast({
                title: '提交失败',
                icon: 'none'
            });
        }
    };

    const typingTimer = useRef<any>(null);
    const firstText = useRef<boolean>(true);

    const resetChat = () => {
        assistantMessageVal.current = '';
        assistantThinkingVal.current = '';
        startTimeArr.current = undefined;
        endTime.current = 0;
        setAssistantMessage(undefined);
        setAssistantThinking(undefined);
        assistantIdVal.current = '';
        setAssistantId('');
        setCustomTitle(data?.eman?.name ?? '');
    };

    const {
        run: chatStreamRun,
        loading: chatStreamLoading,
        abortRequest
    } = useChatStream({
        onProcess: (text) => {
            console.log('onProcess-text', text, assistantThinkingVal.current.length);
            if (firstText.current) {
                if (showVoicePageRef.current) {
                    voicePageRef.current.speakStart();
                }
            }
            firstText.current = false;
            let match;
            let matchContentTotal = '';
            while ((match = thinkTagRegex.exec(text)) !== null) {
                console.log('match', match);
                const matchContent = match[1];
                matchContentTotal += matchContent;
                assistantThinkingVal.current += matchContent;
                console.info('assistantIdVal', assistantIdVal.current);
                setAssistantThinking((message) => {
                    const content = message ?? '';
                    const t = content + matchContent;

                    if (!assistantIdVal.current) {
                        assistantIdVal.current = new Date().getTime().toString();
                        setAssistantId(assistantIdVal.current);

                        setHistory((his) => {
                            console.info('history0id', his);
                            return [
                                ...his,
                                {
                                    id: assistantIdVal.current,
                                    role: RoleEnum.ASSISTANT,
                                    reasoningContent: t,
                                    content: '',
                                    createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                                }
                            ];
                        });
                    } else {
                        setHistory((his) => {
                            console.info('history id', his);
                            his.forEach((item) => {
                                if (item.id === assistantIdVal.current) {
                                    item.reasoningContent = t;
                                }
                            });
                            return his;
                        });
                    }
                    return t;
                });
            }
            if (showVoicePageRef.current) {
                voicePageRef.current?.speakProcess(matchContentTotal); // 传给语音合成
            }
            // 提取非 <think> 标签内容
            const nonThinkContent = text.replace(thinkTagRegex, '');
            console.log('nonThinkContent', nonThinkContent);
            if (nonThinkContent.trim()) {
                if (showVoicePageRef.current) {
                    voicePageRef.current?.speakProcess(nonThinkContent); // 传给语音合成
                }
                assistantMessageVal.current += nonThinkContent;
                if (!startTimeArr.current) {
                    startTimeArr.current = new Date().getTime();
                }
                setAssistantMessage((message) => {
                    const content = message ?? '';
                    const t = content + nonThinkContent;
                    console.log('assistantIdVal', assistantIdVal.current);
                    if (!assistantIdVal.current) {
                        assistantIdVal.current = new Date().getTime().toString();
                        setAssistantId(assistantIdVal.current);
                        setHistory((history) => [
                            ...history,
                            {
                                id: assistantIdVal.current,
                                role: RoleEnum.ASSISTANT,
                                content: t as string,
                                createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                            }
                        ]);
                    } else {
                        // 更新history中的content
                        setHistory((history) => {
                            history.forEach((item) => {
                                if (item.id === assistantIdVal.current) {
                                    item.content = t;
                                }
                            });
                            return history;
                        });
                    }
                    return t;
                });
            }

            Taro.nextTick(() => {
                onScrollView();
            });
        },
        onEnd: () => {
            addLog({
                level: 'debug',
                message: 'useChatStream onEnd',
                data: {
                    showVoicePage: showVoicePageRef.current
                }
            });
            firstText.current = true;
            if (showVoicePageRef.current) {
                voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
            }
            // time = 50ms*总字长 - 第一次onProcess时间点到onEnd时间点的时间差
            endTime.current = new Date().getTime();
            const timeUsed = endTime.current - startTimeArr.current;
            // console.log('timeUsed', timeUsed);
            let timeNeed = assistantMessageVal.current.split('').length * 50;
            // 正则查找assistantMessageVal中markdown图片地址

            const match = assistantMessageVal.current.match(ruleMarkdownImage);
            console.log('match', match);
            if (match) {
                // 计算图片地址累积的长度
                const lentotal = match.reduce((acc, cur) => {
                    return acc + cur.length;
                }, 0);
                console.log('lentotal', lentotal);
                timeNeed = timeNeed - lentotal * 50;
            }
            console.log('timeNeed', timeNeed);
            const interval = setInterval(() => {
                onScrollView();
            }, 50 * 10); // 50ms*10个字一行
            // todo useInterval useTimeout
            console.log('timeNeed - timeUsed', timeNeed, timeUsed, timeNeed - timeUsed);
            typingTimer.current = setTimeout(() => {
                console.log('assistantMessageVal', assistantMessageVal.current);
                clearInterval(interval);

                nextTick(() => {
                    resetChat();
                });
            }, timeNeed - timeUsed + 1000);
        },
        onAbort: () => {
            nextTick(() => {
                firstText.current = true;
                // 继续聆听
                if (showVoicePageRef.current) {
                    voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                    // voicePageRef.current?.startListen();
                }
                resetChat();
                addLog({
                    level: 'warn',
                    message: 'chatStreamRun abort'
                });
            });
        },
        onSuccess: () => {},
        onError: (error) => {
            nextTick(() => {
                firstText.current = true;
                if (error.errMsg) {
                    if (!error.errMsg.includes('abort')) {
                        if (error.errMsg.includes('timeout')) {
                            Taro.showToast({
                                title: '当前访问人数过多，响应有点慢',
                                icon: 'none'
                            });
                        } else if (error.errMsg.includes('limit')) {
                            Taro.showToast({
                                title: '当前访问人数过多，请稍后重试',
                                icon: 'none'
                            });
                        } else if (error.errMsg.includes('401')) {
                            Taro.showToast({
                                title: '登录已失效，请重新登录',
                                icon: 'none'
                            });
                        }
                        // 继续聆听
                        if (showVoicePageRef.current) {
                            voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                            voicePageRef.current?.startListen();
                        }
                    }
                } else {
                    Taro.showToast({
                        title: '对话错误，请重试',
                        icon: 'none'
                    });
                    // 继续聆听
                    if (showVoicePageRef.current) {
                        voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                        voicePageRef.current?.startListen();
                    }
                }

                resetChat();
                addLog({
                    level: 'error',
                    message: 'chatStreamRun error',
                    data: {
                        error
                    }
                });
            });
        }
    });
    const stopChatRequest = () => {
        abortRequest();
    };
    // 取消开场白
    const cancelHello = () => {
        addLog({
            level: 'trace',
            message: 'cancelHello'
        });
        if (helloTimer.current) {
            clearTimeout(helloTimer.current);
            helloTimer.current = undefined;
        }
    };
    const handleSend = async (text: string) => {
        // setShowNewChatTip(false);
        addLog({
            level: 'debug',
            message: 'handleSend',
            data: {
                text
            }
        });
        setHistory((history) => [
            ...history,
            {
                id: Date.now().toString(),
                role: RoleEnum.USER,
                content: text,
                avatar: userInfo?.avatar,
                createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
            }
        ]);

        formIt.resetFields();
        setCanSend(false);
        // console.log('send', message, new Date().getTime());
        setCustomTitle('对方正在输入...');
        const params: any = {
            chatId,
            message: text,
            thinkFlag: deepthinkRef.current === DeepThinkStatus.On,
            timeout: deepthinkRef.current === DeepThinkStatus.On ? 5 * 60 * 1000 : 60000
        };
        if (isWework === '1') {
            params.responseId = chatId + Date.now();
        }
        chatStreamRun(params);
        onScrollView();
    };

    const onSend = () => {
        const message = formIt.getFieldValue('message');

        addLog({
            level: 'debug',
            message: 'onSend',
            data: {
                message
            }
        });
        if (!message.trim()) {
            // Toast_.show未出现
            // console.log('请输入内容');
            // Toast_.show({
            //     message: '请输入内容'
            // });
            return;
        }
        cancelHello();
        handleSend(message);
    };

    // const chatEnd = async () => {
    //     const res = await doneChat(chatId, false);
    //     if (res.data.code === 200) {
    //         chatData.current.done = true;
    //         Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
    //         Taro.switchTab({ url: '/pages/home/<USER>' });
    //     }
    // };
    const showMore = () => {
        setShowMoreActionSheet(true);
    };
    const showActionSheetVoiceSetting = () => {
        setShowMoreActionSheet(false);
        setShowVoiceActionSheet(true);
    };

    const voicePopupConfirm = () => {
        Dialog_.alert({
            title: '修改成功',
            message: '下次练习时生效',
            confirmButtonText: '关闭',
            confirmButtonColor: '#4F66FF'
        }).then(() => {});
    };
    function goBack() {
        Taro.navigateBack();
    }

    // 显示语音界面
    function handleVoicePage() {
        setShowVoicePage(true);
        showVoicePageRef.current = true;

        addLog({
            level: 'trace',
            message: 'handleVoicePage'
        });
    }

    function startListen() {
        voicePageRef.current?.startListen();
        addLog({
            level: 'trace',
            message: 'startListen'
        });
    }

    const switchVoice = async () => {
        if (data?.eman.zipFileUrl && data?.eman.show3dFlag) {
            if (
                isHarmonyRef.current ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null ||
                Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN
            ) {
                Taro.redirectTo({
                    url: `/pages/shuziren/index?chatId=${data?.id}&from=intelligence`
                });
            } else {
                abortRequest();
                try {
                    await checkRecordPermission(Dialog_);
                    handleVoicePage();
                    startListen();
                } catch (error) {
                    console.log('没有授权录音', error);
                }
            }
        } else {
            if (isHarmonyRef.current) {
                setShowHarmonyDialog(true);
            } else {
                abortRequest();
                try {
                    await checkRecordPermission(Dialog_);
                    handleVoicePage();
                    startListen();
                } catch (error) {
                    console.log('没有授权录音', error);
                }
            }
        }
    };

    const onVoiceClick = useCallback(async () => {
        // if (!chatData.current.done && !(minutes <= 0 && seconds <= 0)) {

        // }
        addLog({
            level: 'trace',
            message: 'onVoiceClick'
        });
        const chatMode = Storage.get(StorageEnvKey.CHAT_MODE);
        console.log(data?.eman.zipFileUrl, data?.eman.show3dFlag, chatMode);
        if (isEmanValid) {
            if (chatStreamLoading || assistantMessage) {
                setShowAbortConfirm(true);
            } else {
                switchVoice();
            }
        }
    }, [isEmanValid, data, chatStreamLoading, assistantMessage]);

    const onAbortConfirmClose = () => {
        setShowAbortConfirm(false);
    };

    const onAbortConfirm = () => {
        setShowAbortConfirm(false);
        switchVoice();
    };

    // 语音一句话回调
    const handleVoiceSentence = (sentence: string) => {
        handleSend(sentence);
    };
    const requestHistory = async () => {
        if (!noMore.current) {
            try {
                const param: any = {
                    pageNo: page.current,
                    pageSize: pageSize.current
                };

                if (oldHistoryRef.current.length > 0) {
                    param.lastId = oldHistoryRef.current[oldHistoryRef.current.length - 1].id;
                }

                const { data: historyRes } = await getChatHistoryPage(chatId, param);
                setOldHistory(() => {
                    oldHistoryRef.current.push(...historyRes.data.records);
                    const all = JSON.parse(JSON.stringify(oldHistoryRef.current));
                    // 按createTime排序
                    if (all.length === Number(historyRes.data.total)) {
                        noMore.current = true;
                    }
                    return all;
                });
                // if (historyRes.data.records.length > 0) {
                //     page.current += 1;
                // }
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'requestHistory',
                    data: {
                        error
                    }
                });
            }
        }
    };

    const handleClear = async () => {
        setShowMoreActionSheet(false);
        setClearShow(true);
    };

    const clearContextSend = async () => {
        addLog({
            level: 'trace',
            message: 'clearContextSend'
        });
        setClearShow(false);

        try {
            const res = await clearContext(chatId);
            if (res.data.data) {
                // setOldHistory(() => {
                //     console.log(history, 'history');
                //     oldHistoryRef.current.push(...history);
                //     const all = JSON.parse(JSON.stringify(oldHistoryRef.current));
                //     return all.reverse();
                // });
                if (data) {
                    data.createTime = dayjs().format('YYYY-MM-DD HH:mm:ss');
                    setData(data);
                }

                Taro.showToast({
                    title: '已清除上下文',
                    icon: 'none',
                    duration: 1000
                });
                oldHistoryRef.current = [];
                setOldHistory([]);

                setHistory([]);
                setShowNewChatTip(true);
                page.current = 1;
                noMore.current = false;
                requestHistory();
                onScrollView();
            }
        } catch (error) {
            addLog({
                level: 'error',
                message: 'clearContextSend',
                data: {
                    error
                }
            });
        }
    };
    const handleHideLoading = () => {
        Taro.hideLoading();
        setLoading(false);
    };
    const switchDeepthink = () => {
        const thinkStatus = deepthinkRef.current === DeepThinkStatus.Off ? DeepThinkStatus.On : DeepThinkStatus.Off;
        addLog({
            level: 'trace',
            message: 'switchDeepthink',
            data: {
                thinkStatus
            }
        });
        if (thinkStatus === DeepThinkStatus.On) {
            Taro.showToast({
                title: '已开启深度思考',
                icon: 'none'
            });
        }
        Storage.set(StorageEnvKey.DEEP_THINK, thinkStatus);
        setDeepThink(thinkStatus);
        deepthinkRef.current = thinkStatus;
    };
    // usePullDownRefresh(() => {
    //     requestHistory();
    //     Taro.stopPullDownRefresh();
    // });
    useLoad(() => {
        console.log('useLoad');
        try {
            const pages = getCurrentPages();
            feedbackData.current.path = pages[pages.length - 1].$taroPath || pages[pages.length - 1].route;

            const { appId, version } = Taro.getAccountInfoSync().miniProgram;
            feedbackData.current.appId = appId;
            feedbackData.current.version = version;

            Taro.getSystemInfo({
                success(res: any) {
                    console.log('getSystemInfo', res);
                    feedbackData.current.brand = res.brand;
                    feedbackData.current.model = res.model;
                    feedbackData.current.platform = res.platform;
                    feedbackData.current.system = res.system;
                    feedbackData.current.wxVersion = res.version;
                    feedbackData.current.SDKVersion = res.SDKVersion;
                    feedbackData.current.environment = res.environment;
                    feedbackData.current.microphoneAuthorized = res.microphoneAuthorized;
                    feedbackData.current.name = userInfo.name;
                    feedbackData.current.company = userInfo.companyName;
                    feedbackData.current.phone = userInfo.phoneNumber;
                }
            });
        } catch (error) {
            // addLog({
            //     level: 'error',
            //     message: 'getAccountInfoSync',
            //     data: {
            //         error
            //     }
            // });
        }
        const deepthinkStorage = Storage.get(StorageEnvKey.DEEP_THINK);
        deepthinkRef.current = deepthinkStorage === null ? DeepThinkStatus.Off : deepthinkStorage;
        setDeepThink(deepthinkRef.current);
        Storage.set(StorageEnvKey.REFRESH_HOME, 1);
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
    });

    useUnload(() => {
        abortRequest();
        feedbackData.current.description = '自动上传日志';
        Taro.eventCenter.trigger('uploadLogs', JSON.stringify(feedbackData.current));
    });

    const saveIntro = async (chatId: string) => {
        addLog({
            level: 'debug',
            message: 'saveIntro',
            data: {
                introduce: introduction
            }
        });
        try {
            voicePageRef.current.changeStatus(VoiceStatus.Waiting);
            const saveIntroRes = await saveIntroduction(chatId);
            if (saveIntroRes.data) {
                if (showVoicePageRef.current) {
                    // 语音状态，播放并保存到对话历史

                    voicePageRef.current.speakStart();
                    voicePageRef.current?.speakProcess(introduction); // 传给语音合成
                    voicePageRef.current?.speakEnd(); // 告诉语音界面ai输出结束
                    assistantMessageVal.current = '';
                    startTimeArr.current = undefined;
                    endTime.current = 0;
                    setHistory((history) => [...history, { role: RoleEnum.ASSISTANT, content: introduction }]);
                    assistantIdVal.current = '';
                    setAssistantId('');
                } else {
                    // 非语音状态
                    assistantMessageVal.current = introduction;

                    startTimeArr.current.push(new Date().getTime());

                    setAssistantMessage(introduction);

                    assistantIdVal.current = new Date().getTime().toString();
                    setAssistantId(assistantIdVal.current);
                    setHistory((history) => [
                        ...history,
                        {
                            id: assistantIdVal.current,
                            role: RoleEnum.ASSISTANT,
                            content: introduction,
                            createTime: dayjs().format('YYYY-MM-DD HH:mm:ss')
                        }
                    ]);

                    endTime.current = new Date().getTime();
                    const timeUsed = endTime.current - startTimeArr.current;
                    // console.log('timeUsed', timeUsed);
                    const timeNeed = assistantMessageVal.current.split('').length * 50;
                    // console.log('timeNeed', timeNeed);

                    // todo useInterval useTimeout
                    setTimeout(() => {
                        // console.log('assistantMessageVal', assistantMessageVal.current);
                        assistantMessageVal.current = '';
                        startTimeArr.current = undefined;
                        endTime.current = 0;
                        setAssistantMessage(undefined);
                        setHistory((history) => [
                            ...history,
                            { role: RoleEnum.ASSISTANT, content: assistantMessage as string }
                        ]);
                    }, timeNeed - timeUsed + 1000);
                }
            } else {
                startListen();
            }
        } catch (error) {
            startListen();
        }
    };

    useMount(async () => {
        console.log('mount');
        const sysinfo = Taro.getSystemInfoSync();
        console.log('-sysinfo-', sysinfo);
        setIsIOS(sysinfo.platform === 'ios' || sysinfo.platform === 'iOS');
        window_height = sysinfo.windowHeight;
        setTimeout(() => {
            const query1 = Taro.createSelectorQuery();
            query1
                .select('#bottomContent')
                .boundingClientRect()
                .exec((rect) => {
                    bottomContent_height = rect[0].height;
                });
            const query2 = Taro.createSelectorQuery();
            query2
                .select('#topBox')
                .boundingClientRect()
                .exec((rect) => {
                    topBox_height = rect[0].height;
                });
        }, 1000);
        const voice_interaction_type =
            Storage.get(StorageEnvKey.INTELLIGENCE_INTERACTION_TYPE) || VoiceInteraction.Manual;
        voiceInteractionRef.current = voice_interaction_type;
        try {
            const res = await getChat(chatId);
            const { data } = res.data;
            setData(data);
            chatData.current = data;
            setCustomTitle(data.eman.name);
            requestHistory();
            const resStatus = await eManStatus(data.eman.id);
            setIsEmanValid(resStatus.data.data);

            if (resStatus.data.data) {
                const isHarmony = await checkHarmony();
                console.log('isHarmony', isHarmony);
                isHarmonyRef.current = isHarmony;
                if (from === 'history' || from === 'shuziren') {
                    // 如果从历史列表或者数字人进入，则只显示文字界面
                    handleHideLoading();
                } else {
                    if (isHarmony) {
                        handleHideLoading();
                    } else {
                        handleVoicePage();
                    }

                    addLog({
                        level: 'debug',
                        message: '初始化开场白',
                        data: {
                            introductionType,
                            introductionDelay,
                            introduction
                        }
                    });
                    if (introduction) {
                        // 历史记录是空的
                        if (introductionType === 0) {
                            // 没有开场白
                            // addLog({
                            //     level: 'debug',
                            //     message: 'initIntro 没有开场白',
                            //     data: {
                            //         introductionType,
                            //         introductionDelay,
                            //         introduction
                            //     }
                            // });
                            startListen();
                        } else if (introductionType === 1) {
                            // 立即开场白，保存开场白，并语音播放
                            // addLog({
                            //     level: 'debug',
                            //     message: 'initIntro 立即开场白',
                            //     data: {
                            //         introductionType,
                            //         introductionDelay,
                            //         introduction
                            //     }
                            // });
                            saveIntro(data.id);
                        } else if (introductionType === 2) {
                            // 延迟开场白，增加倒计时，倒计时结束，保存开场白，语音播放；
                            // 手动模式语音，点击取消倒计时；
                            // 自动模式语音，识别到话，取消倒计时，没识别到话，停止聆听，保存开场白，语音播放；
                            // addLog({
                            //     level: 'debug',
                            //     message: 'initIntro 延迟开场白',
                            //     data: {
                            //         introductionType,
                            //         introductionDelay,
                            //         introduction
                            //     }
                            // });
                            startListen();
                            helloTimer.current = setTimeout(async () => {
                                addLog({
                                    level: 'trace',
                                    message: '延迟开场白结束'
                                });
                                if (voiceInteractionRef.current === VoiceInteraction.Auto) {
                                    voicePageRef.current.cancelListen();
                                }
                                saveIntro(data.id);
                                // saveIntroTest();
                            }, introductionDelay * 1000);
                        }
                    } else {
                        // addLog({
                        //     level: 'trace',
                        //     message: '没开场白'
                        // });
                        startListen();
                    }
                }
            } else {
                handleHideLoading();
            }
        } catch (error) {
            console.log('getChat error', error);
        }
    });

    useEffect(() => {
        Taro.onKeyboardHeightChange((res) => {
            setKeyHeight(res.height); //  - 2 安卓机型出现了蓝线
            if (res.height) {
                const query = Taro.createSelectorQuery();
                query
                    .select('#chat')
                    .boundingClientRect()
                    .exec((rect) => {
                        const chat_height = rect[0].height;
                        const t1 = window_height - topBox_height - res.height - bottomContent_height;
                        const t2 = chat_height - 100;
                        console.log('window', window_height);
                        console.log('topBox', topBox_height);
                        console.log('keyboard', res.height);
                        console.log('bottomContent', bottomContent_height);
                        console.log('chat_height', chat_height);
                        console.log('t1', t1);
                        console.log('t2', t2);
                        // 总高度 - 头部 - 键盘 - 发送框 = 内容区域
                        if (t1 < t2) {
                            // 计算出应该上移多少距离
                            // 最多res.height 最少0 中间变量值为t2 - t1
                            // setTranslateHeight(t2 - t1 < res.height ? t2 - t1 : res.height);
                        } else {
                            // setTranslateHeight(0);
                        }
                    });
            }
        });
    }, []);

    return (
        <Page className={styles.page}>
            <View
                className={classNames(styles.loading_box, {
                    [styles.loading_hide]: !loading
                })}
            />
            <View id='topBox' className={styles.topBox}>
                {/* style='padding-top: 88px;' */}
                <NavBar
                    title={customTitle}
                    safeAreaInsetTop
                    zIndex={1}
                    renderLeft={
                        <View className={styles.countDownBox}>
                            <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                        </View>
                    }
                />
                {/* fixed */}
                <View className={styles.noticeBar}>对话为AI情景模拟，非真实场景</View>
                {/* style={`top:${pxTransform(navBarHeight)}`} */}
            </View>
            <View id='chat' className={styles.chat_box} style={{ transform: `translateY(-${keyHeight}px)` }}>
                <ChatHistory
                    scene={data?.scene.scene as string}
                    scriptTime={data?.createTime as string}
                    history={history}
                    assistantMessage={assistantMessage}
                    assistantId={assistantId}
                    eman={{
                        name: data?.eman.name as string,
                        avatar: data?.eman.avatar as string
                    }}
                    showNewChatTip={showNewChatTip}
                    oldHistory={oldHistory}
                    onLoadHistory={requestHistory}
                />
            </View>
            {/* <View
                id='footer'
                className={styles.footer}
                style={{
                    paddingBottom: history.length > 0 || oldHistory.length > 0 ? pxTransform(96 + 156) : 0,
                    marginBottom: keyHeight
                }}
            /> */}
            {/* paddingBottom: history.length > 0 ? pxTransform(156) : 0, */}
            <View id='bottomContent' className={styles.bottomContent} style={{ bottom: keyHeight }}>
                {!chatStreamLoading && assistantMessage === undefined && (
                    <Block>
                        <View
                            className={classNames(styles.deepthink, {
                                [styles.deepthink_active]: deepthink === DeepThinkStatus.On
                            })}
                            onClick={switchDeepthink}
                        >
                            <Image
                                src={deepthink === DeepThinkStatus.On ? IconDeepThinkOn : IconDeepThinkOff}
                                className={styles.deepthink_icon}
                            />
                            <View>深度思考 (R1)</View>
                        </View>
                        <View className={styles.clean_box}>
                            <Image src={IconClear} className={styles.clean_icon} onClick={handleClear} />
                        </View>
                    </Block>
                )}
                <View
                    className={classNames(styles.sendMessage, {
                        [styles.sendMessage_ios]: isIOS
                    })}
                >
                    <View className={styles.sendMessage_tel_view} onClick={onVoiceClick}>
                        <Image className={styles.sendMessage_tel_image} src={isEmanValid ? TelImg : TelImgDisabled} />
                    </View>

                    <Form form={formIt} className={classnames(styles.sendMessage_box, 'send_message_box')}>
                        <FormItem name='message' label=''>
                            <Textarea
                                autoHeight
                                className={styles.sendMessage_input}
                                placeholder={isEmanValid ? '请输入消息' : '该E人已下架'}
                                placeholderStyle={`font-size: ${pxTransform(28)};line-height: 1}`}
                                maxlength={1000}
                                // value={message}
                                disabled={isEmanValid ? chatStreamLoading || !!assistantMessage || showVoicePage : true}
                                confirmType='send'
                                adjustPosition={false}
                                controlled
                                show-confirm-bar={false}
                                onConfirm={(e: any) => {
                                    console.log('confirm', e);
                                    // setMessage(e.detail.value);
                                    onSend();
                                }}
                                onInput={(e: any) => {
                                    formIt.setFieldsValue('message', e.detail.value);
                                    setCanSend(!(e.detail.value.trim() === ''));
                                }}
                            />
                        </FormItem>
                        <Button
                            onClick={onSend}
                            className={styles.sendMessage_send}
                            style={{
                                backgroundImage: `url(${isEmanValid ? (canSend ? send_1 : send_0) : send_0})`
                            }}
                        />
                    </Form>
                </View>
            </View>

            <Popup
                position='bottom'
                show={clearShow}
                closeable={false}
                onClose={() => setClearShow(false)}
                safeAreaInsetBottom
                round
                zIndex={110}
            >
                <View className={styles.clear_popup_title}>确认清除上下文吗？</View>
                <View className={styles.clear_popup_list}>清除后，AI的回复将不受上下文影响，不会清除历史聊天记录</View>
                <View className={styles.clear_popup_actions}>
                    <Button
                        onClick={() => setClearShow(false)}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                    <Button
                        onClick={clearContextSend}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                </View>
            </Popup>
            <AbortConfirm show={showAbortConfirm} onClose={onAbortConfirmClose} onConfirm={onAbortConfirm} />
            <VoiceDialogueIntelligence
                show={showVoicePage}
                ref={voicePageRef}
                chat={data}
                onClose={() => {
                    setShowVoicePage(false);
                    showVoicePageRef.current = false;
                    onScrollView();
                }}
                deepthink={deepthink}
                toggleDeepthink={switchDeepthink}
                showMore={showMore}
                onSentence={handleVoiceSentence}
                onFinish={goBack}
                onLoaded={handleHideLoading}
                cancelHello={cancelHello}
                abortRequest={stopChatRequest}
                addLog={addLog}
            />
            <ActionVoice
                show={showVoiceActionSheet}
                type='INTELLIGENCE_INTERACTION_TYPE'
                onClose={() => setShowVoiceActionSheet(false)}
                onConfirm={voicePopupConfirm}
            />
            <ActionMore
                show={showMoreActionSheet}
                showCleanContext
                onClose={() => setShowMoreActionSheet(false)}
                onSetting={showActionSheetVoiceSetting}
                onCleanContext={handleClear}
                onFeedback={handleFeedback}
            />
            <FeedbackDialog
                show={feedbackShow}
                keyHeight={keyHeight}
                onClose={() => setFeedbackShow(false)}
                onConfirm={submitFeedback}
            />
            <FeedbackSucess show={feedbackSuccessShow} onClose={() => setFeedbackSuccessShow(false)} />
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='A' />
            <Dialog_ />
        </Page>
    );
};

export default App;
