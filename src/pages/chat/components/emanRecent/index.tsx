
import Star from '@/components/star';
import emanJob from '@/utils/emanJob';
import { Button } from '@hygeia/ui';
import { Image, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import { useMemo, type CSSProperties } from 'react';
import styles from './index.less';
import config from "@/config";
export interface EmanCardProps {
    name: string;
    avatar: string;
    background: string;
    department: string;
    /** 职称 */
    title: string;
    skill: string;
    status: number;
    occupation: string;
    type: number;
    shuziren: boolean;

    /** 情景演练 */
    onOk: () => void;
    onEmanClick: () => void;
    style?: CSSProperties;
    isEnterprise: number;
    isQnq: boolean;
    emanDifficult: number;
}
const mark_3d = `${config.cdnPrefix}mark_3d.svg`;
const Index: React.FC<EmanCardProps> = (props) => {
    const {
        avatar,
        name,
        department,
        title,
        skill,
        onOk,
        occupation,
        onEmanClick,
        status,
        type,
        shuziren,
        isEnterprise,
        isQnq,
        emanDifficult
    } = props;

    const { run: handleEmanClick } = useThrottleFn(onEmanClick, { wait: 1500, leading: true, trailing: false });

    const { run: handleOk } = useThrottleFn(onOk, { wait: 1500, leading: true, trailing: false });

    const ocp = useMemo(() => {
        return emanJob(occupation, title, department);
    }, [occupation, title, department]);

    return (
        <View className={styles.card} style={props.style}>
            <View className={styles.info}>
                <View className={styles.avatar_box}>
                    <Image className={styles.avatar_img} onClick={handleEmanClick} src={avatar} />

                    {shuziren && <Image src={mark_3d} className={styles.mark_3d} />}
                </View>
                <View onClick={handleEmanClick} className={styles.des}>
                    <View className={styles.info_name}>{name}</View>
                    <View className={styles.info_occupation}>{ocp}</View>
                </View>

                <View style={{ marginLeft: 'auto' }}>
                    <Button
                        className={styles.action_btn}
                        style={{
                            '--padding-md': pxTransform(22),
                            '--button-normal-height': pxTransform(58)
                        }}
                        round
                        color={
                            status === 1
                                ? '#CCCED7'
                                : type === 2
                                ? isEnterprise === 1
                                    ? 'linear-gradient(270deg, #FE9041 0%, #FFBD3C 100%)'
                                    : 'linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                                : 'linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                        }
                        onClick={handleOk}
                    >
                        {status === 3 ? (type === 2 ? (isEnterprise === 1 ? '咨询' : '对话') : '情景演练') : '已下架'}
                    </Button>
                    {/* <View className={styles.hot}>热度：2389</View> */}
                </View>
            </View>
            <View className={styles.skill}>擅长方向：{skill}</View>
            {isQnq && type === 1 && emanDifficult !== 0 && (
                <View className={styles.eman_difficult}>
                    <View>拜访难度：</View>
                    <Star value={emanDifficult} isGray={true} />
                </View>
            )}
        </View>
    );
};

export default Index;
