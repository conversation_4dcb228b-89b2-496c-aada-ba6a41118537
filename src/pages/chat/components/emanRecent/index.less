@import '@/styles/index.less';

.card {
  background-color: #fff;
  border-radius: 32px;
  margin-bottom: 24px;
  .wrap-box(32px 30px);

  box-sizing: border-box;
}
.avatar_box {
  position: relative;
}
.avatar_img {
  width: 118px;
  height: 118px;
  display: block;
  border-radius: 32px;
}
.mark_3d {
  width: 32px;
  height: 32px;
  position: absolute;
  right:10px;
  bottom: 10px;
  display: block;
  border-radius: 32px;
}
.info {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  &_name {
      .font(32px, #272c47,600);

 
  }
  &_occupation {
      .flex-align-items-center;
      .font(24px,#61626a);

    font-weight: 400;
      margin-top: 10px;
  }
}
.des {
  flex:1;
  padding-left: 20px;
}
.hot {
  color: #9597a0;
  font-size: 24px;
  font-weight: 400;
  text-align: center;
  margin-top: 8px;
}
.skill {
  color: #9597a0;
  font-size: 28px;
  .multi-ellipsis(2);

  margin-top: 24px;
}
.action_btn {
  width: 160px;
}
.eman_difficult {
  .font(24px, #9597A0,400);
  display: flex;
  margin-top: 11px;
}