import { Button, Popup } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import styles from './index.less';
type Props = {
    show: boolean;
    onClose: () => void;
    onConfirm: () => void;
};

const Index: React.FC<Props> = (props) => {
    const { show, onClose, onConfirm } = props;

    return (
        <Popup
            position='bottom'
            show={show}
            closeable={false}
            onClose={() => onClose}
            safeAreaInsetBottom
            round
            zIndex={110}
        >
            <View className={styles.popup_title}>确认切换页面吗？</View>
            <View className={styles.popup_list}>切换页面将中断当前AI回复</View>
            <View className={styles.popup_actions}>
                <Button
                    onClick={() => onClose()}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96),
                        color: '#777777'
                    }}
                    round
                    block
                    color='#F6F6F6'
                >
                    取消
                </Button>
                <Button
                    onClick={onConfirm}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    确定
                </Button>
            </View>
        </Popup>
    );
};

export default Index;
