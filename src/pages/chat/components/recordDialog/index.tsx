import { Popup } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import styles from './index.less';
type Props = {
    show: boolean;
    onClose: () => void;
    onConfirm: () => void;
};

const Index: React.FC<Props> = (props) => {
    const { show, onClose, onConfirm } = props;

    return (
        <Popup position='bottom' show={show} closeable onClose={onClose} safeAreaInsetBottom round zIndex={110}>
            <View className={styles.popup_title}>要重新录制吗？</View>
            <View className={styles.popup_tip}>本次所有录音将作废</View>
            <View className={styles.popup_list} />
            <View className={styles.popup_actions}>
                <Button
                    onClick={onClose}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96),
                        color: '#777777'
                    }}
                    round
                    block
                    color='#F6F6F6'
                >
                    取消
                </Button>
                <Button
                    onClick={onConfirm}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    确定
                </Button>
            </View>
        </Popup>
    );
};

export default Index;
