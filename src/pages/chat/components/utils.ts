import Taro from '@tarojs/taro';
const ttsErrorHandler = (error: any) => {
    if (error.statusCode && error.statusCode === 200) {
        if (error.data.message) {
            switch (error.data.message) {
                case 'LimitExceeded.AccessLimit':
                case '3031':
                case '3003':
                    Taro.showToast({
                        title: '当前访问人数过多，请稍后重试',
                        icon: 'none'
                    });
                    break;
                default:
                    Taro.showToast({
                        title: '语音生成错误',
                        icon: 'none'
                    });
                    break;
            }
        } else {
            Taro.showToast({
                title: '语音生成错误',
                icon: 'none'
            });
        }
    } else {
        if (error.errno === 5 || error.errMsg.includes('time out') || error.errMsg.includes('timeout')) {
            Taro.showToast({
                title: '网络超时',
                icon: 'none'
            });
        } else {
            Taro.showToast({
                title: '语音生成错误',
                icon: 'none'
            });
        }
    }
};

export { ttsErrorHandler };
