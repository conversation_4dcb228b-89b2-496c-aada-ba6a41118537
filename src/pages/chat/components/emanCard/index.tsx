import { Button } from '@hygeia/ui';
import { Image, Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import { useMemo, type CSSProperties } from 'react';

import Star from '@/components/star';
import config from '@/config';
import emanJob from '@/utils/emanJob';
import { useThrottleFn } from 'ahooks';
import styles from './index.less';
const icon = `${config.cdnPrefix}chat/icon1.png`;
const mark_3d = `${config.cdnPrefix}mark_3d.svg`;

export interface EmanCardProps {
    name: string;
    avatar: string;
    background: string;
    department: string;
    /** 职称 */
    title: string;
    /** 场景 */
    scene: any;
    skill: string;
    occupation: string; // 职业
    type: number;
    shuziren: boolean;
    /** 情景演练 */
    onOk: () => void;
    onEmanClick: () => void;
    style?: CSSProperties;
    isEnterprise: number;
    isQnq: boolean;
    emanDifficult: number;
}

const Index: React.FC<EmanCardProps> = (props) => {
    const {
        avatar,
        background,
        name,
        department,
        title,
        scene,
        onOk,
        occupation,
        onEmanClick,
        type,
        shuziren,
        isEnterprise,
        isQnq,
        emanDifficult
    } = props;
    const { run: handleEmanClick } = useThrottleFn(onEmanClick, { wait: 1500, leading: true, trailing: false });
    const { run: handleOk } = useThrottleFn(onOk, { wait: 1500, leading: true, trailing: false });
    const ocp = useMemo(() => {
        return emanJob(occupation, title, department);
    }, [occupation, title, department]);
    return (
        <View className={styles.card} style={props.style}>
            <View className={styles.eman} style={{ backgroundImage: `url(${background})` }}>
                {/* <View className={styles.eman_hot_container}>
                    <Image src={hot} />
                    <View className={styles.hot}>2389</View>
                </View> */}
                {shuziren && <Image src={mark_3d} className={styles.mark_3d} />}

                <View className={styles.eman_scene_container}>
                    {isQnq && type === 1 && emanDifficult !== 0 && (
                        <View className={styles.eman_scene_container_difficult}>
                            <View>拜访难度：</View>
                            <Star value={emanDifficult} />
                        </View>
                    )}
                    <View className={styles.eman_scene_container_content}>
                        <Image src={icon} />
                        <View className={styles.eman_scene}>{scene.scene}</View>
                    </View>
                </View>
            </View>
            <View className={styles.info}>
                <View onClick={handleEmanClick}>
                    <View className={styles['flex-align-items-center']}>
                        <Image className={styles.info_avatar} src={avatar} />
                        <Text className={styles.info_name}>{name}</Text>
                    </View>
                    <Text className={styles.info_occupation}>{ocp}</Text>
                </View>
                <View style={{ marginLeft: 'auto' }}>
                    <Button
                        className={styles.action_btn}
                        style={{
                            '--padding-md': pxTransform(22),
                            '--button-normal-height': pxTransform(58)
                        }}
                        round
                        color={
                            type === 2
                                ? isEnterprise === 1
                                    ? 'linear-gradient(270deg, #FE9041 0%, #FFBD3C 100%)'
                                    : 'linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                                : 'linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                        }
                        onClick={handleOk}
                    >
                        {type === 2 ? (isEnterprise === 1 ? '咨询' : '对话') : '情景演练'}
                    </Button>
                </View>
            </View>
            {/* <View className={styles.skill}>{skill}</View> */}
        </View>
    );
};

export default Index;
