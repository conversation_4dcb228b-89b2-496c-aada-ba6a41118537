@import '@/styles/index.less';

.card {
    width: 668px;
    background-color: #fff;
    border-radius: 32px;
    // overflow: hidden;
    padding-bottom: 24px;
    height: 100%;
    box-sizing: border-box;
}
.eman {
    .wrap-box(32px);

    position: relative;
    height:calc(100vh - 45px - 97px - 98px - 113px - 156px - 100px + 56px);
    color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 32px 32px 0 0;

    .mark_3d {
        width: 48px;
        height: 48px;
        position: absolute;
        top: 28px;
        right: 28px;
        z-index: 1;
    }

    &_scene {
        .font(24px,#fff,400);
        .multi-ellipsis(2);

        flex: 1;
        margin-left: 24px;

    }
    &_scene_container{
        position: absolute;
        bottom: 0;
        height: 348px;
        left: 0;
        padding: 0 30px 48px 30px;
        display: flex;
        justify-content: flex-end;
        flex-direction: column;
        background: linear-gradient(180deg, rgba(0,0,0,0), rgba(0,0,0,100));

        &_difficult {
            .font(24px, #ffffff,400);
            display: flex;
            justify-content: flex-end;
            margin-bottom: 30px;
        }
        &_content {
            display: flex;
            image{
                width: 54px;
                height: 54px;
                margin-bottom: 20px;
            }
        }


    }
    &_hot_container{
        position: absolute;
        top: 22px;
        right: 32px;
        display: flex;
        align-items:center;
        image{
            width: 48px;
            height: 48px;
        }
    }
    .hot{
      margin-left: 8px;
    }
}
.info {
    .wrap-box(0 48px);

    display: flex;
    align-items: center;
    height: 116px;
    box-sizing: border-box;
    margin-top: 40px;
    &_avatar{
        width: 52px;
        height: 52px;
        border-radius: 52px;
        flex-shrink: 0;
    }
    &_name {
        .font(36px, #272c47,500);
        .multi-ellipsis(2);
        margin-left: 16px;
    }
    &_occupation {
        .flex-align-items-center;
        .font(24px,#61626a);

        font-weight: 400;
        margin-top: 16px;
    }
}
.skill {
    .wrap-box(0 48px);
    .font(24px,#61626a);

    margin-top: 16px;
    margin-bottom: 40px;
    .multi-ellipsis(2);
}
.action_btn {
    width: 160px;
}