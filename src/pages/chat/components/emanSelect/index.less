@import '../emanRecent/index.less';

.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(188deg, #dddeff99 0.35%, #f8faff99 24.5%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

  --nav-bar-background-color: transparent;
  --nav-bar-icon-color: #333;
  --nav-bar-arrow-size: 48px;
}

.content {
  margin-top: 30px;
  flex:1;
  height: 0;
  overflow-y: auto;

}
.card {
  margin-left:  32px;
  margin-right: 32px;
}
.empty {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap:24px 0;
  margin-top: 25vh;
  font-size: 28px;
  color: #9597a0;
  font-weight: 400;
  .icon{
      width: 400px;
      height: 205px;
  }
}