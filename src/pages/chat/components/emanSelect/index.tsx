import { getSuitable<PERSON><PERSON> } from '@/services/chat';
import type { EmanVO } from '@/types/chat';
import { Image, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { PageContainer, Text, View } from '@tarojs/components';
import { navigateTo, pxTransform } from '@tarojs/taro';
import { useEffect, useState } from 'react';

import config from '@/config';
import emanJob from '@/utils/emanJob';
import styles from './index.less';

type Props = {
    show: boolean;
    scriptId: string;
    onClose: () => void;
    onChoose: (eman: EmanVO) => void;
};

const Index: React.FC<Props> = (props) => {
    const { show, scriptId, onClose, onChoose } = props;
    const [list, setList] = useState<EmanVO[]>([]);
    const EmanEmpty = `${config.cdnPrefix}empty_eman.png`;
    const requestList = async () => {
        try {
            const res = await getSuitableEman(scriptId);
            setList(res.data.data);
        } catch (error) {
            console.log(error);
        }
    };
    const onEmanClick = (id: string) => {
        navigateTo({
            url: `/pages/emanDetail/index?id=${id}`
        });
    };

    useEffect(() => {
        if (show) {
            requestList();
        }
    }, [show]);
    return (
        <PageContainer zIndex={1000} show={show} position='right' onAfterLeave={onClose}>
            <View className={styles.container}>
                <NavBar className={styles.navbar} title='选择E人' leftArrow safeAreaInsetTop onClickLeft={onClose} />
                <View className={styles.content}>
                    {list.map((item) => (
                        <View key={item.id} className={styles.card}>
                            <View className={styles.info}>
                                <Image
                                    onClick={() => onEmanClick(item.id)}
                                    round
                                    width={pxTransform(118)}
                                    height={pxTransform(118)}
                                    src={item.avatar}
                                />
                                <View onClick={() => onEmanClick(item.id)} className={styles.des}>
                                    <View className={styles.info_name}>{item.name}</View>
                                    <View className={styles.info_occupation}>
                                        {emanJob(item.occupation, item.title, item.department)}
                                    </View>
                                </View>

                                <View style={{ marginLeft: 'auto' }}>
                                    <Button
                                        className={styles.action_btn}
                                        style={{
                                            '--padding-md': pxTransform(22),
                                            '--button-normal-height': pxTransform(58)
                                        }}
                                        round
                                        color='linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                                        onClick={() => onChoose(item)}
                                    >
                                        选择
                                    </Button>
                                    {/* <View className={styles.hot}>热度：2389</View> */}
                                </View>
                            </View>
                            <View className={styles.skill}>擅长方向：{item.skill}</View>
                        </View>
                    ))}
                    {(!list || list.length === 0) && (
                        <View className={styles.empty}>
                            <Image className={styles.icon} src={EmanEmpty} />
                            <Text>暂无合适E人</Text>
                        </View>
                    )}
                </View>
            </View>
        </PageContainer>
    );
};

export default Index;
