import { getSuitable<PERSON>man } from '@/services/chat';
import type { EmanVO } from '@/types/chat';
import { Icon, Image, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, PageContainer, Text, View } from '@tarojs/components';
import Taro, { pxTransform } from '@tarojs/taro';
import { useCallback, useEffect, useMemo, useState } from 'react';

import Star from '@/components/star';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import emanJob from '@/utils/emanJob';
import classNames from 'classNames';
import styles from './index.less';

type Props = {
    show: boolean;
    eman: EmanVO;
    scriptId: string;
    onClose: () => void;
    onChoose: (eman: EmanVO) => void;
};

const Index: React.FC<Props> = (props) => {
    const mark_3d = `${config.cdnPrefix}mark_3d.svg`;
    const { show, scriptId, onClose, onChoose, eman } = props;
    const [list, setList] = useState<EmanVO[]>([]);
    const EmanEmpty = `${config.cdnPrefix}empty_eman.png`;
    const [currentEman, setCurrentEman] = useState<EmanVO | undefined>(eman);
    const accountInfo = Taro.getAccountInfoSync();
    const requestList = async () => {
        try {
            const res = await getSuitableEman(scriptId);
            setList(res.data.data);
            if (eman) {
                setCurrentEman(eman);
            } else {
                if (res.data.data.length > 0) {
                    setCurrentEman(res.data.data[0]);
                }
            }
        } catch (error) {
            console.log(error);
        }
    };
    const showStar = useMemo<boolean>(() => {
        if (currentEman) {
            return accountInfo.miniProgram.appId === AppIdConsts.qnq && currentEman.emanDifficult !== 0;
        } else {
            return false;
        }
    }, [currentEman]);
    const onEmanClick = (eman: EmanVO) => {
        setCurrentEman(eman);
    };

    const emanOcc = useMemo(() => {
        return currentEman ? emanJob(currentEman.occupation, currentEman.title, currentEman.department) : '';
    }, [currentEman]);
    const handleChoose = useCallback(() => {
        console.log(currentEman);
        if (currentEman) {
            onChoose(currentEman);
        }
    }, [currentEman]);

    useEffect(() => {
        if (show) {
            requestList();
        }
    }, [show]);
    return (
        <PageContainer zIndex={1000} show={show} position='right' onAfterLeave={onClose}>
            {list && list.length > 0 && currentEman && (
                <Image src={currentEman?.background ?? ''} fit='cover' className={styles.bg} />
            )}
            <View className={styles.blur} />
            <View className={styles.container}>
                <NavBar
                    className={styles.navbar}
                    title='选择E人'
                    renderLeft={<Icon name='cross' size={pxTransform(40)} color='#ffffff' />}
                    safeAreaInsetTop
                    onClickLeft={onClose}
                />
                <View className={styles.content}>
                    {!list || list.length === 0 ? (
                        <View className={styles.empty}>
                            <Image className={styles.icon} src={EmanEmpty} />
                            <Text>暂无合适E人</Text>
                        </View>
                    ) : (
                        <Block>
                            <View className={styles.card}>
                                {currentEman && (
                                    <Block>
                                        <View
                                            className={styles.eman}
                                            style={{ backgroundImage: `url(${currentEman?.background})` }}
                                        >
                                            {currentEman && currentEman.zipFileUrl && currentEman.show3dFlag && (
                                                <Image src={mark_3d} fit='contain' className={styles.mark_3d} />
                                            )}
                                        </View>
                                        <View className={styles.info}>
                                            <View className={styles.info_name}>{currentEman.name}</View>
                                            <View className={styles.info_line}>
                                                <View className={styles.info_occupation}>{emanOcc}</View>
                                                {showStar && (
                                                    <View className={styles.eman_difficult}>
                                                        <View>拜访难度：</View>
                                                        <Star value={currentEman.emanDifficult} />
                                                    </View>
                                                )}
                                            </View>
                                            <View className={styles.skill}>擅长方向：{currentEman.skill}</View>
                                        </View>
                                    </Block>
                                )}
                            </View>
                            <view className={styles.eman_list}>
                                {list.map((item) => (
                                    // <View
                                    //     key={item.id}
                                    //     className={classNames(styles.eman_item, {
                                    //         [styles.active]: currentEman?.id === item.id
                                    //     })}
                                    // >
                                    //
                                    // </View>
                                    <Image
                                        key={item.id}
                                        onClick={() => onEmanClick(item)}
                                        className={classNames(styles.eman_item_img, {
                                            [styles.active]: currentEman?.id === item.id
                                        })}
                                        src={item.avatar}
                                    />
                                ))}
                            </view>
                            <View className={styles.btn_choose}>
                                <Button
                                    onClick={handleChoose}
                                    style={{
                                        '--padding-md': pxTransform(26),
                                        '--button-normal-height': pxTransform(86)
                                    }}
                                    className={styles.img_style}
                                    round
                                    block
                                >
                                    立即使用
                                </Button>
                            </View>
                        </Block>
                    )}
                </View>
            </View>
        </PageContainer>
    );
};

export default Index;
