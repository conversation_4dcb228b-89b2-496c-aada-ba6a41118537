@import '@/styles/index.less';

.bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
}
.blur {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(80px);
}
.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  --nav-bar-title-text-color: #fff;
}
.navbar {
  position: relative;
  z-index: 2;
  background-color: transparent;
  &::after {
    display: none;
  }
}
.content {
  position: relative;
  z-index: 2;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.card {
  width: 668px;
  border-radius: 32px 32px 0 0;
   overflow: hidden;
  position: relative;
  margin-top: 32px;
  flex: 1;
  box-sizing: border-box;
  mask-image: -webkit-gradient(
          linear,
          left 85%,
          left bottom,
          from(rgba(0,0,0,1)),
          to(rgba(0,0,0,0))
  );

  .eman {
    width: 100%;
    height: 100%;
    .wrap-box(32px);
    color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    border-radius: 32px;

    .mark_3d {
      width: 48px;
      height: 48px;
      position: absolute;
      top: 28px;
      right: 28px;
      z-index: 1;
    }

  }

  &::after {
    content: "";
    display: block;
    height: 200px;
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: 1;
    //mask: linear-gradient( to bottom, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 1) 100% );
    //backdrop-filter: blur(50px);

  }
  .info {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 2;
    padding-left: 32px;
    padding-right: 32px;
    padding-bottom: 60px;
    padding-top: 60px;
    box-sizing: border-box;
    background: linear-gradient(0deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0) 100%);

    &_line {
      display: flex;
      justify-content: space-between;
    }
    .eman_difficult {
      .font(24px, rgba(255, 255, 255, 0.7),400);
      line-height: 1;
      display: flex;
      align-items: center;
    }
    &_name {
      .font(48px, #ffffff,500);
      margin-bottom: 24px;
    }
    &_occupation {
      .flex-align-items-center;
      .font(24px,rgba(255, 255, 255, 0.7));

      font-weight: 400;
    }
    .skill {
      .font(24px,#ffffff);

      margin-top: 12px;
      .multi-ellipsis(2);
    }
  }

}


.eman_list {
  width: 100%;
  overflow-x: auto;
 white-space: nowrap;
  margin-top: 30px;
  text-align: center;
  // 隐藏滚动条
  &::-webkit-scrollbar {
    display:none;
    width:0;
    height:0;
    color:transparent;
  }
}
.eman_item {


  &_img {
    width: 160px;
    height: 160px;
    display: inline-block;
    margin: 10px;
    border-radius: 32px;
    overflow: hidden;
    border-width: 4px;
    border-style: solid;
    border-color: transparent;
    &.active {
      border-color: #fff;
    }

    &:first-child {
      margin-left: 32px;
    }
    &:last-child {
      margin-right: 32px;
    }
  }
}

.btn_choose {
  align-self: stretch;
  margin: 40px 32px 100px 32px;
  .img_style{
    color: #fff;
    opacity: 1;
    background: linear-gradient(90deg, #3C83FF 0%, #6741FE 100%);
    }
}
.empty {
  display: flex;
  align-items: center;
  flex-direction: column;
  gap:24px 0;
  font-size: 28px;
  color: #ffffff;
  font-weight: 400;
  .icon{
      width: 400px;
      height: 205px;
  }
}