import { Storage, useRequest } from '@/common';
import { Page } from '@/components';
import ChatHistory from '@/components/chatHistory';
import ScriptActionSheet from '@/components/scriptActionSheet';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { getAllChatHistory, getChat, getEnam } from '@/services/chat';
import type { ChatVO, ReportVo } from '@/types/chat';
import { Icon, Image, NavBar } from '@antmjs/vantui';
import { PageMeta, Text, View } from '@tarojs/components';
import Taro, { pxTransform, useRouter, useUnload } from '@tarojs/taro';
import { useMount } from 'ahooks';
import { useState } from 'react';
import styles from './dialog.less'; // 跟对话页共用样式
const scriptImg = `${config.cdnPrefix}sceneExercise/script.png`;

const App = () => {
    const { params } = useRouter<{ chatId: string; from: string }>();
    console.log('history', params);
    const { chatId } = params;
    const [showScript, setShowScript] = useState(false);
    const [chat, setChat] = useState<ChatVO>();
    const [reportDetail, setReportDetail] = useState<ReportVo>();
    const { data: history, loading } = useRequest(() => getAllChatHistory(chatId));
    const historicalRecords = true;
    const goBack = () => {
        Taro.navigateBack();
    };

    const onEmanClick = async () => {
        if (chat?.eman.id) {
            try {
                await getEnam(chat.eman.id);
                Taro.navigateTo({
                    url: `/pages/emanDetail/index?id=${chat.eman.id}`
                });
            } catch (error) {
                console.log(error);
            }
        }
    };

    useMount(async () => {
        if (params.from === 'share') {
            const reportData = Storage.get(StorageEnvKey.REPORT_DATA) as ReportVo;
            console.log('reportData', reportData);
            setReportDetail(reportData);
        } else {
            try {
                const res = await getChat(chatId);
                setChat(res.data.data);
            } catch (error) {
                console.log(error);
            }
        }
    });

    useUnload(() => {
        Storage.del(StorageEnvKey.REPORT_DATA);
    });

    return (
        <Page loading={!chat && loading}>
            <PageMeta pageStyle={showScript ? 'overflow: hidden;' : ''} />
            <View className={styles.topBox}>
                <NavBar
                    title='历史记录'
                    safeAreaInsetTop
                    renderLeft={
                        <View className={styles.countDownBox}>
                            <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                        </View>
                    }
                />
                <View className={styles.noticeBar}>对话为AI情景模拟，非真实场景</View>
            </View>
            <ChatHistory
                historyPage
                historicalRecords={historicalRecords}
                scene={params.from === 'share' ? reportDetail?.scene : chat?.scene.scene}
                scriptTime={params.from === 'share' ? reportDetail?.chatStartTime : chat?.createTime}
                eman={{
                    name: '',
                    avatar: ''
                }}
                history={history ?? []}
                emanClick={onEmanClick}
            />
            <View className={styles.bottomContent} style={`padding-bottom:${pxTransform(100)}`}>
                <View className={styles.showScripts} onClick={() => setShowScript(true)}>
                    <Image width={pxTransform(36)} height={pxTransform(36)} src={scriptImg} />
                    <Text>培训脚本</Text>
                </View>
            </View>

            <ScriptActionSheet
                showScript={showScript}
                currentScript={params.from === 'share' ? reportDetail?.scriptDetailVO : chat?.script}
                emanName={params.from === 'share' ? reportDetail?.emanName : chat?.eman.name}
                showScriptClose={() => setShowScript(false)}
            />
        </Page>
    );
};

export default App;
