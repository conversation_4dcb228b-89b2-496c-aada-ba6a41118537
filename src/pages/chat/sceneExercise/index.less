@import '@/styles/index.less';

page {
    background: linear-gradient(188deg, #dddeff99 0.35%, #f8faff99 24.5%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.character {
    .wrap-box(35px);

    display: flex;
    justify-content: space-between;
     &_box {
        width: 326px;
    }
    &_title {
        .font(24px,#9597A0);

        margin-bottom: 16px;
        text-align: center;
    }
    &_card {
        .base-card(32px);

        height: 260px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    &_name {
        .font(28px,#272C47,600);

        margin: 16px 0 12px 0;
    }
    &_job {
     .font(24px,#666666,400);
     .ellipsis();
     width: 100%;
     text-align: center;
    }
}

.eman_select {
    &_circle {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #F0F2FF;
        width: 96px;
        height: 96px;
        border-radius: 96px;
        border: 1px dashed #4F66FF; 
    }
    &_text {
        margin-top: 24px;
        color: #666;
        font-size: 24px;
    }

}

.script {
    box-sizing: border-box;
    flex:1;
    height: 0;
    display: flex;
    flex-direction: column;
    padding: 48px 32px;
    background-color: #fff;
    border-top-left-radius: 32px;
    border-top-right-radius: 32px;

    &_tabs {
        .font(28px,#9597A0,400);

        height: 88px;
        background-color: #F7F7F7;
        box-sizing: border-box;
        padding: 6px;
        border-radius: 16px;
        display: flex;
        justify-content: space-between;
        scroll-behavior: smooth;

        &_select {
            .font(28px,#272C47,600);

            border: 1px solid #EDEDED;
            background: #FFF;
            border-radius: 16px;
            
        }
    }
    &_tab {
        .flex-center;

        width: 339px;
    }

    &_empty {
        .flex-center;

        flex-direction: column;
        .font(28px,#666,400);

        margin-top: 62px;
        height: 353px;
        border: 2px dashed #C9D0FF;
        image{
            width: 112px;
            height: 112px;
        }
        view{
            margin-top: 24px;
        }
    }
    &_button {
        .footer-fixed;

        box-sizing: border-box;
        padding: 0 32px;
        // bottom:constant(safe-area-inset-bottom);
        // bottom:env(safe-area-inset-bottom);
        bottom: 48px;
        z-index: 100;
    }
}

.body {
    margin-top: 30px;
    flex:1;
    height: 0;
    overflow-y: scroll;
    box-sizing: border-box;
    &_title {
        .flex-center;
        .font(28px,#333333,bold);
        text{
            margin-left: 8px;
           
        }
        &_select {
            .font(28px,#4F66FF,bold);
        }
    }
}
.container {
    margin-top: 42px;

    &_item {
        .wrap-box(16px 32px 16px 32px);

        // margin-bottom: 24px;
        min-height: 108px;
        border-radius: 16px;
        &_title {
            .font(32px,#272C47,600);
        }
        &_content {
            .font(28px,#67686F,400);
        }
    }
    &_horizontal {
        display: flex;
        align-items: center;
        justify-self: flex-start;

        .container_item_content {
            margin-left: 48px;
            line-height: 40px;
            
            &_visit {
                flex: 1;
                width: 0;
                .ellipsis();
            }
        }
    }
    &_vertical {
        display: flex;
        justify-content: center;
        flex-direction: column;

        .container_item_content {
            margin-top: 10px;
            line-height: 40px;
        }
    }
}
.customContainer {

    &_item {
        .wrap-box(32px);

        margin-bottom: 24px;
        min-height: 108px;
        border-radius: 16px;
        background-color: #F7F7F7;
        &_title {
            .font(32px,#272C47,600);
        }
        &_content {
            .font(28px,#272C47,400);
        }
    }
    &_horizontal {
        display: flex;
        align-items: center;

        .customContainer_item_content {
            margin-left: auto;
            text-align: right;
        }
    }
    &_vertical {
        display: flex;
        justify-content: center;
        flex-direction: column;

        .customContainer_item_content {
            margin-top: 10px;
        }
    }
}
.showCount{
    margin-top: -10px;
    font-size: 24px;
    padding: 0 24px 24px 0;
    text-align: right;
}
.actionSheet {
    :global{
        .van-action-sheet__header{
            font-size: 36px;
            font-weight: bold;
        }
        .van-cell{
            border-radius: 20px;
        }
        .van-cell + .van-cell{
            margin-top: 16px;
        }
        .van-cell__title{
            margin-left: 10px;
        }
    }
    &_content{
        padding: 32px 32px 0 32px;
        height: 45vh;
        display: flex;
        flex-direction: column;

        &_scripts {
            // height: 75vh;
            height: auto;
        }
    }
}

.scripts_type {
    display: flex;
    gap: 16px;
    margin-bottom: 36px;
    &_item {
        border-radius: 16px;
        padding: 16px 30px;
        border: 1px solid #EDEDED;
        color: #272C47;

        &_active {
            border: 1px solid #4F66FF;
            color: #4F66FF;
        }
    }
}
.scripts_list {
    flex: 1;
    max-height: 70vh;
    min-height: 40vh;
    overflow-y: auto;
    box-sizing: border-box;
    padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */

    &_android {
        padding-bottom: 16px;
    }
}
.split_line {
    text-align: center;
    color: #9597A0;
    font-size: 24px;
    margin-top: 36px;
    margin-bottom: 36px;
    display: flex;
    align-items: center;

    &_text {
        margin-left: 10px;
        margin-right: 10px;
    }

    &::before {
        content: '';
        display: block;
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, #FFF, #9597A0);
    }

    &::after {
        content: '';
        display: block;
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg,#9597A0, #FFF);
    }
}

.empty{
    margin-top: 40%;
    transform: translateY(-40%);
    display: flex;
    align-items: center;
    flex-direction: column;
    gap:24px 0;
    font-size: 28px;
    color: #9597a0;
    font-weight: 400;

    &_script {
        margin-top: 67px;
        transform: translateY(0);
    }
    .icon{
        width: 400px;
        height: 205px;
    }
}

.conflict {
    height: 99px;
    border-radius: 32px;
    background-color: rgba(0,0,0, 0.75);
    display: flex;
    align-items: center;
    padding: 0 52px;
    box-sizing: border-box;
    margin-bottom: 24px;

    &_name {
        font-weight: 400;
        font-size: 28px;
        color: #fff;
    }
    &_tip {
        font-size: 24px;
        flex: 1;
        text-align: right;
        color:  #D5D5D5;
    }

    &_arrow {
        margin-left: 1px;
    }
}

.popup_conflict {
    :global{
        .van-action-sheet__header{
            font-size: 36px;
            font-weight: bold;
        }
    }
    &_content {
        padding: 0 32px 0 32px;
    }
    &_tip {
        font-size: 24px;
        text-align: center;
        color: #9597A0;
        margin-left: 32px;
        margin-right: 32px;
    }
    &_info {
        display: flex;
        justify-content: space-between;
        margin: 32px;
        font-size: 28px;
        text {
            flex: 1;
            width: 0;
            .ellipsis();

            &:last-child {
                text-align: right;
            }
        }
    }
    &_btn {
        margin-top: 28px;
        margin-bottom: 32px;
        &_ios {
            margin-bottom: 0;
        }
    }
}
.popup_unlock {
    :global{
        .van-action-sheet__header{
            font-size: 36px;
            font-weight: bold;
        }
    }
    &_content {
        padding: 0 32px 0 32px;
    }
    &_tip {
        font-size: 24px;
        text-align: center;
        color: #9597A0;

    }
    &_btn {
        margin-top: 28px;
        margin-bottom: 32px;
        &_ios {
            margin-bottom: 0;
        }
    }
    
    &_list {
        margin: 32px;
    }
    &_task {
        padding-top: 32px;
        padding-bottom: 32px;
        border-bottom: 1px solid;
        border-image: linear-gradient(90deg, #fff, #9597A0 50%, #fff);
        border-image-slice: 1;


        &:last-child {
            border: none;
        }
    }
    &_item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        
        &:last-child {
            margin-bottom: 0;
        }
    }
    &_name {
        font-size: 28px;
        font-weight: bold;
    }
    &_value {
        font-size: 28px;
        color: #67686F;
        display: flex;
        align-items: center;
    }
}

.challenge_text {
    width: 150px;
    height: 50px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}
.script_button_horizontal {
    display: flex;
    gap: 24px;
}
.btn {
    &_choose_eman {
        width: 250px;
    }
    &_start_practice {
        flex: 1;
    }
}
.change_eman {
    display: flex;align-items: center; gap: 10px;
}
.imgss{
    width: 94px;
    height: 94px;
    border-radius: 32px;
}
.btn_choose_eman{
    background: #eef0ff;
}
.btn_nochoose_eman{
    background: linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%);
}