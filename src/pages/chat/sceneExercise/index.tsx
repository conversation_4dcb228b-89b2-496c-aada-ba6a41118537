import { Storage, useEvent, useStorage } from '@/common';
import { Page } from '@/components';
import { StorageEnvKey } from '@/constants/storage';
import {
    createChat,
    createChatCustom,
    doneChat,
    getAllScript,
    getChat,
    getEnam,
    getLastChatScript,
    getScript,
    getSuitableEman,
    scriptUnlockCondition
} from '@/services/chat';
import { getMyEManDetail } from '@/services/eman';
import type { EmanVO, ScriptUnlockConditionVo, ScriptVO, UnlockCondition } from '@/types/chat';
import type { UserInfo } from '@/types/common';
import { ActionSheet, Cell, CellGroup, Dialog, Icon, Image, NavBar, Radio, RadioGroup, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Input, Text, Textarea, View } from '@tarojs/components';
import Taro, { navigateTo, pxTransform, useLoad, useRouter } from '@tarojs/taro';
import classnames from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import EmanSelect from '../components/emanSelect/index';

import HarmonyDialog from '@/components/HarmonyDialog';
import config from '@/config';
import { OpenMode } from '@/constants/OpenMode';
import { ChatMode } from '@/constants/voicetype';
import { useLockFn } from '@/hooks';
import ScriptEmanSelect from '@/pages/chat/components/scriptEmanSelect';
import { getRandomScene } from '@/services/common';
import type { EManDetailVo } from '@/types/eman';
import emanJob from '@/utils/emanJob';
import { checkHarmony, checkRecordPermission } from '@/utils/permission';
import tenantSettingUtils from '@/utils/tenantSettingUtils';
import { useThrottleFn } from 'ahooks';
import styles from './index.less';

const empty = `${config.cdnPrefix}mine/empty.png`;
const scriptImg = `${config.cdnPrefix}sceneExercise/script.png`;
const scriptBgImg = `${config.cdnPrefix}sceneExercise/scriptBg.png`;
const IconLockWhite = `${config.cdnPrefix}svg/icon_lock_white.svg`;
const IconLockYellow = `${config.cdnPrefix}svg/icon_lock_yellow.svg`;
const StartChallenge = `${config.cdnPrefix}svg/start_challenge.svg`;
const scriptSwitch = `${config.cdnPrefix}svg/script_switch.svg`;
const container = `${config.cdnPrefix}x/container.png`;
enum ScriptTabEnum {
    SCRIPT = 1,
    CUSTOM = 2
}
enum ChatType {
    ALL = 0,
    ANSWER = 2,
    SKILL = 1
}

const Dialog_ = Dialog.createOnlyDialog();
const Toast_ = Toast.createOnlyToast();
const App = () => {
    const { params } = useRouter<{
        emanId: string;
        sceneId: string;
        scriptId: string;
        from?: string;
        stageId: string;
        goChallenge: string;
        action?: string;
    }>();
    console.log('params', params);
    const { emanId, sceneId, from, scriptId, stageId, goChallenge, action } = params;
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const sceneIdRef = useRef<string>(sceneId);
    const { platform } = Taro.getSystemInfoSync();
    const [scriptTab, setScriptTab] = useState<ScriptTabEnum>(ScriptTabEnum.SCRIPT);
    const [userInfo] = useStorage<UserInfo>(StorageEnvKey.USERINFO);
    const [showActionSheet, setShowActionSheet] = useState(false);
    const [currentScript, setCurrentScript] = useState<ScriptVO | null>();
    const [currentScriptId, setCurrentScriptId] = useState<string>();
    const [customScript, setCustomScript] = useState<ScriptVO>({
        backdrop: '',
        createTime: '',
        deleted: false,
        goal: '',
        id: '',
        location: '医生办公室',
        name: '',
        product: '',
        time: '13:30',
        timeLimit: 10,
        trust: 0,
        updateTime: '',
        type: 0,
        question: [],
        randomFlag: null
    });
    const [currentScriptType, setCurrentScriptType] = useState<ChatType>(ChatType.ALL);
    const [currentScripts, setCurrentScripts] = useState<ScriptVO[]>([]);
    const [applicableScripts, setApplicableScripts] = useState<ScriptVO[]>([]);
    const [unapplicableScripts, setUnapplicableScripts] = useState<ScriptVO[]>([]);
    const [scriptTypes, setScriptTypes] = useState<number[]>([]);
    const [randomText, setRandomText] = useState<string>('');
    const [showActionSheetUnend, setShowActionSheetUnend] = useState(false);
    const [unCloseChatId, setUnCloseChatId] = useState<string>('');
    const [conflictShow, setConflictShow] = useState(false);
    const [showPopupConflict, setShowPopupConflict] = useState(false);
    const [showPopupUnlock, setShowPopupUnlock] = useState(false);
    const [taskCondition, setTaskCondition] = useState<ScriptUnlockConditionVo[]>();
    const [scripts, setScripts] = useState<ScriptVO[]>();
    const [eman, setEman] = useState<EManDetailVo>();
    const [showEmanSelect, setShowEmanSelect] = useState(false);
    const [showScriptListEmanSelect, setShowScriptListEmanSelect] = useState(false); // 脚本列表过来的
    const [canSelectEman, setCanSelectEman] = useState(false);
    const [canSelectScript, setCanSelectScript] = useState(false);
    const isHarmonyRef = useRef<boolean>(false);
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    // const { data: scripts } = useRequest(() => getAllScript(emanId), {
    //     // getAllScript暂无分页，有分页的话如果上次的系统脚本不在第一页还得处理一下
    //     onSuccess: (scriptData: ScriptVO[]) => {
    //         // console.log('getEnam-data');
    //         if (scriptData) {
    //             setScriptTypes(Array.from(new Set(scriptData.map((script: any) => script.type))));
    //             getLastChatScript(emanId).then(async (response) => {
    //                 // console.log('getLastChatScript-data', response.data);
    //                 const res = response.data;
    //                 if (res.code === 200 && res.data) {
    //                     // 判断脚本是否适用e人
    //                     const fd = scriptData?.find((item: any) => item.id === res.data.scriptId);
    //                     if (fd) {
    //                         if (res.data.type === 1 && res.data.scriptId) {
    //                             // 脚本
    //                             setScriptTab(ScriptTabEnum.SCRIPT);
    //                             const { data } = await getScript(res.data.scriptId);
    //                             if (data.code === 200) {
    //                                 setCurrentScript(data.data);
    //                                 setCurrentScriptId(data.data.id);
    //                                 handleRandomText(data.data.randomFlag, data.data.randomNum, data.data.question);
    //                                 if (!fd.applicable) {
    //                                     setConflictShow(true);
    //                                 }
    //                                 getScriptCondition(data.data.id);
    //                             }
    //                         } else if (res.data.type === 2 && res.data.backdrop) {
    //                             // 自定义
    //                             setScriptTab(ScriptTabEnum.CUSTOM);
    //                             setCustomScript(res.data);
    //                         }
    //                     }
    //                 }
    //             });
    //         }
    //     }
    // });

    const requestEman = async () => {
        try {
            const res = await getEnam(emanId);
            setEman(res.data.data);

            return res.data.data;
        } catch (error) {
            console.log(error);
            return null;
        }
    };

    const requestScript = async () => {
        try {
            const { data } = await getScript(scriptId);

            setCurrentScript(data.data);
            setCurrentScriptId(scriptId);
            handleRandomText(data.data.randomFlag, data.data.randomNum, data.data.question);
        } catch (error) {
            console.log(error);
        }
    };

    const reuqestAllScript = async () => {
        try {
            const allScriptRes = await getAllScript(emanId, '1,2');
            console.log(allScriptRes, 'allScriptRes');
            const scriptData = allScriptRes.data.data;
            if (scriptData) {
                setScripts(scriptData);
                setScriptTypes(Array.from(new Set(scriptData.map((script: any) => script.type))));
                getLastChatScript(emanId).then(async (response) => {
                    // console.log('getLastChatScript-data', response.data);
                    const res = response.data;
                    if (res.code === 200 && res.data) {
                        // 判断脚本是否适用e人
                        const fd = scriptData?.find((item: any) => item.id === res.data.scriptId);
                        if (fd) {
                            if (res.data.type === 1 && res.data.scriptId) {
                                // 脚本
                                setScriptTab(ScriptTabEnum.SCRIPT);
                                const { data } = await getScript(res.data.scriptId);
                                if (data.code === 200) {
                                    setCurrentScript(data.data);
                                    setCurrentScriptId(data.data.id);
                                    handleRandomText(data.data.randomFlag, data.data.randomNum, data.data.question);
                                    if (!fd.applicable) {
                                        setConflictShow(true);
                                    }
                                    getScriptCondition(data.data.id);
                                }
                            } else if (res.data.type === 2 && res.data.backdrop) {
                                // 自定义
                                setScriptTab(ScriptTabEnum.CUSTOM);
                                setCustomScript(res.data);
                            }
                        }
                    }
                });
            }
        } catch (error) {
            console.log(error);
        }
    };

    async function getScriptCondition(scriptId: string) {
        try {
            const res = await scriptUnlockCondition(scriptId);
            setTaskCondition(res.data.data);
        } catch (error) {
            console.log(error);
        }
    }
    const onSelectScript = async (item: ScriptVO) => {
        console.log(item);
        setConflictShow(!item.applicable);
        setShowActionSheet(false);
        const { data } = await getScript(item.id);
        if (data.code === 200) {
            setCurrentScript(data.data);
            setCurrentScriptId(item.id);
            handleRandomText(data.data.randomFlag, data.data.randomNum, data.data.question);
        }
        getScriptCondition(item.id);
    };

    function handleRandomText(randomFlag: number, randomNum: number, questions: any[]) {
        let text = '';
        //  0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
        switch (randomFlag) {
            case 0:
                text = '按顺序提问所有问题';
                break;
            case 1:
                text = '随机提问所有题目';
                break;

            case 2:
                text = `随机提问${randomNum}题`;
                break;
            case 3:
                // 向上取整randomNum
                text = `随机提问${Math.ceil((randomNum / 100) * questions.length)}题`;
                break;
            default:
                text = '';
        }
        setRandomText(text);
    }

    const handleChat = useCallback(async () => {
        try {
            // if (scriptTab === ScriptTabEnum.SCRIPT) {
            const params: any = {
                emanId,
                sceneId: sceneIdRef.current,
                scriptId: currentScript!.id
            };
            if (from === 'checkpoint') {
                params.stageId = stageId;
                params.emanId = eman?.id;
            }
            if (from === 'scriptList') {
                params.emanId = eman?.id;
            }
            console.log(from, params);

            if (eman?.zipFileUrl && eman?.show3dFlag) {
                if (
                    isHarmonyRef.current ||
                    Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                    Storage.get(StorageEnvKey.CHAT_MODE) === null
                ) {
                    try {
                        const { data } = await createChat(params);
                        const url = `/pages/shuziren/index?chatId=${data?.data?.id}&chatReportId=${
                            data?.data?.chatReportId
                        }&introductionType=${data.data.introductionType}&introductionDelay=${
                            data.data.introductionDelay
                        }&introduction=${data.data.introduction || ''}`;
                        Taro.navigateTo({
                            url
                        });
                    } catch (error) {
                        setCurrentScript(null);
                        setCurrentScriptId('');
                    }
                } else {
                    try {
                        const { data } = await createChat(params);
                        const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${
                            data?.data?.id
                        }&chatReportId=${data?.data?.chatReportId}&introductionType=${
                            data.data.introductionType
                        }&introductionDelay=${data.data.introductionDelay}&introduction=${
                            data.data.introduction || ''
                        }&scriptId=${scriptId}`;
                        Taro.navigateTo({
                            url
                        });
                    } catch (error) {
                        setCurrentScript(null);
                        setCurrentScriptId('');
                    }
                }
            } else {
                if (isHarmonyRef.current && currentScript?.disableTextInputFlag) {
                    setShowHarmonyDialog(true);
                } else {
                    try {
                        const { data } = await createChat(params);
                        const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${
                            data?.data?.id
                        }&chatReportId=${data?.data?.chatReportId}&introductionType=${
                            data.data.introductionType
                        }&introductionDelay=${data.data.introductionDelay}&introduction=${
                            data.data.introduction || ''
                        }&scriptId=${scriptId}`;
                        Taro.navigateTo({
                            url
                        });
                    } catch (error) {
                        setCurrentScript(null);
                        setCurrentScriptId('');
                    }
                }
            }
            // }
            /*  if (scriptTab === ScriptTabEnum.CUSTOM) {
                if (customScript.backdrop.length > 400) {
                    Taro.showToast({ title: '背景文字太多啦', icon: 'none' });
                    return;
                }
                if (customScript.goal.length > 100) {
                    Taro.showToast({ title: '目标文字太多啦', icon: 'none' });
                    return;
                }
                const { data } = await createChatCustom({
                    ...customScript,
                    emanId,
                    sceneId: sceneIdRef.current
                });
                if (data.code === 200) {
                    const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${data?.data?.id}&chatReportId=${data?.data?.chatReportId}&scriptId=${scriptId}`;
                    Taro.navigateTo({
                        url
                    });
                } else {
                    Taro.showToast({
                        title: data.message,
                        icon: 'error'
                    });
                }
            } */
        } catch (error) {
            // Taro.showToast({
            //     title: error,
            //     icon: 'error'
            // });
            console.log('permission', error);
        }
    }, [eman, scriptTab, currentScript]);

    const { run: continueChat } = useThrottleFn(
        async () => {
            // 继续对话
            try {
                const res = await getChat(unCloseChatId);
                const { data } = res.data;

                if (data.eman.zipFileUrl && data.eman.show3dFlag) {
                    if (
                        isHarmonyRef.current ||
                        Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                        Storage.get(StorageEnvKey.CHAT_MODE) === null
                    ) {
                        const url = `/pages/shuziren/index?chatId=${unCloseChatId}&interrupt=1`;
                        Taro.navigateTo({ url });
                    } else {
                        try {
                            const scriptDataRes = await getScript(data.script.id);
                            if (isHarmonyRef.current && scriptDataRes.data.data.disableTextInputFlag) {
                                setShowHarmonyDialog(true);
                            } else {
                                const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${unCloseChatId}&interrupt=1&scriptId=${data.script.id}`;
                                Taro.navigateTo({ url });
                            }
                        } catch (error) {}
                    }
                } else {
                    try {
                        const scriptDataRes = await getScript(data.script.id);
                        if (isHarmonyRef.current && scriptDataRes.data.data.disableTextInputFlag) {
                            setShowHarmonyDialog(true);
                        } else {
                            const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${unCloseChatId}&interrupt=1&scriptId=${data.script.id}`;
                            Taro.navigateTo({ url });
                        }
                    } catch (error) {}
                }
            } catch (error) {
                console.log(error);
            }
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );
    const { run: newChat } = useThrottleFn(
        async () => {
            // 结束并开启新对话
            try {
                await doneChat(unCloseChatId, false);
                handleChat();
            } catch (error) {
                console.log(error);
            }
            setUnCloseChatId('');
            setShowActionSheetUnend(false);
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    const checkUndone = useEvent(async () => {
        const params: any = {
            emanId,
            sceneId: sceneIdRef.current,
            scriptId: currentScript!.id
        };
        if (from === 'checkpoint') {
            params.stageId = stageId;
            params.emanId = eman?.id;
        }
        if (from === 'scriptList') {
            params.emanId = eman?.id;
        }
        console.log('checkUndone', from, params, isHarmonyRef.current, currentScript?.disableTextInputFlag);

        if (eman?.zipFileUrl && eman?.show3dFlag) {
            if (
                isHarmonyRef.current ||
                Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null
            ) {
                try {
                    const { data } = await createChat(params, false);
                    const url = `/pages/shuziren/index?chatId=${data?.data?.id}&chatReportId=${
                        data?.data?.chatReportId
                    }&introductionType=${data.data.introductionType}&introductionDelay=${
                        data.data.introductionDelay
                    }&introduction=${data.data.introduction || ''}`;
                    Taro.navigateTo({
                        url
                    });
                } catch (error) {
                    const { data } = error;
                    if (data) {
                        if (data.code === 10002) {
                            // 有未结束的对话
                            setShowActionSheetUnend(true);
                            setUnCloseChatId(data.data);
                        } else if (data.code === 401) {
                            Taro.showToast({
                                icon: 'none',
                                title: '登录过期，重新登录',
                                mask: true
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: data.message
                            });
                        }
                    } else {
                        Taro.showToast({
                            icon: 'none',
                            title: '网络错误'
                        });
                    }
                }
            } else {
                try {
                    const { data } = await createChat(params, false);
                    const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${data?.data?.id}&chatReportId=${
                        data?.data?.chatReportId
                    }&introductionType=${data.data.introductionType}&introductionDelay=${
                        data.data.introductionDelay
                    }&introduction=${data.data.introduction || ''}&scriptId=${scriptId}`;
                    Taro.navigateTo({
                        url
                    });
                } catch (error) {
                    const { data } = error;
                    if (data) {
                        if (data.code === 10002) {
                            // 有未结束的对话
                            setShowActionSheetUnend(true);
                            setUnCloseChatId(data.data);
                        } else if (data.code === 401) {
                            Taro.showToast({
                                icon: 'none',
                                title: '登录过期，重新登录',
                                mask: true
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: data.message
                            });
                        }
                    } else {
                        Taro.showToast({
                            icon: 'none',
                            title: '网络错误'
                        });
                    }
                }
            }
        } else {
            if (isHarmonyRef.current && currentScript?.disableTextInputFlag) {
                setShowHarmonyDialog(true);
            } else {
                try {
                    const { data } = await createChat(params, false);
                    const url = `/pages/chat/dialogue/index?text=${goChallenge}&chatId=${data?.data?.id}&chatReportId=${
                        data?.data?.chatReportId
                    }&introductionType=${data.data.introductionType}&introductionDelay=${
                        data.data.introductionDelay
                    }&introduction=${data.data.introduction || ''}&scriptId=${scriptId}`;
                    Taro.navigateTo({
                        url
                    });
                } catch (error) {
                    const { data } = error;
                    if (data) {
                        if (data.code === 10002) {
                            // 有未结束的对话
                            setShowActionSheetUnend(true);
                            setUnCloseChatId(data.data);
                        } else if (data.code === 401) {
                            Taro.showToast({
                                icon: 'none',
                                title: '登录过期，重新登录',
                                mask: true
                            });
                        } else {
                            Taro.showToast({
                                icon: 'none',
                                title: data.message
                            });
                        }
                    } else {
                        Taro.showToast({
                            icon: 'none',
                            title: '网络错误'
                        });
                    }
                }
            }
        }
    });

    const openEmanDetail = () => {
        if (from === 'checkpoint' || from === 'scriptList') {
            // 选择
        } else {
            Taro.navigateTo({
                url: `/pages/emanDetail/index?id=${emanId}`
            });
        }
    };

    const handleTaskComplete = async (condition: UnlockCondition) => {
        if (condition.currentNum >= condition.needNum) return;
        if (condition.permissionFlag) {
            setShowPopupUnlock(false);
            try {
                const allScriptRes = await getAllScript(emanId);
                const scriptData = allScriptRes.data.data;
                const fd = scriptData?.find((item: any) => item.id === condition.scriptId);
                if (fd) {
                    const { data } = await getScript(condition.scriptId);
                    if (data.code === 200) {
                        setCurrentScript(data.data);
                        setCurrentScriptId(data.data.id);
                        handleRandomText(data.data.randomFlag, data.data.randomNum, data.data.question);
                        if (!fd.applicable) {
                            setConflictShow(true);
                        }
                        getScriptCondition(data.data.id);
                    }
                }
            } catch (error) {
                console.log(error);
            }
        } else {
            Toast.show({
                message: '无任务脚本权限\n请联系管理员'
            });
        }
    };

    const handleSelectScript = useCallback(() => {
        if (canSelectScript) {
            setShowActionSheet(true);
        } else {
            if (from === 'checkpoint') {
                Taro.showToast({
                    icon: 'none',
                    title: '闯关模式不支持切换脚本'
                });
            }
        }
    }, [canSelectScript, from]);

    let startY = 0;
    const moveThreshold = 30; // 滑动的阈值，超过这个值则认为开始滑动
    const handleTouchStart = (e: TouchEvent) => {
        startY = e.touches[0].clientY;
    };
    const handleTouchMove = (e: TouchEvent) => {
        const moveY = e.touches[0].clientY - startY;
        if (moveY > moveThreshold) {
            setShowActionSheet(false);
        }
    };

    const [handleStartPractice, startPracticeLoading] = useLockFn(
        useCallback(async () => {
            try {
                await checkRecordPermission(Dialog_);
                if (eman) {
                    if (currentScript?.lockFlag) {
                        setShowPopupUnlock(true);
                    } else {
                        if (!conflictShow) {
                            await checkUndone();
                        }
                    }
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '缺少合适的E人，请联系管理员'
                    });
                }
            } catch (error) {
                Taro.showToast({
                    title: '录音授权失败',
                    icon: 'none'
                });
            }
        }, [eman, conflictShow, currentScript])
    );
    //     async () => {
    //         if (eman) {
    //             if (currentScript?.lockFlag) {
    //                 setShowPopupUnlock(true);
    //             } else {
    //                 if (!conflictShow) {
    //                     checkUndone();
    //                 }
    //             }
    //         } else {
    //             Taro.showToast({
    //                 icon: 'none',
    //                 title: '缺少合适的E人，请联系管理员'
    //             });
    //         }
    //     },
    //     { wait: 1200, leading: true, trailing: false }
    // );

    const goBack = () => {
        const pages = Taro.getCurrentPages();
        if (pages.length > 1) {
            Taro.navigateBack();
        } else {
            tenantSettingUtils((homePath: string) => {
                Taro.switchTab({
                    url: homePath
                });
            });
        }
    };

    const handleNormal = async () => {
        setCanSelectEman(false);
        setCanSelectScript(true);
        requestEman();
        reuqestAllScript();
    };

    const fromCheckpoint = async () => {
        setCanSelectEman(true);
        setCanSelectScript(false);
        try {
            const res = await getSuitableEman(scriptId);
            const { data } = res.data;
            if (data && data.length > 0) {
                setEman(data[0]);
                if (!sceneIdRef.current) {
                    const res = await getRandomScene(data[0].occupation, data[0].title);
                    sceneIdRef.current = res.data.data.id;
                }
            }
        } catch (error) {
            console.log(error);
        }
        requestScript();
    };

    const handleScriptList = async () => {
        setCanSelectEman(true);
        setCanSelectScript(false);
        requestScript();
    };

    const handleOpenMode = async () => {
        setCanSelectEman(false);
        setCanSelectScript(false);
        const emanData = await requestEman();
        if (emanData) {
            try {
                const res = await getRandomScene(emanData.occupation, emanData.title);
                sceneIdRef.current = res.data.data.id;
            } catch (error) {
                console.log(error);
            }
        }
        requestScript();
    };

    const handleChooseEman = () => {
        if (from === 'checkpoint') {
            setShowEmanSelect(true);
        } else if (from === 'scriptList') {
            console.log('111222');
            setShowScriptListEmanSelect(true);
        }
    };

    const onEmanChoose = async (eman: EmanVO) => {
        if (from === 'checkpoint') {
            setEman(eman);
            setShowEmanSelect(false);
        } else if (from === 'scriptList') {
            setShowScriptListEmanSelect(false);
            if (!sceneIdRef.current) {
                const res = await getRandomScene(eman.occupation, eman.title);
                sceneIdRef.current = res.data.data.id;
            }
            setEman(eman);
        }
    };

    useEffect(() => {
        const filterTypeData = (scripts || []).filter(
            (item: any) => currentScriptType === ChatType.ALL || item.type === currentScriptType
        );
        setCurrentScripts(filterTypeData);
        const applicable = filterTypeData.filter((item: any) => item.applicable);
        // 未锁定放前面, 锁定放后面
        setApplicableScripts(applicable);
        const unApplicable = filterTypeData.filter((item: any) => !item.applicable);
        setUnapplicableScripts(unApplicable);
    }, [currentScriptType, scripts]);

    const initPage = async () => {
        const isHarmony = await checkHarmony();
        console.log('isHarmony', isHarmony);
        isHarmonyRef.current = isHarmony;
        if (from === 'checkpoint') {
            // 請求e人、脚本
            fromCheckpoint();
        } else if (from === 'scriptList') {
            handleScriptList();
        } else {
            if (action && action === OpenMode.Script) {
                // 扫码进来的
                handleOpenMode();
            } else {
                handleNormal();
            }
        }
    };

    useLoad(() => {
        console.log('from', from);
        initPage();
    });
    console.log(scriptTypes, 'scriptTypesscriptTypes');

    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title='情景演练'
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => goBack()}
            />
            <View className={styles.character}>
                <View className={styles.character_box}>
                    <View className={styles.character_title}>E人角色</View>
                    {canSelectEman ? (
                        <View className={styles.character_card} onClick={handleChooseEman}>
                            {eman ? (
                                <Block>
                                    <Image
                                        round
                                        fit='cover'
                                        width={pxTransform(96)}
                                        height={pxTransform(96)}
                                        src={eman?.avatar as string}
                                    />
                                    <View className={styles.character_name}>{eman?.name}</View>
                                    <Text className={styles.character_job}>
                                        {emanJob(eman.occupation, eman.title, eman.department)}
                                    </Text>
                                </Block>
                            ) : (
                                <Block>
                                    <View>
                                        <Image className={styles.imgss} fit='cover' src={container} />
                                        {/* <Icon name='plus' color='#4F66FF' size={pxTransform(48)} /> */}
                                    </View>
                                    <View className={styles.character_name}>请选择</View>
                                    <Text className={styles.character_job}>拜访对象</Text>
                                </Block>
                            )}
                        </View>
                    ) : (
                        <View className={styles.character_card} onClick={openEmanDetail}>
                            <Image
                                round
                                fit='cover'
                                width={pxTransform(96)}
                                height={pxTransform(96)}
                                src={eman?.avatar as string}
                            />
                            <View className={styles.character_name}>{eman?.name}</View>
                            <View className={styles.character_job}>
                                {eman && emanJob(eman.occupation, eman.title, eman.department)}
                            </View>
                        </View>
                    )}
                </View>
                <View className={styles.character_box}>
                    <View className={styles.character_title}>我的角色</View>
                    <View className={styles.character_card}>
                        <Image
                            round
                            fit='cover'
                            width={pxTransform(96)}
                            height={pxTransform(96)}
                            src={userInfo!.avatar || AvatarDefault}
                        />
                        <View className={styles.character_name}>我</View>
                        <Text className={styles.character_job}>
                            {currentScript ? currentScript.applicableObject : '请先选择脚本'}
                        </Text>
                    </View>
                </View>
            </View>
            <View className={styles.script}>
                {/* <View className={styles.script_tabs}>
                    <View
                        className={classnames(styles.script_tab, {
                            [styles.script_tabs_select]: scriptTab === ScriptTabEnum.SCRIPT
                        })}
                        onClick={() => setScriptTab(ScriptTabEnum.SCRIPT)}
                    >
                        脚本库
                    </View>
                    <View
                        className={classnames(styles.script_tab, {
                            [styles.script_tabs_select]: scriptTab === ScriptTabEnum.CUSTOM
                        })}
                        onClick={() => {
                            if (currentScript?.type === ChatType.SKILL) {
                                setScriptTab(ScriptTabEnum.CUSTOM);
                            } else if (currentScript?.type === ChatType.ANSWER) {
                                Taro.showToast({
                                    title: '功能暂未开放',
                                    icon: 'none'
                                });
                            }
                        }}
                    >
                        自定义
                    </View>
                </View> */}
                {scriptTab === ScriptTabEnum.SCRIPT &&
                    (currentScript ? (
                        <View
                            className={styles.body}
                            style={{ paddingBottom: conflictShow ? pxTransform(200) : pxTransform(80) }}
                        >
                            <View
                                className={classnames(styles.body_title, {
                                    [styles.body_title_select]: canSelectScript
                                })}
                                onClick={handleSelectScript}
                            >
                                {canSelectScript && (
                                    <Image width={pxTransform(32)} height={pxTransform(32)} src={scriptSwitch} />
                                )}
                                <Text>{currentScript?.name}</Text>
                            </View>
                            <View className={styles.container}>
                                {currentScript?.type === ChatType.SKILL ? (
                                    <Block>
                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>拜访对象</Text>
                                            <Text
                                                className={classnames(
                                                    styles.container_item_content,
                                                    styles.container_item_content_visit
                                                )}
                                            >
                                                {currentScript?.visitObject}
                                                {currentScript?.visitObject && currentScript?.department && '-'}
                                                {currentScript?.department &&
                                                    currentScript?.department
                                                        .split(',')
                                                        .map((item) => item.split('-')[item.split('-').length - 1])
                                                        .join('、')}
                                            </Text>
                                        </View>
                                        <View className={classnames(styles.container_item, styles.container_vertical)}>
                                            <Text className={styles.container_item_title}>背景</Text>

                                            <Text className={styles.container_item_content}>
                                                {eman
                                                    ? currentScript.backdrop.replaceAll('【拜访对象】', eman.name)
                                                    : currentScript.backdrop}
                                            </Text>
                                        </View>
                                        <View className={classnames(styles.container_item, styles.container_vertical)}>
                                            <Text className={styles.container_item_title}>目标</Text>

                                            <Text className={styles.container_item_content}>
                                                {eman
                                                    ? currentScript.goal.replaceAll('【拜访对象】', eman.name)
                                                    : currentScript.goal}
                                            </Text>
                                        </View>
                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>产品</Text>
                                            <Text className={styles.container_item_content}>
                                                {currentScript.product}
                                            </Text>
                                        </View>
                                        {/* <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>时间</Text>
                                            <Text className={styles.container_item_content}>{currentScript.time}</Text>
                                        </View> */}
                                        {currentScript.location && (
                                            <View
                                                className={classnames(
                                                    styles.container_item,
                                                    styles.container_horizontal
                                                )}
                                            >
                                                <Text className={styles.container_item_title}>地点</Text>
                                                <Text className={styles.container_item_content}>
                                                    {currentScript.location}
                                                </Text>
                                            </View>
                                        )}

                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>时限</Text>
                                            <Text className={styles.container_item_content}>
                                                {currentScript.timeLimit}分钟
                                            </Text>
                                        </View>
                                    </Block>
                                ) : null}
                                {currentScript?.type === ChatType.ANSWER ? (
                                    <Block>
                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>产品</Text>
                                            <Text className={styles.container_item_content}>
                                                {currentScript.product}
                                            </Text>
                                        </View>
                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>提问方式</Text>
                                            <Text className={styles.container_item_content}>
                                                {/* {currentScript.randomFlag ? '随机提问所有题目' : '按顺序提问所有问题'} */}
                                                {randomText}
                                            </Text>
                                        </View>
                                        <View
                                            className={classnames(styles.container_item, styles.container_horizontal)}
                                        >
                                            <Text className={styles.container_item_title}>时限</Text>
                                            <Text className={styles.container_item_content}>
                                                {currentScript?.timeLimit}分钟
                                            </Text>
                                        </View>
                                        <View className={classnames(styles.container_item, styles.container_vertical)}>
                                            <Text className={styles.container_item_title}>试题</Text>
                                            <View className={styles.container_item_content}>
                                                {currentScript?.question
                                                    ? currentScript?.question.map((item, index) => (
                                                          <View key={item.id}>
                                                              {index + 1}.{item.question}
                                                          </View>
                                                      ))
                                                    : null}
                                            </View>
                                        </View>
                                    </Block>
                                ) : null}
                            </View>
                        </View>
                    ) : (
                        <View className={styles.script_empty} onClick={() => setShowActionSheet(true)}>
                            <Image src={scriptBgImg} />
                            <View>选择脚本</View>
                        </View>
                    ))}
                {scriptTab === ScriptTabEnum.CUSTOM && (
                    <View className={styles.body}>
                        <View className={styles.customContainer}>
                            <View className={classnames(styles.customContainer_item, styles.customContainer_vertical)}>
                                <Text className={styles.customContainer_item_title} />
                                <Textarea
                                    className={styles.customContainer_item_content}
                                    style={`min-height:${pxTransform(160)};max-height:${pxTransform(400)}`}
                                    placeholderStyle='color:#CCCED7'
                                    autoHeight
                                    placeholder='例如:某医院神内科张主任，每月仅使用150支止痛宁，且主要用于急救，科室内有多个相似品种。最近张医生参加了神经学研讨会，对新进展产生兴趣。'
                                    maxlength={999}
                                    value={customScript.backdrop}
                                    onInput={(e) => {
                                        setCustomScript({ ...customScript, backdrop: e.detail.value });
                                    }}
                                />
                            </View>
                            {customScript.backdrop.length > 400 ? (
                                <View className={styles.showCount} style='color: #FF3734;'>
                                    文字太多啦 {customScript.backdrop.length}/400
                                </View>
                            ) : (
                                <View className={styles.showCount} style='color: #CCCED7;'>
                                    {customScript.backdrop.length}/400
                                </View>
                            )}
                            <View className={classnames(styles.customContainer_item, styles.customContainer_vertical)}>
                                <Text className={styles.customContainer_item_title}>目标</Text>
                                <Textarea
                                    className={styles.customContainer_item_content}
                                    style={`min-height:${pxTransform(80)};max-height:${pxTransform(120)}`}
                                    placeholderStyle='color:#CCCED7'
                                    autoHeight
                                    placeholder='例如:与张医生沟通，提高止痛宁在科室的应用率，探讨合作机会。'
                                    maxlength={999}
                                    value={customScript.goal}
                                    onInput={(e) => {
                                        setCustomScript({ ...customScript, goal: e.detail.value });
                                    }}
                                />
                            </View>
                            {customScript.goal.length > 100 ? (
                                <View className={styles.showCount} style='color: #FF3734;'>
                                    文字太多啦 {customScript.goal.length}/100
                                </View>
                            ) : (
                                <View className={styles.showCount} style='color: #CCCED7;'>
                                    {customScript.goal.length}/100
                                </View>
                            )}
                            <View
                                className={classnames(styles.customContainer_item, styles.customContainer_horizontal)}
                            >
                                <Text className={styles.customContainer_item_title}>产品</Text>
                                <Input
                                    className={styles.customContainer_item_content}
                                    placeholderStyle='color:#CCCED7'
                                    placeholder='请输入'
                                    value={customScript.product}
                                    onInput={(e) => {
                                        setCustomScript({ ...customScript, product: e.detail.value });
                                    }}
                                />
                            </View>
                            <View
                                className={classnames(styles.customContainer_item, styles.customContainer_horizontal)}
                            >
                                <Text className={styles.customContainer_item_title}>时间</Text>
                                <Text className={styles.customContainer_item_content}>{customScript.time}</Text>
                            </View>
                            <View
                                className={classnames(styles.customContainer_item, styles.customContainer_horizontal)}
                            >
                                <Text className={styles.customContainer_item_title}>地点</Text>
                                <Text className={styles.customContainer_item_content}>{customScript.location}</Text>
                            </View>
                            <View
                                className={classnames(styles.customContainer_item, styles.customContainer_horizontal)}
                            >
                                <Text className={styles.customContainer_item_title}>时限</Text>
                                <Text className={styles.customContainer_item_content}>
                                    {customScript.timeLimit}分钟
                                </Text>
                            </View>
                        </View>
                    </View>
                )}
                <View className={styles.script_button}>
                    {conflictShow && (
                        <View className={styles.conflict} onClick={() => setShowPopupConflict(true)}>
                            <Text className={styles.conflict_name}>角色冲突</Text>
                            <Text className={styles.conflict_tip}>当前E人和脚本不匹配</Text>
                            <Icon
                                className={styles.conflict_arrow}
                                name='arrow'
                                size={pxTransform(36)}
                                color='#ffffff'
                            />
                        </View>
                    )}
                    <View className={styles.script_button_horizontal}>
                        {from === 'scriptList' && (
                            <Block>
                                <Button
                                    style={{
                                        '--padding-md': pxTransform(28),
                                        '--button-normal-height': pxTransform(96)
                                    }}
                                    className={eman ? styles.btn_choose_eman : styles.btn_nochoose_eman}
                                    round
                                    block
                                    onClick={handleChooseEman}
                                >
                                    {eman ? (
                                        <View className={styles.change_eman}>
                                            <Icon
                                                name='sort'
                                                size={pxTransform(40)}
                                                style={{ transform: 'rotate(90deg)' }}
                                                color={eman ? '#4F66FF' : '#fff'}
                                            />
                                            <Text style={{ color: '#4F66FF' }}>切换E人</Text>
                                        </View>
                                    ) : (
                                        <Text style={{ color: '#fff' }}> 选择E人</Text>
                                    )}
                                </Button>
                                {eman ? (
                                    <Button
                                        style={{
                                            '--padding-md': pxTransform(28),
                                            '--button-normal-height': pxTransform(96)
                                        }}
                                        className={styles.btn_start_practice}
                                        round
                                        block
                                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                        loading={startPracticeLoading}
                                        onClick={handleStartPractice}
                                    >
                                        <Text>开始练习</Text>
                                    </Button>
                                ) : null}
                            </Block>
                        )}
                        {from !== 'scriptList' && (
                            <Button
                                style={{
                                    '--padding-md': pxTransform(28),
                                    '--button-normal-height': pxTransform(96)
                                }}
                                className={styles.btn_start_practice}
                                round
                                block
                                color={
                                    conflictShow ? '#CCCED7' : 'linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                }
                                disabled={
                                    (scriptTab === ScriptTabEnum.SCRIPT && !currentScript) ||
                                    (scriptTab === ScriptTabEnum.CUSTOM &&
                                        (!customScript.backdrop || !customScript.goal || !customScript.product)) ||
                                    conflictShow
                                }
                                loading={startPracticeLoading}
                                onClick={handleStartPractice}
                            >
                                <View
                                    style={{
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        gap: '5px'
                                    }}
                                >
                                    {currentScript && currentScript.lockFlag && (
                                        <Image src={IconLockWhite} width={pxTransform(32)} height={pxTransform(32)} />
                                    )}
                                    {from === 'checkpoint' ? (
                                        <View
                                            className={styles['challenge_text']}
                                            style={{ backgroundImage: `url(${StartChallenge})` }}
                                        />
                                    ) : (
                                        <Text style={{ display: from === 'checkpoint' ? 'none' : 'block' }}>
                                            开始练习
                                        </Text>
                                    )}
                                </View>
                            </Button>
                        )}
                    </View>
                </View>
            </View>
            <ActionSheet
                className={styles.actionSheet}
                show={showActionSheet}
                onClose={() => setShowActionSheet(false)}
                safeAreaInsetBottom={false}
                title={
                    <View onTouchStart={handleTouchStart} onTouchMove={handleTouchMove}>
                        脚本库
                    </View>
                }
            >
                <View className={classnames(styles.actionSheet_content, styles.actionSheet_content_scripts)}>
                    <View className={styles.scripts_type}>
                        {currentScripts.length !== 0 && (
                            <Block>
                                <View
                                    className={classnames(
                                        styles.scripts_type_item,
                                        currentScriptType === ChatType.ALL && styles.scripts_type_item_active
                                    )}
                                    onClick={() => setCurrentScriptType(ChatType.ALL)}
                                >
                                    全部
                                </View>
                                {scriptTypes.map((item) => {
                                    if (item != 3) {
                                        return (
                                            <View
                                                key={item}
                                                className={classnames(
                                                    styles.scripts_type_item,
                                                    currentScriptType === item && styles.scripts_type_item_active
                                                )}
                                                onClick={() => setCurrentScriptType(item)}
                                            >
                                                {item === ChatType.SKILL && '技巧类'}
                                                {item === ChatType.ANSWER && '答题类'}
                                            </View>
                                        );
                                    }
                                })}
                            </Block>
                        )}
                    </View>
                    <View
                        className={classnames(styles.scripts_list, {
                            [styles.scripts_list_android]: platform === 'android'
                        })}
                    >
                        {currentScripts.length !== 0 && (
                            <RadioGroup value={currentScriptId}>
                                <CellGroup
                                    border={false}
                                    style={{
                                        '--cell-group-inset-border-radius': pxTransform(8)
                                    }}
                                >
                                    {applicableScripts.map((item) => (
                                        <Cell
                                            inset
                                            style={{
                                                '--cell-background-color': '#F6F6F6',
                                                '--cell-font-size': pxTransform(28)
                                            }}
                                            key={item.id}
                                            title={item.name}
                                            clickable
                                            border={false}
                                            onClick={() => onSelectScript(item)}
                                            renderIcon={
                                                <Image
                                                    width={pxTransform(32)}
                                                    height={pxTransform(32)}
                                                    src={item.lockFlag ? IconLockYellow : scriptImg}
                                                />
                                            }
                                            renderRightIcon={<Radio name={item.id} checkedColor='#4F66FF' />}
                                        />
                                    ))}
                                    {applicableScripts.length === 0 && (
                                        <View className={classnames(styles.empty, styles.empty_script)}>
                                            <Image src={empty} className={styles.icon} />
                                            <Text>无适合此E人的脚本</Text>
                                        </View>
                                    )}
                                    {unapplicableScripts.length > 0 && (
                                        <View className={styles.split_line}>
                                            <Text className={styles.split_line_text}>
                                                以下脚本拜访对象和当前E人不匹配
                                            </Text>
                                        </View>
                                    )}
                                    {unapplicableScripts.map((item) => (
                                        <Cell
                                            inset
                                            style={{
                                                '--cell-background-color': '#F6F6F6',
                                                '--cell-font-size': pxTransform(28)
                                            }}
                                            key={item.id}
                                            title={item.name}
                                            clickable
                                            border={false}
                                            onClick={() => onSelectScript(item)}
                                            renderIcon={
                                                <Image
                                                    width={pxTransform(32)}
                                                    height={pxTransform(32)}
                                                    src={item.lockFlag ? IconLockYellow : scriptImg}
                                                />
                                            }
                                            renderRightIcon={<Radio name={item.id} checkedColor='#4F66FF' />}
                                        />
                                    ))}
                                </CellGroup>
                            </RadioGroup>
                        )}
                        {currentScripts.length === 0 && (
                            <View className={styles.empty}>
                                <Image src={empty} className={styles.icon} />
                                <Text>暂无脚本</Text>
                            </View>
                        )}
                    </View>
                </View>
            </ActionSheet>

            <ActionSheet
                show={showActionSheetUnend}
                onClose={() => setShowActionSheetUnend(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>检测到未结束的练习</View>
                    </View>
                }
                closeOnClickOverlay={false}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(0)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
            >
                <Button.Group direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={continueChat}
                    >
                        继续对话
                    </Button>
                    <Button block round color='#F6F6F6' style='color: #777777' onClick={newChat}>
                        结束并开启新对话
                    </Button>
                </Button.Group>
            </ActionSheet>
            <ActionSheet
                className={styles.popup_conflict}
                show={showPopupConflict}
                onClose={() => setShowPopupConflict(false)}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(0)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
                title='角色冲突'
                position='bottom'
            >
                <View className={styles.popup_conflict_content}>
                    <View className={styles.popup_conflict_tip}>
                        当前E人的角色和脚本拜访对象的不匹配，为了保证良好的对话效果建议更换E人或者脚本
                    </View>
                    <View className={styles.popup_conflict_info}>
                        <Text>E人角色：</Text>
                        <Text>{eman && emanJob(eman.occupation, eman.title, eman.department)}</Text>
                    </View>
                    <View className={styles.popup_conflict_info}>
                        <Text>脚本拜访对象角色：</Text>
                        <Text>
                            {currentScript?.visitObject}
                            {currentScript?.visitObject && currentScript?.department && '-'}
                            {currentScript?.department &&
                                currentScript?.department
                                    .split(',')
                                    .map((item) => item.split('-')[item.split('-').length - 1])
                                    .join('、')}
                        </Text>
                    </View>
                    <Button
                        className={classnames(styles.popup_conflict_btn, {
                            [styles.popup_conflict_btn_ios]: platform === 'ios'
                        })}
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={() => setShowPopupConflict(false)}
                    >
                        我知道了
                    </Button>
                </View>
            </ActionSheet>
            <ActionSheet
                className={styles.popup_unlock}
                show={showPopupUnlock}
                onClose={() => setShowPopupUnlock(false)}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(0)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
                title='未解锁该脚本'
                position='bottom'
            >
                <View className={styles.popup_unlock_content}>
                    <View className={styles.popup_unlock_tip}>请完成以下任务以解锁脚本</View>
                    <View className={styles.popup_unlock_list}>
                        {taskCondition &&
                            taskCondition.map((task: ScriptUnlockConditionVo) => (
                                <View className={styles.popup_unlock_task} key={task.name}>
                                    {task.unlockCondition.map((condition: UnlockCondition) => (
                                        <Block key={condition.scriptId}>
                                            <View className={styles.popup_unlock_item}>
                                                <Text className={styles.popup_unlock_name}>任务脚本</Text>
                                                <Text className={styles.popup_unlock_value}>
                                                    {condition.scriptName}
                                                </Text>
                                            </View>
                                            <View className={styles.popup_unlock_item}>
                                                <Text className={styles.popup_unlock_name}>任务要求</Text>
                                                <Text className={styles.popup_unlock_value}>
                                                    获得{condition.needNum}次{condition.score}分及以上
                                                </Text>
                                            </View>
                                            <View className={styles.popup_unlock_item}>
                                                <Text className={styles.popup_unlock_name}>任务进度</Text>
                                                <View className={styles.popup_unlock_value}>
                                                    <Text>
                                                        {condition.currentNum > condition.needNum
                                                            ? condition.needNum
                                                            : condition.currentNum}
                                                        /{condition.needNum}
                                                    </Text>
                                                    <Button
                                                        style={{
                                                            '--padding-md': pxTransform(24),
                                                            '--button-normal-height': pxTransform(48),
                                                            marginLeft: pxTransform(20),
                                                            fontSize: pxTransform(24)
                                                        }}
                                                        round
                                                        color={
                                                            condition.currentNum >= condition.needNum
                                                                ? '#CCCED7'
                                                                : 'linear-gradient(270deg, #6741FE 0%, #3C83FF 100%)'
                                                        }
                                                        onClick={() => {
                                                            handleTaskComplete(condition);
                                                        }}
                                                    >
                                                        {condition.currentNum >= condition.needNum
                                                            ? '已完成'
                                                            : '去完成'}
                                                    </Button>
                                                </View>
                                            </View>
                                        </Block>
                                    ))}
                                </View>
                            ))}
                    </View>

                    <Button
                        className={classnames(styles.popup_unlock_btn, {
                            [styles.popup_unlock__btn_ios]: platform === 'ios'
                        })}
                        style={{ color: '#777777' }}
                        block
                        round
                        color='#F6F6F6'
                        onClick={() => setShowPopupUnlock(false)}
                    >
                        我知道了
                    </Button>
                </View>
            </ActionSheet>
            {from === 'checkpoint' && (
                <EmanSelect
                    show={showEmanSelect}
                    scriptId={scriptId}
                    onClose={() => setShowEmanSelect(false)}
                    onChoose={onEmanChoose}
                />
            )}

            {from === 'scriptList' && (
                <ScriptEmanSelect
                    show={showScriptListEmanSelect}
                    eman={eman}
                    scriptId={scriptId}
                    onClose={() => setShowScriptListEmanSelect(false)}
                    onChoose={onEmanChoose}
                />
            )}
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='A' />
            <Dialog_ />
            <Toast_ style={{ textAlign: 'center' }} />
        </Page>
    );
};

export default App;
