page {
    position: relative;
    box-sizing: border-box;
    padding: 0 32px;
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%), linear-gradient(158deg, #CFF7F4 0%, #D1EBFF 12%, #FFF 35%);

    --nav-bar-background-color: transparent;
}
:global {
    .van-button__text {
        display: block;
        width: 100%;
    }
}

.user_info {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 36px;
    margin-bottom: 40px;
}
.avatar_button {
    margin-right: 24px;
    margin-left: 32px;
    padding: 0;
    background-color: transparent;
    &::after {
        display: none;
    }
}
.avatar_image {
    display: block;
    width: 108px;
    height: 108px;
    margin-right: 24px;
    border-radius: 100%;
}
.placeholder {
    color: #3c3f3e;
    font-weight: 500;
    font-size: 36px;
}
.nickname {
    font-weight: bold;
    font-size: 36px;
}
.phoneButton {
    margin: 0;
    padding: 0;
    color: #3c3f3e;
    font-weight: bold;
    font-size: 36px;
    background: none;
    &::after {
        border: none;
    }
}
.munuBox {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    width: 100%;
    height: 117px;
    padding: 0 32px;
    background-color: #fff;
    border-radius: 24px;
    .left {
        display: flex;
        flex: 1;
        align-items: center;
    }
    .menuText {
        color: #272c47;
        font-weight: bold;
        font-size: 32px;
        font-family: 'PingFang SC';
    }
}
.icon {
    width: 36px;
    height: 36px;
    margin-right: 16px;
}
.right {
    width: 36px;
    height: 36px;
}
.munuBoxs {
    display: flex;
    flex-direction: column;
    margin-top: 24px;
    border-radius: 24px;
    overflow: hidden;

    .munuBox {
        position: relative;
        box-sizing: border-box;
        width: 100%;
        height: 120px;
        border-radius: 0;
        &::before {
            position: absolute;
            top: 0;
            left: 32px;
            width: calc(100% - 64px);
            height: 1px;
            background-color: #eeeff3;
            content: '';
        }
        &:first-child {
            &::before {
                display: none;
            }
        }
    }
}
.shareButton {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 117px;
    margin: 0;
    padding: 0;
    .van-button__text {
        display: block;
    }
}
.shareButton::after {
    border: none;
}

.myEman {
    margin-bottom: 24px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    .menuText {
        color: #fff;
    }
}
.emanRight {
    display: flex;
    align-items: center;
    height: 70px;
}
.emanAvatar {
    width: 70px;
    height: 70px;
    margin-right: 16px;
    border-radius: 35px;
}

.points {
    margin-right: 16px;
    font-size: 32px;
}
