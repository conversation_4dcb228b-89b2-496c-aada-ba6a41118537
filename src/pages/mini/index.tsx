import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import { EventKey } from '@/constants/eventKey';
import { StorageEnvKey } from '@/constants/storage';
import { useLogin } from '@/hooks';
import { getMyEMan } from '@/services/eman';
import { getPointsStat } from '@/services/points';
import { getDataBoardOpen } from '@/services/user';
import type { UserInfo } from '@/types/common';
import type { EManVo } from '@/types/eman';
import { Button, NavBar } from '@antmjs/vantui';
import { Block, Image, View } from '@tarojs/components';
import Taro, { useDidShow, useLoad, useShareAppMessage, useUnload } from '@tarojs/taro';
import classNames from 'classnames';
import { useCallback, useState } from 'react';
import styles from './index.less';
import { HomePath } from "@/constants/homePath";

const iconRightWhite = `${config.cdnPrefix}icon_right_white.svg`;
const iconData = `${config.cdnPrefix}mine/icon_data.svg`;
const mineData = `${config.cdnPrefix}mine/mine_data.svg`;
const iconPoints = `${config.cdnPrefix}icon_points.svg`;
const iconCheckpoint = `${config.cdnPrefix}mine/checkpoint.svg`;
const iconMeWhite = `${config.cdnPrefix}mine/me_white.svg`;
const right = `${config.cdnPrefix}mine/right.png`;
const setting = `${config.cdnPrefix}mine/setting.png`;
const share = `${config.cdnPrefix}mine/share.png`;
const shareImage = `${config.cdnPrefix}image.png`;
const shareImageky = `${config.cdnPrefix}shared.png`;
const App = () => {
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;

    const [userInfo, setUserInfo] = useState<UserInfo>({} as UserInfo);

    const [eManList, setEManList] = useState<EManVo[]>([]);
    const [isEnterprise, setIsEnterprise] = useState<number>();
    const [monthPoints, setMonthPoints] = useState<number>(0);
    const [kanbanShow, setKanbanShow] = useState<boolean>(false);
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [token, setToken] = useStorage<string>(StorageEnvKey.TOKEN);

    const _getEManList = async () => {
        const res = await getMyEMan();
        if (res.data.code === 200) {
            const { data } = res;
            setEManList(data.data.records || []);
        }
    };
    const getPointsStatic = async () => {
        try {
            const res = await getPointsStat();
            setMonthPoints(res.data.data.monthPoints);
        } catch (error) {
            setMonthPoints(0);
        }
    };
    const getKanbanShow = async () => {
        const res = await getDataBoardOpen();
        setKanbanShow(res.data.data);
    };

    const initData = () => {
        _getEManList();
        getPointsStatic();
        getKanbanShow();
    };
    useDidShow(() => {
        // 0.1版本昵称是手机号
        const user = Storage.get(StorageEnvKey.USERINFO) || {};
        setUserInfo(user);
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);
            setToken(token);
            if (token) {
                // 获取E人列表
                initData();
            }
        } else {
            if (user.phoneNumber) {
                // 获取E人列表
                initData();
            }
        }
    });
    // Taro.eventCenter.on(EventKey.LOGIN_SUCCESS, (userInfo: any) => {
    //     const e = Storage.get(StorageKey.IS_ENTERPRISE);
    //     if (e) {
    //         setIsEnterprise(e);
    //     } else {
    //         setIsEnterprise(0);
    //     }
    //     setUserInfo(userInfo);
    //     _getEManList();
    // });
    // Taro.eventCenter.on(EventKey.LOGIN_FAIL, () => {
    //     Taro.showToast({
    //         icon: 'none',
    //         title: '登录失败，请重试'
    //     });
    // });
    const { login } = useLogin({
        onSuccess: (userInfo) => {
            const token = Storage.get(StorageEnvKey.TOKEN);
            if (token) {
                setToken(token);
            }
            Taro.showToast({
                icon: 'none',
                title: '登录成功'
            });
            setUserInfo(userInfo);
            console.log(token, userInfo);
            initData();
        },
        onError: (error: any) => {
            console.log(error);
        }
    });

    // const linkToBag = () => {
    //     if (userInfo.phoneNumber) {
    //         Taro.navigateTo({ url: '/pages/bag/index/index' });
    //     } else {
    //         getPhoneNumber();
    //     }
    // };

    const handleLogin = useCallback(
        (cb: () => void) => {
            if (isWework === '1') {
                const token = Storage.get(StorageEnvKey.TOKEN);

                if (token) {
                    cb();
                } else {
                    login();
                }
            } else {
                if (userInfo.phoneNumber) {
                    cb();
                } else {
                    login();
                }
            }
        },
        [isWework, userInfo]
    );
    const linkToSetting = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: '/pages/settings/setting/index' });
        });
    };

    const linkToTest = () => {
        Taro.navigateTo({ url: '/pages/test/test' });
    };

    const gotoUpdateUser = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: '/pages/settings/user/index' });
        });
    };

    const linkToEmanCreate = () => {
        handleLogin(() => {
            const url =
                eManList.length > 0
                    ? `/pages/eman/home/<USER>
                    : '/pages/eman/characterStepOne/index?type=create';
            Taro.navigateTo({ url });
        });
    };

    const navigateToPoints = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: '/pages/points/center/index' });
        });
    };

    const linkToMyDataBoard = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: `/pages/data/detail/index?id=${userInfo.id}` });
        });
    };

    const linkToDataBoard = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: '/pages/data/board/index' });
        });
    };

    const linkToCheckpoint = () => {
        handleLogin(() => {
            Taro.navigateTo({ url: '/pages/activity/checkpoint/list/index' });
        });
    };

    useShareAppMessage(() => {
        const accountInfo = Taro.getAccountInfoSync();
        const appid = accountInfo.miniProgram.appId;
        let imageUrl = '';
        if (appid === AppIdConsts.qnq) {
            imageUrl = shareImageky;
        } else {
            imageUrl = shareImage;
        }
        const homePath = Storage.get(StorageEnvKey.HOME_PATH);
        return {
            title: '快来看看吧，与TA沟通原来还可以这样',
            path: homePath ? homePath : HomePath.DIALOG,
            imageUrl
        };
    });
    useLoad(() => {
        const e = Storage.get(StorageEnvKey.IS_ENTERPRISE);
        if (e) {
            setIsEnterprise(e);
        } else {
            setIsEnterprise(0);
        }
        Taro.eventCenter.on(EventKey.LOGOUT, () => {
            const user = Storage.get(StorageEnvKey.USERINFO) || {};
            setToken('');
            setUserInfo(user);
        });
    });
    useUnload(() => {
        // Taro.eventCenter.off(EventKey.LOGIN_SUCCESS);
        // Taro.eventCenter.off(EventKey.LOGIN_FAIL);
        Taro.eventCenter.off(EventKey.LOGOUT);
    });
    return (
        <Page className={styles.page}>
            <NavBar title='' safeAreaInsetTop />

            <View className={styles.user_info} onClick={gotoUpdateUser}>
                <Image src={userInfo?.avatar || AvatarDefault} className={styles.avatar_image} mode='aspectFill' />
                <View className={styles.nickname}>
                    {isWework === '1'
                        ? token
                            ? userInfo.name
                            : '未登录'
                        : userInfo.phoneNumber
                        ? userInfo.name
                        : '未登录'}
                </View>
            </View>

            {isEnterprise === 0 && (
                <View
                    className={classNames(styles.munuBox, styles.myEman)}
                    style={{ backgroundImage: `url(${config.cdnPrefix}mine/eman_bg.png)` }}
                    onClick={linkToEmanCreate}
                >
                    <View className={styles.left}>
                        <Image src={iconMeWhite} className={styles.icon} mode='aspectFit' />
                        <View className={styles.menuText}>我的E人</View>
                    </View>
                    <View className={styles.emanRight}>
                        {(isWework === '1' ? token : userInfo.phoneNumber) && eManList.length > 0 && (
                            <Image src={eManList[0].avatar} className={styles.emanAvatar} mode='aspectFill' />
                        )}

                        <Image src={iconRightWhite} className={styles.right} mode='aspectFit' />
                    </View>
                </View>
            )}

            {isEnterprise === 1 && (
                <view>
                    <View className={styles.munuBoxs}>
                        <Block>
                            {kanbanShow && (
                                <View className={styles.munuBox} onClick={linkToDataBoard}>
                                    <View className={styles.left}>
                                        <Image src={iconData} className={styles.icon} mode='aspectFit' />
                                        <View className={styles.menuText}>数据看板</View>
                                    </View>
                                    <Image src={right} className={styles.right} mode='aspectFit' />
                                </View>
                            )}
                            <View className={styles.munuBox} onClick={linkToMyDataBoard}>
                                <View className={styles.left}>
                                    <Image src={mineData} className={styles.icon} mode='aspectFit' />
                                    <View className={styles.menuText}>我的练习数据</View>
                                </View>
                                <Image src={right} className={styles.right} mode='aspectFit' />
                            </View>
                        </Block>
                    </View>
                    <View className={styles.munuBoxs}>
                        <View className={styles.munuBox} onClick={navigateToPoints}>
                            <View className={styles.left}>
                                <Image src={iconPoints} className={styles.icon} mode='aspectFit' />
                                <View className={styles.menuText}>本月积分</View>
                            </View>
                            <View
                                className={styles.points}
                                style={{
                                    visibility: (isWework === '1' ? token : userInfo.phoneNumber) ? 'visible' : 'hidden'
                                }}
                            >
                                {monthPoints}
                            </View>
                            <Image src={right} className={styles.right} mode='aspectFit' />
                        </View>
                        <View className={styles.munuBox} onClick={linkToCheckpoint}>
                            <View className={styles.left}>
                                <Image src={iconCheckpoint} className={styles.icon} mode='aspectFit' />
                                <View className={styles.menuText}>闯关活动</View>
                            </View>
                            <Image src={right} className={styles.right} mode='aspectFit' />
                        </View>
                    </View>
                </view>
            )}
            {/* <View className={styles.munuBox} onClick={linkToBag}>
                <View className={styles.left}>
                    <Image src={bag} className={styles.icon} mode='aspectFit' />
                    <View className={styles.menuText}>背包</View>
                </View>
                <Image src={right} className={styles.right} mode='aspectFit' />
            </View> */}

            <View className={styles.munuBoxs}>
                <Button className={styles.shareButton} openType='share'>
                    <View className={styles.munuBox}>
                        <View className={styles.left}>
                            <Image src={share} className={styles.icon} mode='aspectFit' />
                            <View className={styles.menuText}>推荐给好友</View>
                        </View>
                        <Image src={right} className={styles.right} mode='aspectFit' />
                    </View>
                </Button>
                <View className={styles.munuBox} onClick={linkToSetting}>
                    <View className={styles.left}>
                        <Image src={setting} className={styles.icon} mode='aspectFit' />
                        <View className={styles.menuText}>设置</View>
                    </View>
                    <Image src={right} className={styles.right} mode='aspectFit' />
                </View>
                {/* <View className={styles.munuBox} onClick={linkToTest}>
                    <View className={styles.left}>
                        <Image src={setting} className={styles.icon} mode='aspectFit' />
                        <View className={styles.menuText}>测试</View>
                    </View>
                    <Image src={right} className={styles.right} mode='aspectFit' />
                </View> */}
            </View>
        </Page>
    );
};

export default App;
