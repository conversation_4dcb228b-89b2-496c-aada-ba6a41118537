import { useRequest } from '@/common';
import { Page } from '@/components';
import {
    deleteCertificationVerify,
    getAuditFailReason,
    getCertificationById,
    getDepartment,
    getProfessionalTitle,
    postCertification,
    putCertification
} from '@/services/certification';
import { upload } from '@/services/common';
import { getMyEManDetail } from '@/services/eman';
import type { certificationVo } from '@/types/chat';
import { ActionSheet, Checkbox, Dialog, Form, FormItem, Icon, NavBar, Toast, Uploader } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Input, Text, Textarea, View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import FormCascader from '../components/formCascader';
import styles from './index.less';

// E人上架 status 状态，1-下架，2-审核中，3-上架，4-审核失败
// 职业认证 status 状态，1-未审核， 2-审核中， 3-审核成功， 4-审核失败

const Dialog_ = Dialog.createOnlyDialog();

const App = () => {
    const { params } = useRouter<{ emanId: string; certificationId: string; eid: string }>();
    const { emanId, certificationId, eid } = params;
    const [title] = useState('职业认证');
    const formIt = Form.useForm();
    const initialValues = {
        name: '',
        department: '',
        title: '',
        organization: '',
        file: []
    };
    const [detail, setdetail] = useState<certificationVo>({
        name: '',
        department: '',
        title: '',
        organization: '',
        file: []
    });
    const [detailStyle, setdetailStyle] = useState(false);
    const [showActionSheet, setShowActionSheet] = useState(false);
    const [showActionSheet1, setShowActionSheet1] = useState(false);
    const [formTips, setformTips] = useState(false);
    const [eStatus, setEStatus] = useState(-1);
    // const [, forceUpdate] = useState(-1);

    const { data: optionsTitle } = useRequest(getProfessionalTitle);
    const { data: optionsDepartment } = useRequest(getDepartment);
    const firstLoad = useRef(true);

    const getDetail = () => {
        getMyEManDetail(emanId).then((response) => {
            const res = response.data;
            if (res.code === 200) {
                setEStatus(res.data.status);
            }
        });
        if (certificationId) {
            getCertificationById(certificationId).then((response) => {
                const res = response.data;
                if (res.code === 200) {
                    const formData = {
                        ...res.data,
                        file: res.data.file.map((item: any) => {
                            return {
                                type: 'image',
                                url: item
                            };
                        })
                    };
                    formIt.setFields(formData);
                    // Taro.nextTick(() => {
                    setTimeout(() => {
                        formIt.setFieldsValue(
                            'file',
                            res.data.file.map((item: any) => {
                                return {
                                    type: 'image',
                                    url: item
                                };
                            })
                        );
                        console.log('getDetail-formIt', formIt.getFieldsValue());
                    }, 300);
                    // });
                    // formData.reason = '测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试测试';
                    setdetail(formData);
                    // console.log('firstLoad.current', firstLoad.current);
                    if ([1, 2, 3, 4].includes(formData?.status) && firstLoad.current) {
                        setdetailStyle(true);
                    }
                    firstLoad.current = false;
                    console.log('getDetail-formData', formData);
                    if (formData.status === 4) {
                        getAuditFailReason(certificationId).then((res) => {
                            if (res.data.code === 200) {
                                formData.reason = res.data.data;
                                setdetail(formData);
                            }
                        });
                    }
                }
            });
        }
    };

    const handleSubmit = () => {
        formIt.validateFields((errorMessage, fieldValues) => {
            console.log(errorMessage, fieldValues);
            if (errorMessage && errorMessage.length) {
                return console.info('errorMessage', errorMessage);
            }
            const formData = {
                ...fieldValues,
                file: fieldValues.file.map((item: any) => {
                    return item.url;
                })
            };
            if (certificationId) {
                putCertification({ ...formData, id: certificationId }).then((res) => {
                    if (res.data.code === 200) {
                        Taro.redirectTo({ url: `/pages/eman/certificationResult/index?eid=${eid}` });
                    } else {
                        Taro.showToast({ title: '提交失败', icon: 'none' });
                    }
                });
            } else {
                postCertification(formData).then((res) => {
                    if (res.data.code === 200) {
                        Taro.redirectTo({ url: `/pages/eman/certificationResult/index?eid=${eid}` });
                    } else {
                        Taro.showToast({ title: '提交失败', icon: 'none' });
                    }
                });
            }
        });
    };

    const valueFormatUpload = async (event, formName, instance) => {
        // todo 限制格式和大小
        Toast.loading('上传中...');
        const { file } = event.detail;
        // console.log('valueFormatUpload-file', file);
        console.log(event, formName);
        let fileList = instance.getFieldValue(formName) || [];
        // let fileListLen = fileList.length;
        for (const item of file) {
            // console.log('valueFormatUpload-item', item);
            const res = await upload(item.url);
            const url = JSON.parse(res).data;
            item.url = url;
            fileList = fileList.concat(item);
            // fileList.splice(fileListLen, 0, item);
            // fileListLen++;
        }
        Toast.clear();
        // console.log('valueFormatUpload-fileList', fileList);
        return fileList;
    };

    const deleteFile = (event) => {
        const { index, fileList } = event.detail;
        // const fileList1 = formIt.getFieldValue('file');
        // console.log('fileList', fileList);
        // console.log('fileList1', fileList1);
        fileList.splice(index, 1);
        formIt.setFieldsValue('file', fileList);
    };

    const ruleName = {
        rule: (values: string, callback: (errMess: string) => void) => {
            const reg = /^[\u4e00-\u9fa5]+$/;
            if (!reg.test(values)) {
                callback(`不能使用字母、数字、特殊符号`);
            }
        }
    };

    const handleChangeStatus = () => {
        if ([2, 3].includes(eStatus)) {
            Taro.showToast({ title: '请先下架E人或撤销E人审核', icon: 'none' });
        } else {
            setShowActionSheet(true);
        }
    };

    const handleShowReason = () => {
        console.log('handleShowReason');
        Dialog_.alert({
            title: '审核未通过',
            message: detail.reason,
            confirmButtonText: '我知道了',
            confirmButtonColor: '#4F66FF'
        }).then(() => {});
    };

    const handleWithdrawal = () => {
        deleteCertificationVerify(certificationId).then((res) => {
            if (res.data.code === 200) {
                Taro.showToast({ title: '撤销成功', icon: 'none' });
                setShowActionSheet(false);
                // const detailTemp = { ...detail, status: 1 };
                // setdetail(detailTemp);
                getDetail();
            }
        });
    };

    const handleChangeStyle = () => {
        if ([2, 3].includes(eStatus)) {
            Taro.showToast({ title: '请先下架E人或撤销E人审核', icon: 'none' });
        } else {
            setdetailStyle(false);
            getDetail();
        }
    };

    const handleCilckGoBack = () => {
        let bool = false;
        Object.keys(initialValues).forEach((key) => {
            if (
                JSON.stringify(formIt.getFieldValue(key)) !== JSON.stringify(detail[key]) &&
                !(JSON.stringify(formIt.getFieldValue(key)) === undefined && JSON.stringify(detail[key] === []))
            ) {
                console.log('key', key, formIt.getFieldValue(key), detail[key]);
                bool = true;
                return;
            }
        });
        if (bool) {
            // 修改过但未保存
            setShowActionSheet1(true);
        } else {
            Taro.navigateBack();
        }
    };

    useEffect(() => {
        getDetail();
    }, []);

    // 表单全填且打勾后enable，切换后就先不打勾。但是formIt怎么监听，也没有blur事件。
    // useEffect(() => {
    //     Object.keys(initialValues).forEach((key) => {
    //         console.log('useEffect-formIt', JSON.stringify(formIt.getFieldValue(key)));
    //     });
    // }, [formIt.getFieldValue('organization')]); // , detail?.status, detailStyle

    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title={title}
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => handleCilckGoBack()}
            />
            {/* E人上架 status 状态，1-下架，2-审核中，3-上架，4-审核失败 */}
            {/* 职业认证 status 状态，1-未审核， 2-审核中， 3-审核成功， 4-审核失败 */}
            {[4].includes(detail?.status) && (
                <View className={styles.page_tips_warning}>
                    <Icon name='warning-o' size={pxTransform(32)} color='#FF8F28' className='icon' />
                    <View className={styles.t1}>您的职业认证审核未通过，点击</View>
                    <View className={styles.t2} onClick={handleShowReason}>
                        查看详情&gt;&gt;
                    </View>
                </View>
            )}
            {/* eStatus-{eStatus} */}
            {[2, 3].includes(eStatus) && (
                <View className={styles.page_tips_info}>
                    <Icon name='info-o' size={pxTransform(32)} color='#4C82FB' className='icon' />
                    <View className={styles.t1}>如需修改，请先下架E人或撤销E人审核</View>
                </View>
            )}
            <Dialog_ />
            <View className={styles.page_content}>
                {detailStyle ? (
                    <View className={styles.formDetail}>
                        <Form initialValues={initialValues} form={formIt}>
                            <FormItem
                                label='姓名'
                                name='name'
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Input disabled className={styles.input} />
                            </FormItem>
                            <FormItem
                                label='科室'
                                name='department'
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Text className={styles.input}>
                                    {
                                        formIt.getFieldValue('department').split('-')[
                                            formIt.getFieldValue('department').split('-').length - 1
                                        ]
                                    }
                                </Text>
                            </FormItem>
                            <FormItem
                                label='职称'
                                name='title'
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Text className={styles.input}>
                                    {
                                        formIt.getFieldValue('title').split('-')[
                                            formIt.getFieldValue('title').split('-').length - 1
                                        ]
                                    }
                                </Text>
                            </FormItem>
                            <FormItem
                                label='第一执业医院'
                                layout='vertical'
                                name='organization'
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Textarea disabled style={`min-height:${pxTransform(80)}`} autoHeight />
                            </FormItem>
                            <FormItem
                                layout='vertical'
                                label='资质证明'
                                name='file'
                                required
                                requiredIcon
                                trigger='onAfterRead'
                                validateTrigger='onAfterRead'
                                className={styles.form_item}
                                labelClassName={styles.label}
                                valueKey='fileList'
                                valueFormat={valueFormatUpload}
                            >
                                <Uploader name='file' maxCount={0} deletable={false} />
                            </FormItem>
                        </Form>
                    </View>
                ) : (
                    <View className={styles.form}>
                        <Form
                            initialValues={initialValues}
                            form={formIt}
                            onFinish={(errs, res) => console.info(errs, res)}
                        >
                            <FormItem
                                label='姓名'
                                name='name'
                                required
                                requiredIcon
                                rules={ruleName}
                                trigger='onInput'
                                validateTrigger='onInput'
                                valueFormat={(e) => e.detail.value}
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Input
                                    maxlength={15}
                                    className={styles.input}
                                    placeholder='请输入'
                                    placeholderClass={styles.placeholder}
                                />
                            </FormItem>
                            <FormItem
                                label='科室'
                                name='department'
                                valueKey='value'
                                required
                                requiredIcon
                                trigger='onConfirm'
                                validateTrigger='onBlur'
                                valueFormat={(e) => e}
                                renderRight={
                                    <Icon
                                        name='arrow'
                                        color='#C9C9C9'
                                        size={pxTransform(36)}
                                        className={styles.icon_right}
                                    />
                                }
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                {/* todo 回显 */}
                                <FormCascader
                                    title='科室'
                                    options={optionsDepartment?.map((item) => {
                                        return {
                                            text: item.name,
                                            children: item.subName
                                        };
                                    })}
                                />
                                {/* formIt.setFieldsValue('department', '') */}
                            </FormItem>
                            <FormItem
                                label='职称'
                                name='title'
                                valueKey='value'
                                required
                                requiredIcon
                                trigger='onConfirm'
                                validateTrigger='onBlur'
                                valueFormat={(e) => e}
                                renderRight={
                                    <Icon
                                        name='arrow'
                                        color='#C9C9C9'
                                        size={pxTransform(36)}
                                        className={styles.icon_right}
                                    />
                                }
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <FormCascader
                                    title='职称'
                                    options={optionsTitle?.map((item) => {
                                        return {
                                            text: item.title,
                                            children: item.subTitle
                                        };
                                    })}
                                />
                            </FormItem>
                            <FormItem
                                layout='vertical'
                                label='第一执业医院'
                                name='organization'
                                required
                                requiredIcon
                                trigger='onInput'
                                validateTrigger='onBlur'
                                valueFormat={(e) => e.detail.value}
                                className={styles.form_item}
                                labelClassName={styles.label}
                            >
                                <Textarea
                                    maxlength={64}
                                    style={`min-height:${pxTransform(80)}`}
                                    autoHeight
                                    placeholder='请输入'
                                    placeholderClass={styles.placeholder}
                                />
                            </FormItem>
                            <View className={styles.form_item_wrap}>
                                <FormItem
                                    layout='vertical'
                                    label='资质证明'
                                    name='file'
                                    required
                                    requiredIcon
                                    trigger='onAfterRead'
                                    validateTrigger='onAfterRead'
                                    className={styles.form_item}
                                    labelClassName={styles.label}
                                    valueKey='fileList'
                                    valueFormat={valueFormatUpload}
                                >
                                    <Uploader name='file' multiple maxCount={9} deletable onDelete={deleteFile} />
                                </FormItem>
                                <View className={styles.file_tips}>
                                    请您上传工牌、执业证、资格证、专业技术资格证（职称证） 等照片或截图。注意需要包含
                                    <Text>姓名</Text>和<Text>第一执业医院</Text>信息。 （至少1项，最多9张）
                                </View>
                            </View>
                            <View className={styles.form_tips}>
                                <Checkbox value={formTips} onChange={() => setformTips(!formTips)} />
                                <View>
                                    我承诺以上信息为本人真实有效信息，并同意
                                    <Text
                                        onClick={() =>
                                            Taro.navigateTo({ url: `/pages/eman/certificationAgreement/index` })
                                        }
                                    >
                                        个人职业资质认证服务协议
                                    </Text>
                                    。
                                </View>
                            </View>
                        </Form>
                    </View>
                )}
            </View>
            <View className={styles.bottom}>
                {/* E人上架 status 状态，1-下架，2-审核中，3-上架，4-审核失败 */}
                {/* 职业认证 status 状态，1-未审核， 2-审核中， 3-审核成功， 4-审核失败 */}
                {/* detailStyle记录是否点击过修改按钮，变成表单样式，不改变status状态值 */}
                {detailStyle && [2].includes(detail?.status) ? (
                    <Button
                        onClick={handleChangeStatus}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        审核中
                    </Button>
                ) : detailStyle && [1, 3, 4].includes(detail?.status) ? (
                    <View className={styles.btn_group}>
                        <Button
                            onClick={handleChangeStyle}
                            style={{
                                '--padding-md': pxTransform(28),
                                '--button-normal-height': pxTransform(96),
                                color: '#777777'
                            }}
                            round
                            block
                            color='#FFFFFF'
                        >
                            修改
                        </Button>
                        {/* 未审核可以直接再次提交审核 */}
                        {[1].includes(detail?.status) && (
                            <Button
                                onClick={handleSubmit}
                                style={{
                                    '--padding-md': pxTransform(28),
                                    '--button-normal-height': pxTransform(96)
                                }}
                                round
                                block
                                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                            >
                                提交审核
                            </Button>
                        )}
                    </View>
                ) : (
                    <Button
                        disabled={!formTips}
                        onClick={handleSubmit}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        提交
                    </Button>
                )}
            </View>
            <ActionSheet
                show={showActionSheet}
                onClose={() => setShowActionSheet(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>确定撤销审核吗？</View>
                    </View>
                }
                closeOnClickOverlay={false}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(48)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
            >
                <Button.Group direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={handleWithdrawal}
                    >
                        确定
                    </Button>
                    <Button
                        block
                        round
                        color='#F6F6F6'
                        style='color: #777777'
                        onClick={() => setShowActionSheet(false)}
                    >
                        取消
                    </Button>
                </Button.Group>
            </ActionSheet>
            <ActionSheet
                show={showActionSheet1}
                onClose={() => setShowActionSheet1(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>操作未保存，确定退出吗？</View>
                    </View>
                }
                closeOnClickOverlay={false}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(48)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
            >
                <Button.Group direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={() => Taro.navigateBack()}
                    >
                        确定
                    </Button>
                    <Button
                        block
                        round
                        color='#F6F6F6'
                        style='color: #777777'
                        onClick={() => setShowActionSheet1(false)}
                    >
                        取消
                    </Button>
                </Button.Group>
            </ActionSheet>
        </Page>
    );
};

export default App;
