page {
    background: #f3f3f3;

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
}
:global {
    .vant-form-required-box {
        display: none;
    }
    .vant-form-message {
        text-align: right;
    }
    .vant-form-formItem-controll {
        margin-right: 12px!important;
    }
    .vant-form-formItem-controll-item{
        flex-direction: row;
        // justify-content: flex-end;
        background-color: #ffffff;
    }
    .van-checkbox__label {
        color: #67686f;
    }
}
.page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100vh;
    padding-bottom: 100px;
}
.page_tips_warning {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    box-sizing: border-box;
    width: 686px;
    height: 80px;
    margin: 28px auto;
    padding: 20px 30px;
    font-size: 28px;
    background: #fffbe8;
    border-radius: 32px;
    .t1 {
        margin-left: 15px;
    }
    .t2 {
        color: #ff8f28;
    }
}
.page_tips_info {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    box-sizing: border-box;
    width: 686px;
    height: 80px;
    margin: 28px auto;
    padding: 20px 30px;
    font-size: 28px;
    background: #e6eeff;
    border-radius: 32px;
    .t1 {
        margin-left: 15px;
    }
}
.page_content {
    flex: 1;
    padding-bottom: calc(96px + 30px + constant(safe-area-inset-bottom));
    padding-bottom: calc(96px + 30px + env(safe-area-inset-bottom));
    overflow-y: auto;
}
.bottom {
    position: fixed;
    right: 0;
    bottom: calc(30px + constant(safe-area-inset-bottom));
    bottom: calc(30px + env(safe-area-inset-bottom));
    left: 0;
    padding: 0 36px;
    .btn_group {
        display: flex;
        gap: 24px;
        box-sizing: border-box;
        width: 100%;
    }
}
.step {
    color: #9597a0;
    font-size: 24px;
    text-align: center;
}

.avatar {
    display: block;
    width: 230px;
    height: 230px;
    margin: 56px auto;
}
.icon_right {
    width: 36px;
    height: 36px;
    padding-left: 10px;
}
.formDetail {
    margin-right: 32px;
    margin-left: 32px;
    background: #fff;
    border-radius: 24px;
    :global {
        .vant-form-formItem-wrapper {
            margin: 0;
            background-color: transparent;
        }
        textarea {
            color: #9597a0;
        }
    }
    .input {
        flex: 1;
        color: #9597a0;
        text-align: right;
    }
}
.form {
    margin-right: 32px;
    margin-left: 32px;
    :global {
        .vant-form-formItem-wrapper {
            margin-bottom: 24px;
            border-radius: 24px;
        }
    }
}
.form_item {
    margin: 0 32px;
}
.form_item_wrap {
    margin-bottom: 24px;
    padding-bottom: 32px;
    background: #fff;
    border-radius: 24px;
    .file_tips {
        margin-top: -40px;
        padding: 0 32px;
        color: #9597a0;
        font-weight: 400;
        font-size: 24px;
        line-height: 36px;

        text {
            color: #ff8f28;
        }
    }
}

.label {
    color: #272c47;
    font-weight: 400;
    font-size: 32px;
}
.input {
    flex: 1;
    text-align: right;
}
textarea {
    background-color: #ffffff;
    color: #272C47;
}
.placeholder {
    color: #9597a0;
    font-size: 32px;

    &_textarea {
        color: #ccced7;
    }
}

.form_tips {
    display: flex;
    align-items: flex-start;
    color: #67686f;
    font-weight: 400;
    font-size: 24px;
    line-height: normal;
    text {
        color: #4f66ff;
    }
}

.actionsheet_title{
    text-align: center;
    font-size: 40px;
    font-weight: 600;
    margin-top: 48px;
}