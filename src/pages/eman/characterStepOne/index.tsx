import { Page } from '@/components';
import config from '@/config';
import { Icon, Image, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import styles from './index.less';

const doctor = `${config.cdnPrefix}eman/doctor.png`;
const nurse = `${config.cdnPrefix}eman/nurse.png`;
const pharmacist = `${config.cdnPrefix}eman/pharmacist.png`;
const technician = `${config.cdnPrefix}eman/technician.png`;

const App = () => {
    const { params } = useRouter<{ type: 'create' | 'edit'; id?: string; occupation?: string; status?: string }>();

    const [title, setTitle] = useState('');
    const [activeName, setActiveName] = useState<string>('');
    const [occupationList] = useState([
        { icon: doctor, name: '执业医师' },
        { icon: nurse, name: '护士' },
        { icon: pharmacist, name: '执业药师' },
        { icon: technician, name: '技师' }
    ]);

    useEffect(() => {
        if (params.type === 'create') {
            setTitle('创建E人');
        } else if (params.type === 'edit') {
            setTitle('基础人设');
            setActiveName(params.occupation as string);
        }
    }, [params]);

    const goBack = () => {
        Taro.navigateBack();
    };

    const goNext = () => {
        Taro.navigateTo({
            url: `/pages/eman/characterStepTwo/index?type=${params.type}&occupation=${activeName}&id=${params.id}`
        });
    };
    const _setActiveName = (name: string) => {
        if (!['2', '3'].includes(params?.status as string)) {
            setActiveName(name);
        }
    };

    return (
        <Page className={styles.page}>
            <NavBar className={styles.navbar} title={title} leftArrow safeAreaInsetTop onClickLeft={() => goBack()} />
            <View className={styles.page_content}>
                {['2', '3'].includes(params?.status as string) && (
                    <View className={styles.page_tips_info}>
                        <Icon name='info-o' size={pxTransform(32)} color='#4C82FB' className='icon' />
                        <View className={styles.t1}>如需修改，请先下架E人或撤销E人审核</View>
                    </View>
                )}
                <View className={styles.title}>
                    {params.type === 'create' ? <View>我们开始吧</View> : null}
                    <View>请选择E人的职业</View>
                </View>
                <View className={styles.role_list}>
                    {occupationList.map((item) => (
                        <View className={styles.role_item} key={item.name} onClick={() => _setActiveName(item.name)}>
                            <View
                                className={classNames(
                                    styles.role_avatar_box,
                                    activeName === item.name ? styles.active : ''
                                )}
                            >
                                <Image className={styles.role_avatar} round fit='contain' src={item.icon} />
                            </View>
                            <View className={styles.role_name}>{item.name}</View>
                        </View>
                    ))}
                </View>
            </View>
            <View className={styles.bottom}>
                <Button
                    onClick={goNext}
                    disabled={activeName === ''}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    下一步
                </Button>
                <View className={styles.step}>1/2</View>
            </View>
        </Page>
    );
};

export default App;
