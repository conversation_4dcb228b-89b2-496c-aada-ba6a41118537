@import '@/styles/index.less';
page {
  background: linear-gradient(188deg, #dddeff99 0.35%, #f8faff99 24.5%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

  --nav-bar-background-color: transparent;
  --nav-bar-icon-color: #333;
  --nav-bar-arrow-size: 48px;
}

.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding-bottom: 100px;
  box-sizing: border-box;
}

.page_content {
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
}

.title {
  font-size: 40px;
  color: #000;
  font-weight: 500;
  line-height: 60px;
  margin-top: 75px;
  margin-bottom: 100px;
  text-align: center;
}

.role {
  &_list {
    overflow: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    padding-left: 108px;
    padding-right: 108px;
    gap: 92px 0;
  }
  &_item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  &_avatar {
    width: 200px;
    height: 200px;
  
    &_box {
      width: 224px - 8px;
      height: 224px - 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 100%;
      border-width: 4px;
      border-style: solid;
      border-color: transparent;
      &.active {
        border-color:  #4F66FF;
        box-shadow: 0 4px 10px 0 #4F66FF47;
      }
    }
  }
  &_name {
    font-size: 28px;
    font-weight: 400;
    text-align: center;
    margin-top: 16px;
    color: #000;
  }
}

.bottom {
  padding: 21px 36px 38px 36px;
}
.step {
  color: #9597A0;
  font-size: 24px;
  text-align: center;
  margin-top: 28px;
}
.page_tips_info {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  box-sizing: border-box;
  width: 686px;
  height: 80px;
  margin: 28px auto;
  padding: 20px 30px;
  font-size: 28px;
  background: #e6eeff;
  border-radius: 32px;
  .t1 {
      margin-left: 15px;
  }
}