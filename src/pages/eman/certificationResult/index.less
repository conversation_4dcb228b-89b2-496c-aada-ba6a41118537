page {
    background: #f3f3f3;

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
}
.page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    height: 100vh;
    padding-bottom: 100px;
    background: #fff;
}
.content {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
    transform: translate(-50%, -100%);
    .img {
        width: calc(476px / 2);
        height: calc(409px / 2);
    }
    .t1 {
        margin-top: 36px;
        color: #272c47;
        font-weight: bold;
        font-size: 32px;
    }
    .t2 {
        margin-top: 16px;
        color: #9597a0;
        font-size: 28px;
    }
    .btn {
        width: 320px;
        margin-top: 40px;
    }
}
