import { Page } from '@/components';
import { Image } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { View } from '@tarojs/components';

import config from '@/config';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import styles from './index.less';

const resultSuccess = `${config.cdnPrefix}eman/result-success.png`;
const App = () => {
    const { params } = useRouter<{ eid: string }>();
    const { eid } = params; // eManId

    const onConfirm = () => {
        Taro.navigateTo({
            url: `/pages/eman/home/<USER>
        });
    };
    return (
        <Page className={styles.page}>
            <View className={styles.content}>
                <Image className={styles.img} src={resultSuccess} />
                <View className={styles.t1}>提交成功</View>
                <View className={styles.t2}>审核中</View>
                <Button
                    className={styles.btn}
                    onClick={onConfirm}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    完成
                </Button>
            </View>
        </Page>
    );
};

export default App;
