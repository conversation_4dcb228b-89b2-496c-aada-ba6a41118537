import { Popup } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Text, View } from '@tarojs/components';
import { pxTransform, showToast } from '@tarojs/taro';
import { useCallback, useEffect, useState } from 'react';
import styles from './index.less';

export interface option {
    text: string;
    children: string[];
}
export interface props {
    options: option[];
}

const Index: React.FC<props> = (props: any) => {
    const { title, options, value } = props;
    // const [currentText, setcurrentText] = useState(''); //
    const [currentIndexLeft, setcurrentIndexLeft] = useState(0);
    const [currentIndexRight, setcurrentIndexRight] = useState(-1);
    const [state, changeState] = useState<{ show: boolean; innerValue: string[] }>({
        show: false,
        innerValue: []
    });

    const setState = useCallback(
        (key: string, value: any) => {
            changeState({
                ...state,
                [key]: value
            });
        },
        [state]
    );

    const toggleShow = useCallback(
        (show: boolean) => {
            setState('show', show);
        },
        [setState]
    );

    const onConfirm = useCallback(() => {
        // console.log(
        //     'onConfirm',
        //     options,
        //     currentIndexLeft,
        //     currentIndexRight,
        //     options[currentIndexLeft].children[currentIndexRight]
        // );
        if (currentIndexLeft === -1 || currentIndexRight === -1) {
            showToast({
                title: `请选择${title}`,
                icon: 'none'
            });
            return;
        }

        if (props.onConfirm)
            props.onConfirm(
                `${options[currentIndexLeft].text}-${options[currentIndexLeft].children[currentIndexRight]}`
            );
        // setcurrentText(options[currentIndexLeft].text + '-' + options[currentIndexLeft].children[currentIndexRight]);
        toggleShow(false);
    }, [currentIndexLeft, currentIndexRight, props, state.innerValue, toggleShow, title]); // why

    const onCancel = useCallback(() => {
        if (props.onCancel) props.onCancel();
        toggleShow(false);
    }, [props, toggleShow]);

    useEffect(() => {
        console.log('useEffect-value', value);
        if (state.show && options?.length && value && value.includes('-')) {
            const indexLeft = options.findIndex((item: any) => item.text === value.split('-')[0]);
            if (indexLeft !== -1) {
                setcurrentIndexLeft(indexLeft);
                const indexRight = options[indexLeft].children.findIndex((item: any) => item === value.split('-')[1]);
                if (indexRight !== -1) {
                    setcurrentIndexRight(indexRight);
                }
            }
        }
        // if (!state.show) {
        //     setState('innerValue', [...(value || [])]);
        //     // setcurrentText(value);
        // }
    }, [state.show, value]);

    function handleChooseItemLeft(item: any, index: number) {
        setcurrentIndexLeft(index);
        setcurrentIndexRight(-1);
    }
    function handleChooseItemRight(item: any, index: number) {
        setcurrentIndexRight(index);
    }
    return (
        <View className={styles.formCascader}>
            <View onClick={() => toggleShow(true)} className={styles.input}>
                <Text className={value?.length ? styles.hasvalue : ''}>{value || '请选择'}</Text>
            </View>
            <Popup
                position='bottom'
                show={state.show}
                closeable
                onClose={onCancel}
                safeAreaInsetBottom
                round
                className={styles.my_popup}
            >
                <View className={styles.popup_title}>选择{title}</View>
                <View className={styles.my_cascader}>
                    <View className={styles.left}>
                        {options?.map((item: option, index: number) => (
                            <View
                                className={currentIndexLeft === index ? styles.item_current : styles.item}
                                key={item.text}
                                onClick={() => handleChooseItemLeft(item, index)}
                            >
                                {item.text}
                            </View>
                        ))}
                    </View>
                    <View className={styles.right}>
                        {options?.[currentIndexLeft].children.map((item: string, index: number) => (
                            <View
                                className={currentIndexRight === index ? styles.item_current : styles.item}
                                key={item}
                                onClick={() => handleChooseItemRight(item, index)}
                            >
                                {item}
                            </View>
                        ))}
                    </View>
                </View>
                <View className={styles.btn_group}>
                    <Button
                        onClick={onCancel}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                    <Button
                        onClick={onConfirm}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                </View>
            </Popup>
        </View>
    );
};

export default Index;
