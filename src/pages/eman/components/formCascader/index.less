:global {
    .van-checkbox-group--horizontal {
        gap: 16px;
        justify-content: center;
    }
    .van-checkbox--horizontal {
        position: relative;
        min-width: 103px;
        height: 63px;
        margin-right: 0;
    }
    .van-popup__close-icon {
        top: 58px;
        right: 42px;
        color: #272c47;
    }
}
.formCascader {
    width: 100%;
    :global {
        .van-popup--bottom {
            height: 80vh;
        }
    }
}
.input {
    width: 100%;
    color: #9597a0;
    font-weight: 400;
    font-size: 32px;
    text-align: right;
}
.hasvalue {
    color: #272c47;
}

.popup_title {
    height: 48px;
    margin-top: 48px;
    font-weight: 600;
    font-size: 40px;
    text-align: center;
}
.btn_group {
    position: absolute;
    bottom: calc(30px + constant(safe-area-inset-bottom));
    bottom: calc(30px + env(safe-area-inset-bottom));
    display: flex;
    gap: 24px;
    box-sizing: border-box;
    width: 100%;
    padding: 0 32px;
}
.my_cascader {
    display: flex;
    justify-content: space-between;
    height: calc(100% - 48px - 48px - 96px - 30px - constant(safe-area-inset-bottom));
    height: calc(100% - 48px - 48px - 96px - 30px - env(safe-area-inset-bottom));
    padding: 30px 0 30px;

    .left {
        width: 40%;
        overflow-y: scroll;
        background: #f6f6f6;
        .item {
            flex-shrink: 0;
            height: 103px;
            padding-left: 48px;
            line-height: 103px;
        }
        .item_current {
            position: relative;
            flex-shrink: 0;
            height: 103px;
            padding-left: 48px;
            color: #4f66ff;
            line-height: 103px;
            background: #fff;
            &::before {
                position: absolute;
                top: calc((103px - 40px) / 2);
                left: 0;
                width: 0;
                height: 40px;
                border-top: 4px solid transparent;
                border-bottom: 4px solid transparent;
                border-left: 8px solid blue;
                content: '';
            }
        }
    }
    .right {
        width: 60%;
        overflow-y: scroll;
        .item {
            flex-shrink: 0;
            height: 103px;
            padding-left: 32px;
            line-height: 103px;
        }
        .item_current {
            flex-shrink: 0;
            height: 103px;
            padding-left: 32px;
            color: #4f66ff;
            line-height: 103px;
        }
    }
}
