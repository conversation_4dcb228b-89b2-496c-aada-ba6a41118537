import config from '@/config';
import { getAvatar } from '@/services/common';
import type { EmanAvatarVo } from '@/types/eman';
import { ActionSheet, Image, Popup } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, ScrollView, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import classnames from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

enum GenderTabEnum {
    Male = 1,
    Female = 2
}

const AvatarDefault = `${config.cdnPrefix}eman/avatar_default.svg`;
const female = `${config.cdnPrefix}eman/female.svg`;
const male = `${config.cdnPrefix}eman/male.svg`;
const mark_3d = `${config.cdnPrefix}mark_3d.svg`;
const Index = (props: any) => {
    // 重置声音
    const { value, oldGender, handleReset } = props;
    const [list, setList] = useState<EmanAvatarVo[]>([]);
    const genderTab = useRef<GenderTabEnum>((oldGender === 0 ? GenderTabEnum.Male : oldGender) || GenderTabEnum.Male);
    const [avatar, setAvatar] = useState<string>();

    const backgroundIndex = useRef<number>(0);

    const [state, changeState] = useState<{
        show: boolean;
        innerValue: { avatar: string; background: string; zipFileUrl: string; videoUrl: string };
    }>({
        show: false,
        innerValue: { avatar: '', background: '', zipFileUrl: '', videoUrl: '' }
    });

    const [showActionSheet, setShowActionSheet] = useState(false);
    const setState = useCallback(
        (key: string, value: any) => {
            changeState({
                ...state,
                [key]: value
            });
        },
        [state]
    );
    const setValueFn = (index: number) => {
        backgroundIndex.current = index;
        setState('innerValue', {
            avatar: list[backgroundIndex.current].avatar,
            background: list[backgroundIndex.current].background,
            zipFileUrl: list[backgroundIndex.current].zipFileUrl,
            videoUrl: list[backgroundIndex.current].videoUrl
        });
    };

    const _getAvatar = useCallback(async () => {
        const url = genderTab.current === 1 ? '/common/male-avatar' : '/common/female-avatar';
        const res = await getAvatar(url);

        setList(() => {
            return [...res.data.data];
        });
        const i = res.data.data.findIndex((item) => item.avatar === avatar);

        if (i !== -1) {
            backgroundIndex.current = i;
        }
        setState('innerValue', {
            avatar: res.data.data[backgroundIndex.current].avatar,
            background: res.data.data[backgroundIndex.current].background,
            zipFileUrl: res.data.data[backgroundIndex.current].zipFileUrl,
            videoUrl: res.data.data[backgroundIndex.current].videoUrl
        });
    }, [genderTab, setState]);

    const toggleShow = useCallback(
        (show: boolean) => {
            if (![2, 3].includes(props.status)) {
                setState('show', show);
            }
        },
        [setState, props.status]
    );

    const onConfirm = useCallback(() => {
        const tone = props.tone ? JSON.parse(props.tone).name : '';
        if (oldGender !== 0 && genderTab.current !== oldGender && tone) {
            setShowActionSheet(true);
            return;
        }

        if (props.onConfirm) props.onConfirm({ ...state.innerValue, gender: genderTab.current });
        setAvatar(state.innerValue.avatar);
        toggleShow(false);
    }, [props, state.innerValue, toggleShow, genderTab]);

    const onCancel = useCallback(() => {
        if (props.onCancel) props.onCancel();
        toggleShow(false);
    }, [props, toggleShow]);

    const _handleReset = () => {
        if (props.onConfirm) props.onConfirm({ ...state.innerValue, gender: genderTab.current });
        setAvatar(state.innerValue.avatar);
        toggleShow(false);
        handleReset();
        setShowActionSheet(false);
    };

    useEffect(() => {
        if (!state.show) {
            setAvatar(value.avatar);
        }
        if (state.show) {
            genderTab.current = oldGender === 0 ? GenderTabEnum.Male : oldGender;
            setList(() => {
                return [];
            });
            backgroundIndex.current = 0;
            _getAvatar();
        }
    }, [state.show, value]);

    const getAvatarList = (gender: number) => {
        setList(() => {
            return [];
        });
        genderTab.current = gender;
        _getAvatar();
    };

    return (
        <Block>
            <View className={styles.avatar_box} onClick={() => toggleShow(true)}>
                <Image className={styles.avatar} fit='contain' src={avatar || AvatarDefault} />

                {(list[backgroundIndex.current]?.zipFileUrl || value.zipFileUrl) && (
                    <Image src={mark_3d} className={styles.mark_3d} />
                )}
            </View>
            <Popup
                position='bottom'
                show={state.show}
                closeable
                onClose={onCancel}
                safeAreaInsetBottom
                round
                zIndex={100}
            >
                <View className={styles.popup_title}>选择头像</View>
                <View className={styles.popup_content}>
                    <View className={styles.tabs}>
                        <View
                            className={classnames(styles.tab, {
                                [styles.tabs_select]: genderTab.current === GenderTabEnum.Male
                            })}
                            onClick={() => getAvatarList(GenderTabEnum.Male)}
                        >
                            <img src={male} className={styles.icon} />
                        </View>
                        <View
                            className={classnames(styles.tab, {
                                [styles.tabs_select]: genderTab.current === GenderTabEnum.Female
                            })}
                            onClick={() => getAvatarList(GenderTabEnum.Female)}
                        >
                            <img src={female} className={styles.icon} />
                        </View>
                    </View>
                    <View className={styles.avatar_selected}>
                        <Image
                            className={styles.avatar_selected_img}
                            radius={pxTransform(32)}
                            fit='cover'
                            src={list[backgroundIndex.current]?.avatar || AvatarDefault}
                        />
                        {list[backgroundIndex.current]?.zipFileUrl && list[backgroundIndex.current]?.show3dFlag && (
                            <Image src={mark_3d} className={styles.mark_3d} />
                        )}
                    </View>
                </View>
                <ScrollView scrollX className={styles.avatar_scroll}>
                    {list.map((item, index) => (
                        <View className={styles.avatar_item} key={`avatar${index}`} onClick={() => setValueFn(index)}>
                            <Image
                                radius={pxTransform(24)}
                                fit='contain'
                                src={item.avatar}
                                className={styles.avatar_img}
                            />
                            {item.zipFileUrl && <Image src={mark_3d} className={styles.mark_3d} />}
                        </View>
                    ))}
                </ScrollView>
                <View className={styles.actions}>
                    <Button
                        onClick={onCancel}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                    <Button
                        onClick={onConfirm}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                </View>
            </Popup>
            <ActionSheet
                show={showActionSheet}
                zIndex={110}
                onClose={() => setShowActionSheet(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>头像与声音的性别冲突</View>
                        <View>继续修改将重置声音，确定吗？</View>
                    </View>
                }
                closeOnClickOverlay={false}
                // @ts-ignore
                style={{ '--action-sheet-header-height': pxTransform(124) }}
            >
                <Button.Group className={styles.actionSheet_group} direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={_handleReset}
                    >
                        确定
                    </Button>
                    <Button
                        block
                        round
                        color='#F6F6F6'
                        style={{ color: '#777777' }}
                        onClick={() => setShowActionSheet(false)}
                    >
                        取消
                    </Button>
                </Button.Group>
            </ActionSheet>
        </Block>
    );
};

export default Index;
