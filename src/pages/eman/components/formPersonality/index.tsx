import { getNatureList } from '@/services/common';
import { Checkbox, CheckboxGroup, Icon, Popup } from '@antmjs/vantui';

import { Button } from '@hygeia/ui';
import { Text, View } from '@tarojs/components';
import Taro, { pxTransform, useDidShow } from '@tarojs/taro';
import classNames from 'classnames';
import { useCallback, useEffect, useState } from 'react';
import styles from './index.less';
const Index = (props: any) => {
    const { value } = props;
    const [list, setList] = useState<string[]>([]);
    const _getNatureList = async () => {
        const res = await getNatureList();
        setList(res.data.data);
    };
    useDidShow(() => {
        _getNatureList();
    });
    const [state, changeState] = useState<{ show: boolean; innerValue: string[] }>({
        show: false,
        innerValue: []
    });

    const setState = useCallback(
        (key: string, value: any) => {
            changeState({
                ...state,
                [key]: value
            });
        },
        [state]
    );

    const toggleShow = useCallback(
        (show: boolean) => {
            if (![2, 3].includes(props.status)) {
                setState('show', show);
            }
        },
        [setState, props.status]
    );

    const formatValue = useCallback((value: string[]) => {
        return value.join(',');
    }, []);

    const onConfirm = useCallback(() => {
        if (props.onConfirm) props.onConfirm(state.innerValue);
        toggleShow(false);
    }, [props, state.innerValue, toggleShow]);

    const onCancel = useCallback(() => {
        if (props.onCancel) props.onCancel();
        toggleShow(false);
    }, [props, toggleShow]);
    const setValue = (array: string[]) => {
        setState('innerValue', [...array]);
    };
    const handleMax = (item: string) => {
        if (state.innerValue.includes(item)) return;
        if (state.innerValue.length === 3) {
            Taro.showToast({ icon: 'none', title: '最多选择3个标签' });
        }
    };
    useEffect(() => {
        if (!state.show) {
            setState('innerValue', [...(value || [])]);
        }
    }, [state.show, value]);

    return (
        <>
            <View onClick={() => toggleShow(true)} className={styles.input}>
                <Text className={value?.length ? styles.hasvalue : ''}>
                    {value && value.length ? formatValue(value) : '请选择'}
                </Text>
                {![2, 3].includes(props.status) && (
                    <Icon name='arrow' color='#C9C9C9' size={pxTransform(36)} className={styles.icon_right} />
                )}
            </View>
            <Popup position='bottom' show={state.show} closeable onClose={onCancel} safeAreaInsetBottom round>
                <View className={styles.popup_title}>选择性格标签</View>
                <View className={styles.tip}>请选择1-3个性格描述</View>
                <view className={styles.tag_list}>
                    <CheckboxGroup
                        direction='horizontal'
                        value={state.innerValue}
                        max={3}
                        onChange={(e) => setValue(e.detail)}
                    >
                        {list.map((item) => (
                            <Checkbox
                                name={item}
                                key={item}
                                onClick={() => handleMax(item)}
                                renderIcon={
                                    <View
                                        className={
                                            state.innerValue.includes(item)
                                                ? classNames(styles.check_active, styles.check)
                                                : classNames(styles.check_normal, styles.check)
                                        }
                                    >
                                        {item}
                                    </View>
                                }
                            />
                        ))}
                    </CheckboxGroup>
                </view>
                <View className={styles.actions}>
                    <Button
                        onClick={onCancel}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                    <Button
                        onClick={onConfirm}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        disabled={state.innerValue.length < 1}
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                </View>
            </Popup>
        </>
    );
};

export default Index;
