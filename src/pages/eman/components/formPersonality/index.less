:global {
  .van-checkbox-group--horizontal {
    justify-content: center;
    gap: 16px;
  }
  .van-checkbox--horizontal {
    min-width: 103px;
    height: 63px;
    position: relative;
    margin-right: 0;
  }
  .van-popup__close-icon  {
    color: #272C47;
    top: 58px;
    right: 42px;
  }
}
.input {
  font-size: 32px;
  color: #9597a0;
  text-align: right;
  font-weight: 400;
  width: 100%;
}
.hasvalue {
  color: #272C47;
}
.popup_title {
  text-align: center;
  font-size: 40px;
  font-weight: 600;
  margin-top: 48px;
}
.tip {
  text-align: center;
  font-size: 28px;
  font-weight: 400;
  margin-top: 32px;
}
.tag_list {
  margin: 48px 86px 74px;
}
.check {
  padding-left: 24px;
  padding-right: 24px;
  height: 63px;
  display: flex;
  align-items: center;
  border-radius: 16px;
  &_normal {
    background: #F6F6F6;
    color: #272c47;
    font-size: 28px;
    font-weight: 400;
  }
  &_active {
    color: #fff;
    background: linear-gradient(270deg, #6745FF 0%, #4082FF 100%);
  }
}

.actions {
  display: flex;
  gap: 24px;
  padding: 0 32px;
  margin-bottom: 24px;
}