@import '@/styles/index.less';

:global {
  .van-cell  {
    border-radius: 20px;
    background: #F6F6F6;
    margin-bottom: 16px;
  }
  .van-popup__close-icon  {
    color: #272C47;
    top: 58px;
    right: 42px;
  }
}
.input {
  font-size: 32px;
  color: #9597a0;
  text-align: right;
  font-weight: 400;
  width: 100%;
}
.hasvalue {
  color: #272C47;
}
.popup_title {
  text-align: center;
  font-size: 40px;
  font-weight: 600;
  margin-top: 48px;
}
.tag_list {
  margin: 32px 32px 21px;
  height: 709px - 32px;
  overflow-y: auto;
}
.icon_voice {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  vertical-align: middle;
}
.icon_voice_loading {
  width: 40px;
  height: 40px;
  margin-right: 16px;
  vertical-align: middle;
  // 无限360度旋转动画
  animation: spin 1s linear infinite;
}
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.check_active {
  width: 30px;
  height: 30px;
}
.popup_content {
  margin: 32px 32px 0 32px ;
}
.actions {
  display: flex;
  gap: 24px;
  padding: 0 32px;
  margin-bottom: 24px;
}
.tabs {
  .font(28px,#9597A0,400);

  height: 88px;
  background-color: #F7F7F7;
  box-sizing: border-box;
  padding: 6px;
  border-radius: 16px;
  display: flex;
  justify-content: space-between;
  scroll-behavior: smooth;
  margin-top: 32px;


  &_select {
      .font(28px,#272C47,600);

      border: 1px solid #EDEDED;
      background: #FFF;
      border-radius: 16px;
      
  }
}
.tab {
  .flex-center;
  .icon{
    width: 36px;
    height: 36px
  }

  border: 1px solid transparent;
  width: 339px;
}