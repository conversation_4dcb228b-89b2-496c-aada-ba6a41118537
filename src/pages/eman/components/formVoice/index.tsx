import config from '@/config';
import { getManVoice, getWoManVoice, ttsVoice } from '@/services/common';
import type { VoiceVo } from '@/types/eman';
import { ActionSheet, Cell, Icon, Popup, Radio, RadioGroup } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Image, Text, View } from '@tarojs/components';
import type { InnerAudioContext } from '@tarojs/taro';
import { createInnerAudioContext, env, getFileSystemManager, pxTransform, setInnerAudioOption } from '@tarojs/taro';
import { useMount, useUnmount } from 'ahooks';
import classnames from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import styles from './index.less';

const female = `${config.cdnPrefix}eman/female.svg`;
const male = `${config.cdnPrefix}eman/male.svg`;

// const plugin = requirePlugin('QCloudAIVoice');
// plugin.setQCloudSecret(config.QCloudAIVoice.appId, config.QCloudAIVoice.secretId, config.QCloudAIVoice.secretKey, true); // 设置腾讯云账号信息，其中appid是数字，secretid/secretkey是字符串，openConsole是布尔值(true/false)，为控制台打印日志开关

const Index = (props: any) => {
    enum GenderTabEnum {
        Male = 1,
        Female = 2
    }
    const IconCheckActive = `${config.cdnPrefix}check_active.svg`;
    // const list = [
    //     { id: 1, tag: '京腔大爷' },
    //     { id: 2, tag: '湾区大叔' },
    //     { id: 3, tag: '亲切男声' },
    //     { id: 4, tag: '智慧老者' }
    // ];
    const [list, setList] = useState<VoiceVo[]>([]);

    const { value, oldGender, handleReset } = props;
    const genderTab = useRef<GenderTabEnum>((oldGender === 0 ? GenderTabEnum.Male : oldGender) || GenderTabEnum.Male);

    const [state, changeState] = useState<{ show: boolean; voiceText: '' }>({
        show: false,
        voiceText: ''
    });
    const [showActionSheet, setShowActionSheet] = useState(false);

    const innerAudioContext = useRef<InnerAudioContext | null>();
    const currentVoice = useRef<number | null>();
    const [playNow, setPlayNow] = useState<{ voiceType: number | null; state: number | null }>();

    const setState = useCallback(
        (key: string, value: any) => {
            changeState({
                ...state,
                [key]: value
            });
        },
        [state]
    );

    const _getVoice = useCallback(async () => {
        const res = genderTab.current === 1 ? await getManVoice() : await getWoManVoice();
        setList(() => {
            return [...res.data.data];
        });
    }, []);

    // useDidShow(() => {
    //     _getManVoice();
    // });

    const toggleShow = useCallback(
        (show: boolean) => {
            if (![2, 3].includes(props.status)) {
                setState('show', show);
            }
        },
        [setState, props.status]
    );

    const formatValue = useCallback((value: string) => {
        if (value) {
            const valueObj = JSON.parse(value);
            if (valueObj.name === '') return '';
            return valueObj.name;
        } else {
            return '';
        }
    }, []);

    const onConfirm = useCallback(() => {
        if (oldGender !== 0 && genderTab.current !== oldGender && props.avatar !== '') {
            setShowActionSheet(true);
            return;
        }
        if (props.onConfirm) props.onConfirm(state.voiceText);
        props.handleGender(genderTab.current);
        toggleShow(false);
    }, [props, state.voiceText, toggleShow, oldGender]);

    const onCancel = useCallback(() => {
        if (props.onCancel) props.onCancel();
        toggleShow(false);
    }, [props, toggleShow]);
    const _handleReset = () => {
        if (props.onConfirm) props.onConfirm(state.voiceText);
        toggleShow(false);
        handleReset(genderTab.current);
        setShowActionSheet(false);
    };

    const stopAudio = () => {
        if (innerAudioContext.current) {
            innerAudioContext.current.stop();
        }
    };

    const handleItemClick = (item: VoiceVo) => {
        setState('voiceText', JSON.stringify(item));
        currentVoice.current = item.voiceType;
        stopAudio();
        setPlayNow({
            voiceType: item.voiceType,
            state: 0
        });
        const fs = getFileSystemManager();
        const audioPath = `${env.USER_DATA_PATH}/eman/voice_${item.name}_${item.voiceType}.mp3`;
        fs.access({
            path: audioPath,
            success: () => {
                if (innerAudioContext.current && currentVoice.current === item.voiceType) {
                    innerAudioContext.current.src = audioPath;
                    innerAudioContext.current.onPlay(() => {
                        console.log('开始播放');
                        setPlayNow({
                            state: 1,
                            voiceType: item.voiceType
                        });
                    });
                    innerAudioContext.current.onEnded(() => {
                        console.log('播放结束');
                    });
                    innerAudioContext.current.play();
                } else {
                    console.log('没有语音');
                    setPlayNow({
                        state: 1,
                        voiceType: item.voiceType
                    });
                }
            },
            fail: () => {
                ttsVoice({
                    name: item.name,
                    type: genderTab.current,
                    text: '你好，欢迎使用语贝，让我们一起开启全新的旅程'
                }).then(async (res) => {
                    fs.writeFile({
                        filePath: audioPath,
                        data: res.data.data,
                        encoding: 'base64',
                        success: (e: any) => {
                            console.log('文件保存成功', e);
                            if (innerAudioContext.current && currentVoice.current === item.voiceType) {
                                innerAudioContext.current.src = audioPath;
                                innerAudioContext.current.onPlay(() => {
                                    console.log('开始播放');
                                    setPlayNow({
                                        state: 1,
                                        voiceType: item.voiceType
                                    });
                                });
                                innerAudioContext.current.onEnded(() => {
                                    console.log('播放结束');
                                });
                                innerAudioContext.current.play();
                            } else {
                                console.log('没有语音');
                                setPlayNow({
                                    state: 1,
                                    voiceType: item.voiceType
                                });
                            }
                        },
                        fail(error) {
                            console.error('文件保存失败', error);
                        }
                    });
                });
            }
        });
    };
    const handleTabChange = (e: GenderTabEnum) => {
        stopAudio();
        setPlayNow({ voiceType: null, state: null });
        setList(() => {
            return [];
        });
        genderTab.current = e;
        _getVoice();
    };
    useMount(() => {
        const fs = getFileSystemManager();
        fs.access({
            path: `${env.USER_DATA_PATH}/eman`,
            success: () => {
                fs.rmdir({
                    dirPath: `${env.USER_DATA_PATH}/eman`,
                    recursive: true,
                    complete: () => {
                        fs.mkdir({
                            dirPath: `${env.USER_DATA_PATH}/eman`,
                            recursive: false,
                            success(res) {
                                console.log(res);
                            },
                            fail(res) {
                                console.error(res);
                            }
                        });
                    }
                });
            },
            fail: () => {
                fs.mkdir({
                    dirPath: `${env.USER_DATA_PATH}/eman`,
                    recursive: false,
                    success(res) {
                        console.log(res);
                    },
                    fail(res) {
                        console.error(res);
                    }
                });
            }
        });
    });
    useUnmount(() => {
        const fs = getFileSystemManager();
        fs.rmdir({
            dirPath: `${env.USER_DATA_PATH}/eman`,
            recursive: true,
            success(res) {
                console.log(res);
            },
            fail(res) {
                console.error(res);
            }
        });
    });
    useEffect(() => {
        if (state.show) {
            if (value) {
                setState('voiceText', value);
                currentVoice.current = value.voiceType;
            }
            genderTab.current = oldGender === 0 ? GenderTabEnum.Male : oldGender;
            setList(() => {
                return [];
            });
            _getVoice();
            innerAudioContext.current = createInnerAudioContext();
            setInnerAudioOption({ obeyMuteSwitch: false });
        } else {
            innerAudioContext.current?.destroy();
            innerAudioContext.current = null;
            setPlayNow({ voiceType: null, state: null });
        }
    }, [oldGender, state.show]);

    return (
        <Block>
            <View onClick={() => toggleShow(true)} className={styles.input}>
                <Text className={value ? styles.hasvalue : ''}>{value ? formatValue(value) : '请选择'}</Text>
                {![2, 3].includes(props.status) && (
                    <Icon name='arrow' color='#C9C9C9' size={pxTransform(36)} className={styles.icon_right} />
                )}
            </View>
            <Popup
                position='bottom'
                show={state.show}
                closeable
                onClose={onCancel}
                safeAreaInsetBottom
                round
                zIndex={100}
            >
                <View className={styles.popup_title}>选择声音</View>
                <View className={styles.popup_content}>
                    <View className={styles.tabs}>
                        <View
                            className={classnames(styles.tab, {
                                [styles.tabs_select]: genderTab.current === GenderTabEnum.Male
                            })}
                            onClick={() => handleTabChange(GenderTabEnum.Male)}
                        >
                            <img src={male} className={styles.icon} />
                        </View>
                        <View
                            className={classnames(styles.tab, {
                                [styles.tabs_select]: genderTab.current === GenderTabEnum.Female
                            })}
                            onClick={() => handleTabChange(GenderTabEnum.Female)}
                        >
                            <img src={female} className={styles.icon} />
                        </View>
                    </View>
                </View>
                <View className={styles.tag_list}>
                    <RadioGroup
                        direction='vertical'
                        value={state.voiceText ? JSON.parse(state.voiceText).voiceType : ''}
                    >
                        {list.map((item) => (
                            <Cell
                                border={false}
                                renderTitle={
                                    <View>
                                        {playNow?.voiceType === item.voiceType ? (
                                            playNow.state === 0 ? (
                                                <Image
                                                    className={styles.icon_voice_loading}
                                                    src={`${config.cdnPrefix}eman/assets/voice_loading.svg`}
                                                />
                                            ) : (
                                                <Image
                                                    className={styles.icon_voice}
                                                    src={`${config.cdnPrefix}eman/assets/icon_voice.svg`}
                                                />
                                            )
                                        ) : (
                                            <Image
                                                className={styles.icon_voice}
                                                src={`${config.cdnPrefix}eman/assets/icon_voice.svg`}
                                            />
                                        )}
                                        <Text>{item.name}</Text>
                                    </View>
                                }
                                key={item.name}
                                clickable
                                onClick={() => handleItemClick(item)}
                                renderRightIcon={
                                    <Radio
                                        name={item.name}
                                        value={item.name}
                                        checkedColor='#4F66FF'
                                        renderIcon={
                                            (state.voiceText ? JSON.parse(state.voiceText).voiceType : '') ===
                                            item.voiceType ? (
                                                <Image className={styles.check_active} src={IconCheckActive} />
                                            ) : (
                                                <Icon name='circle' size={pxTransform(30)} color='#CCCCCC' />
                                            )
                                        }
                                    />
                                }
                            />
                        ))}
                    </RadioGroup>
                </View>
                <View className={styles.actions}>
                    <Button
                        onClick={onCancel}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                    <Button
                        onClick={onConfirm}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                </View>
            </Popup>
            <ActionSheet
                show={showActionSheet}
                zIndex={110}
                onClose={() => setShowActionSheet(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>声音与头像的性别冲突</View>
                        <View>继续修改将重置头像，确定吗？</View>
                    </View>
                }
                closeOnClickOverlay={false}
                // @ts-ignore
                style={{ '--action-sheet-header-height': pxTransform(124) }}
            >
                <Button.Group className={styles.actionSheet_group} direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={_handleReset}
                    >
                        确定
                    </Button>
                    <Button
                        block
                        round
                        color='#F6F6F6'
                        style={{ color: '#777777' }}
                        onClick={() => setShowActionSheet(false)}
                    >
                        取消
                    </Button>
                </Button.Group>
            </ActionSheet>
        </Block>
    );
};

export default Index;
