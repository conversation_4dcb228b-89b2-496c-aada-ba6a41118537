import { Page } from '@/components';
import { View } from '@tarojs/components';
import styles from './index.less';
const App = () => {
    const html = `
    <p><strong>更新时间：2024年4月29日</strong></p>
    <p><strong>生效时间：2024年5月1日</strong></p>
    <p>欢迎您与我们共同签署《语贝职业认证服务协议》（以下简称“本协议”），本协议签署后您可使用我们提供的语贝职业认证服务。</p>
    <p>各条款前所列索引关键词仅为帮助您理解该条款表达的主旨之用，不影响或限制本协议条款的含义或解释。为维护您自身权益，请您仔细阅读各条款具体表述。</p>
    <p><strong>【提示条款】</strong><strong>一旦您在线点击“同意”</strong> <strong>（</strong> <strong>或点击其他表示接受的按钮</strong> <strong>）</strong> <strong>本协议，即表示您已充分阅读、理解并接受本协议的全部内容，并与我们达成一致，</strong> <strong>本协议即于您和我们之间产生法律约束力。如您不同意本协议或其中任何条款约定，请您立即停止使用本服务。</strong></p>
    <h2 id="1"><strong>1. 定义</strong></h2>
    <p>1.1. 语贝平台：指由杭州智宇未来数字技术有限公司（以下简称“智宇未来”）创建和运营的，旨在提供虚拟人物创建和互动的产品（以下简称“语贝”）。</p>
    <p>1.2. 职业认证服务：指用户在语贝平台内创建虚拟人物（以下简称“E人”）时，根据语贝的要求提交职业信息，并由语贝进行审核的服务。</p>
    <p>1.3. 第三方审核机构：指语贝委托对用户进行职业认证审核的第三方机构。</p>
    <p>1.4. 认证信息：指用户为完成职业认证所提交的所有资料和信息，包括但不限于职业资质证明、身份信息等。</p>
    <h2 id="2"><strong>2. 职业认证服务</strong></h2>
    <p>2.1. <strong>【申请审核】</strong> 用户可以根据自身需求，在语贝平台内创建E人并向语贝提交职业认证申请。</p>
    <p>2.2.<strong>【资料和信息提交】</strong> 用户申请职业认证需要按照提示在线提交有关资料和信息。用户在申请审核前应仔细阅读并接受本协议，用户实际使用本服务、提交资料和信息、付款等行为均视为用户已阅读并同意受本协议的约束。</p>
    <p>2.3.<strong>【审核能力】</strong> 用户理解并同意，职业认证服务仅限于在语贝的合法权限和合理能力范围内对用户提交的资料及信息进行合理、谨慎的形式审核，语贝无法实质审查用户的实际职业行为，并不为此提供任何承诺或担保。</p>
    <p>2.4.<strong>【审核内容】</strong> 用户理解并同意，语贝有权自行或委托第三方审核机构甄别核实包括但不限于以下内容：</p>
    <p>（1）用户是否拥有合法职业资质，以及是否取得相应的资质、权利或授权；</p>
    <p>（2）用户提交的职业信息的真实性；</p>
    <p>（3）其他语贝或第三方审核机构认为需要核实的内容。</p>
    <p>2.5.【<strong>资料和信息变更</strong>】您应积极配合我们及第三方审核机构的审核需求，并有权了解、查询审核进度。您向我们或者第三方审核机构提供的资料和信息如有变更的，应当及时采取以下措施：</p>
    <p>（1）如处于审核过程中的资料和信息发生变更，您应立即通知我们或负责审核的第三方审核机构并提交更新后的有关资料及信息；</p>
    <p>（2）如审核成功后资料和信息发生变更，您应及时提出变更申请并提交更新后的有关资料及信息；</p>
    <p>（3）如审核成功后我们发现资料和信息存在错误，您应按照我们的要求及时更正有关资料及信息。</p>
    <p>如您未能及时采取以上措施的，我们有权依照本协议约定对您进行相关处理（包括但不限于中止/终止部分或全部语贝服务），如因此审核失败的，您应当自行承担审核失败的不利后果。</p>
    <p>2.6. 您知悉并同意，只有当您的职业认证有效时，才可进一步开展其他语贝E人运营活动。如果我们中止/终止向您提供部分或全部语贝服务，您开展前述商业活动也将受到不同程度的影响，相应的不利后果均由您承担。如其他商业活动涉及费用的，我们将根据前述商业活动中与您的约定或规则规定进行处理。</p>
    <h2 id="3"><strong>3. 审核费用</strong></h2>
    <p>3.1.<strong>【费用标准】</strong> 用户理解并同意，语贝会根据用户的审核申请向用户收取相应的审核费用。每一件认证申请收取一次费用，具体收费标准以用户提交申请时申请页面公示为准。</p>
    <p>3.2.<strong>【费用支付】</strong> 用户理解并且同意，本协议项下费用的支付方式以相应费用支付页面提供及说明为准，语贝有权委托其关联公司代为向用户收取费用。</p>
    <h2 id="4"><strong>4. 信息与知识产权</strong></h2>
    <p>4.1.<strong>【资料和信息的保证】</strong> 用户声明并保证，用户提供给语贝或第三方审核机构的所有资料和信息为本人真实有效信息，且保证合法性、准确性，不侵犯第三方合法权利。</p>
    <p>4.2.<strong>【资料和信息的授权】</strong> 用户同意授权语贝将用户提交的资料和信息委托第三方审核机构进行审核，第三方审核机构有权出于审核之必要查阅、使用及保存上述资料及信息。</p>
    <p>您理解并同意我们有权根据法律、法规或监管部门的要求，将您提交的资料和信息递交至相关政府部门或其委托的企业进行审核、备案。</p>
    <p>4.3.<strong>【资料和信息的保存和使用】</strong>在本协议许可的范围内，我们及第三方审核机构会对相关资料及信息采用专业加密存储与传输方式进行传输及使用，以保障您的信息安全。您理解并同意，为履行本协议，我们有权保存您的相关信息，供的联系方式与您进行沟通。</p>
    <p>4.4.<strong>【资料和信息的公示】</strong>您确认并同意，为包括但不限于您的注册信息等。您理解并同意，我们可以基于本协议服务及其他现存或潜在的合作机会， 通过您提便于其他用户知悉您的E人信息， 我们有权公开您所提供的全部或部分信息，该信息的公开不会涉及到您的商业机密或秘密。</p>
    <p>4.5.<strong>【资料和信息的补充】</strong>您理解并同意，您提交的资料和信息是做出审核结果及为您职业认证服务的重要依据，任何资料和信息的变更将可能影响审核结果，您应当在资料和信息变更时及时重新提交审核申请，否则我们有权依照本协议约定对您的账号进行相关处理。</p>
    <h2 id="5"><strong>5. 违约及处理</strong></h2>
    <p>5.1.<strong>【处理措施】</strong> 用户违反本协议的，我们有权独立判断并视情况采取措施，包括但不限于责令用户补充申请资料、责令用户修改E人信息、责令用户重新申请审核、禁止用户发布E人、删除用户发布的E人、限制/关闭账号部分或全部功能等、收回账号所得收益等。</p>
    <p>5.2.<strong>【违约赔偿】</strong> 因用户违反本协议，引起第三方投诉或诉讼、索赔等的，用户应当自行处理并承担由此引起的一切风险、损失及法律责任。</p>
    <h2 id="6"><strong>6. 协议变更与通知</strong></h2>
    <p>6.1.<strong>【协议变更】</strong>我们有权因法律法规变化或运营调整等因素，不时修改、补充本协议，修改后的协议、补充协议等（下称“变更事项”）将按本协议约定的方式通知您。如您不同意变更事项，您有权于变更事项的生效日前联系我们反馈意见。如反馈意见得以采纳，我们将酌情调整变更事项。</p>
    <p><strong>如您对已生效的变更事项仍不同意的，您应当于变更事项的生效之日前停止使用语贝E人服务，变更事项对您不产生效力；如您在变更事项生效后仍继续使用语贝E人服务的，则视为您同意已生效的变更事项。</strong></p>
    <p><strong>为免歧义，如您为首次签署本协议，则本协议在您按照【提示条款】的说明表示接受时即生效，不受限于协议版本更新的生效时间。</strong></p>
    <p>6.2.<strong>【通知】</strong>您同意我们通过以下任一合理的方式向您送达各类通知：</p>
    <p>（1）站内信、系统消息推送、私信、弹窗等；</p>
    <p>（2）根据用户预留的联系方式发出的电子邮件、短信、函件等。</p>
    <h2 id="7"><strong>7. 法律适用、管辖与其他</strong></h2>
    <p>7.1.<strong>【法律适用】</strong> 本协议的订立、生效、履行、解释、修订、终止及纠纷解决，适用中华人民共和国法律。</p>
    <p>7.2.<strong>【管辖】</strong> 若用户和我们发生争议，由双方协商解决。协商不成的，任一方均可向被告所在地人民法院提起诉讼。</p>
    <p>7.3.<strong>【可分性】</strong> 本协议任一条款被视为废止、无效或不可执行，该条应视为可分的且并不影响本协议其余条款的有效性及可执行性。</p>`;
    const transHtml = html
        .replaceAll('<strong', '<strong style="font-weight: bold;"')
        .replaceAll('<h2', '<h2 style="margin:20px 0 10px;"')
        .replaceAll('<p', '<p style="margin:10px 0 5px;"');
    return (
        <Page className={styles.page}>
            <View className={styles.title}>语贝职业认证服务协议</View>
            <View className={styles.content}>
                <View dangerouslySetInnerHTML={{ __html: transHtml }}></View>
            </View>
        </Page>
    );
};

export default App;
