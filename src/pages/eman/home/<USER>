import { Page } from '@/components';
import { Button, Dialog, Icon, Image, NavBar, Popup } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import Taro, { getDeviceInfo, pxTransform, useDidShow, useRouter } from '@tarojs/taro';
// import { useMount } from 'ahooks';
import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { ChatMode } from '@/constants/voicetype';
import { createChat } from '@/services/chat';
import { getRandomScene } from '@/services/common';
import { downEMan, getAuditFailReason, getMyEManDetail, upEMan } from '@/services/eman';
import type { EManDetailVo } from '@/types/eman';
import emanJob from '@/utils/emanJob';
import classNames from 'classnames';
import { debounce } from 'lodash';
import { useEffect, useState } from 'react';
import styles from './index.less';
const Dialog_ = Dialog.createOnlyDialog();

const icon1 = `${config.cdnPrefix}eman/1.png`;
const icon2 = `${config.cdnPrefix}eman/2.png`;
const icon3 = `${config.cdnPrefix}eman/3.png`;
const up = `${config.cdnPrefix}eman/up.png`;
const up1 = `${config.cdnPrefix}eman/up1.png`;
const mark_3d = `${config.cdnPrefix}mark_3d.svg`;

const App = () => {
    const { params } = useRouter<{ id: string; type: string }>();
    const { id } = params; // eManId
    const [eManDetail, setEManDetail] = useState<EManDetailVo>({} as EManDetailVo); // 提示职业认证

    const [occupationPopup, changeOccupationPopup] = useState<boolean>(false); // 提示职业认证
    const [eManActionPopup, setEManActionPopup] = useState<boolean>(false); // 上下架
    const [isIOS, setIsIOS] = useState(false);

    useEffect(() => {
        const isIOS = getDeviceInfo().platform === 'iOS' || getDeviceInfo().platform === 'ios';
        setIsIOS(isIOS);
    }, []);
    // 上架
    const upFun = () => {
        setEManActionPopup(true);
    };
    const _getMyEManDetail = async () => {
        const res = await getMyEManDetail(id);
        setEManDetail(res.data.data);

        const IS_GROUNDING = Storage.get(StorageEnvKey.IS_GROUNDING);
        if (IS_GROUNDING == 'grounding') {
            upFun();
            Storage.set(StorageEnvKey.IS_GROUNDING, undefined);
        }
    };
    useDidShow(() => {
        _getMyEManDetail();
    });

    const onCancel = () => {
        changeOccupationPopup(false);
    };

    // 下架
    const downFu = () => {
        setEManActionPopup(true);
    };
    const _getAuditFailReason = async () => {
        const res = await getAuditFailReason(eManDetail.id);
        if (res.data.code === 200) {
            Dialog_.alert({
                title: '审核未通过',
                message: res.data.data,
                confirmButtonText: '关闭',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    };
    // 上下架弹窗确定按钮
    const onConfirm = async () => {
        setEManActionPopup(false);

        // 判断职业认证是否完成
        if (
            eManDetail.professionalCertificationDetail?.status !== 2 &&
            eManDetail.professionalCertificationDetail?.status !== 3
        ) {
            changeOccupationPopup(true);
        } else {
            if (eManDetail.status === 1 || eManDetail.status === 4) {
                const res = await upEMan(eManDetail.id);
                if (res.data.code === 200) {
                    Taro.showToast({
                        title: '提交成功',
                        icon: 'none'
                    });
                    _getMyEManDetail();
                } else {
                    Dialog_.alert({
                        title: '提示',
                        message: res.data.message,
                        confirmButtonText: '关闭',
                        confirmButtonColor: '#4F66FF'
                    }).then(() => {});
                }
            } else if (eManDetail.status === 2 || eManDetail.status === 3) {
                const text = eManDetail.status === 2 ? '撤销' : '下架';
                const res = await downEMan(eManDetail.id);
                if (res.data.code === 200) {
                    Taro.showToast({
                        title: `${text}成功`,
                        icon: 'none'
                    });
                    _getMyEManDetail();
                } else {
                    Dialog_.alert({
                        title: '提示',
                        message: res.data.message,
                        confirmButtonText: '关闭',
                        confirmButtonColor: '#4F66FF'
                    }).then(() => {});
                }
            }
        }
    };
    const showDescript = () => {
        if (eManDetail.skill && eManDetail.skill.length > 20) {
            Dialog_.alert({
                title: '擅长方向',
                message: eManDetail.skill,
                confirmButtonText: '关闭',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    };
    const noOpen = () => {
        Taro.showToast({
            title: '敬请期待',
            icon: 'none'
        });
    };
    const gotoCertification = () => {
        if (occupationPopup) changeOccupationPopup(false);
        Taro.navigateTo({
            url: `/pages/eman/certification/index?eid=${id}&emanId=${eManDetail.id}&certificationId=${
                eManDetail.professionalCertificationDetail?.id || ''
            }`
        });
    };
    const gotoCreateEMan = () => {
        Taro.navigateTo({
            url: `/pages/eman/characterStepOne/index?type=edit&occupation=${eManDetail.occupation}&id=${eManDetail.id}&status=${eManDetail.status}`
        });
    };

    const openSceneExercise = async () => {
        if (
            !eManDetail.professionalCertificationDetail ||
            eManDetail.professionalCertificationDetail.status === 4 ||
            eManDetail.professionalCertificationDetail.status === 1
        ) {
            changeOccupationPopup(true);
            return;
        }

        const res = await getRandomScene(eManDetail.occupation, eManDetail.title);
        /*  Taro.navigateTo({
            url: `/pages/chat/sceneExercise/index?emanId=${eManDetail.id}&sceneId=${res.data.data.id}&from=mine`
        }); */

        const { data } = await createChat({ emanId: eManDetail.id, sceneId: res.data.data.id });
        if (
            eManDetail.zipFileUrl &&
            eManDetail.show3dFlag &&
            (Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null)
        ) {
            Taro.navigateTo({
                url: `/pages/shuziren/index?chatId=${data.data?.id}&from=home&introductionType=${
                    data.data.introductionType
                }&introductionDelay=${data.data.introductionDelay}&introduction=${data.data.introduction || ''}`
            });
        } else {
            Taro.navigateTo({
                url: `/pages/chat/enterpriseIntelligence/index?chatId=${data.data?.id}&introductionType=${
                    data.data.introductionType
                }&introductionDelay=${data.data.introductionDelay}&introduction=${data.data.introduction || ''}`
            });
        }
    };

    return (
        <Page className={styles.page}>
            <Dialog_ />
            <View className={styles.container}>
                <View className={styles.background}>
                    <View
                        className={styles.avatar_image}
                        style={{
                            backgroundImage: `url(${eManDetail.background})`
                        }}
                    />
                    <View className={styles.empty} />
                </View>
            </View>
            <View className={styles.wrap}>
                <NavBar
                    safeAreaInsetTop
                    border={false}
                    onClickLeft={() => {
                        Taro.switchTab({
                            url: `/pages/mini/index`
                        });
                    }}
                    renderLeft={<Icon size={pxTransform(48)} name='cross' color='#ffffff' />}
                />
                <View className={styles.top_msg}>
                    <View className={styles.user_info}>
                        <View className={styles.avatar_box}>
                            <Image src={eManDetail.avatar} fit='cover' className={styles.avatar} />
                            {eManDetail.zipFileUrl && <Image src={mark_3d} className={styles.mark_3d} />}
                        </View>
                        <View className={styles.info}>
                            <View className={styles.name}>{eManDetail.name}</View>
                            <View className={styles.account}>ID：{eManDetail.number}</View>
                        </View>
                    </View>
                    <View className={styles.other_msg}>
                        {eManDetail && emanJob(eManDetail.occupation, eManDetail.title, eManDetail.department)}

                        <View className={styles.other_msg_item}>
                            <View className={styles.description} onClick={showDescript}>
                                {/* <View className={styles.detail} >
                                    详情
                                </View> */}
                                擅长方向: {eManDetail.skill}
                            </View>
                        </View>
                        <View className={styles.fans_container}>
                            <View className={styles.left}>
                                <View className={styles.item} onClick={noOpen}>
                                    <View className={styles.fansNum}>{eManDetail.followerNum}</View>
                                    <View className={styles.fans}>粉丝</View>
                                </View>
                                <View className={styles.item} onClick={noOpen}>
                                    <View className={styles.fansNum}>{eManDetail.chatNum}</View>
                                    <View className={styles.fans}>对话</View>
                                </View>
                            </View>
                            {(eManDetail.status === 1 || eManDetail.status === 4) && (
                                <View className={styles.up} onClick={upFun}>
                                    <Image src={up} fit='cover' width={pxTransform(32)} height={pxTransform(32)} />
                                    上架
                                </View>
                            )}
                            {eManDetail.status === 2 && (
                                <View onClick={downFu} className={styles.completed}>
                                    审核中
                                </View>
                            )}
                            {eManDetail.status === 3 && (
                                <View className={styles.completed} onClick={downFu}>
                                    <Image src={up1} fit='cover' width={pxTransform(32)} height={pxTransform(32)} />
                                    已上架
                                </View>
                            )}
                        </View>
                    </View>
                </View>
                <View className={styles.bottom_msg}>
                    {eManDetail.status === 4 && (
                        <View className={styles.refuse_container}>
                            <Icon name='warning-o' color='#FF8F28' size={pxTransform(32)} />
                            <View className={styles.refuse_text} onClick={_getAuditFailReason}>
                                您的E人审核未通过,点击 <span className={styles.detail}>查看详情&gt;&gt;</span>
                            </View>
                        </View>
                    )}

                    <View className={styles.bottom_msgTitle}>E人训练</View>
                    <View className={styles.bottom_msg_content}>
                        <View className={styles.bottom_msg_content_item} onClick={gotoCreateEMan}>
                            <View className={styles.left}>
                                <Image src={icon1} fit='cover' width={pxTransform(45)} height={pxTransform(45)} />
                                <View className={styles.bottom_msg_content_text}>基础人设</View>
                            </View>
                            <View className={styles.right}>
                                <View className={styles.bottom_msg_contentStatus}>已完成</View>
                                <Icon size={pxTransform(28)} name='arrow' color='#C3C1C1' />
                            </View>
                        </View>
                        <View className={styles.bottom_msg_content_item} onClick={gotoCertification}>
                            <View className={styles.left}>
                                <Image src={icon2} fit='cover' width={pxTransform(45)} height={pxTransform(45)} />
                                <View className={styles.bottom_msg_content_text}>
                                    <span>职业认证</span>
                                    <span className={styles.tips}>上架或对话需完成职业认证</span>
                                </View>
                            </View>
                            <View className={styles.right}>
                                <View
                                    className={
                                        (styles.bottom_msg_contentStatus,
                                        eManDetail.professionalCertificationDetail?.status !== 3
                                            ? styles.no_completed
                                            : '')
                                    }
                                >
                                    {!eManDetail.professionalCertificationDetail && '待完成'}
                                    {eManDetail.professionalCertificationDetail?.status === 1 && '待审核'}
                                    {eManDetail.professionalCertificationDetail?.status === 2 && '审核中'}
                                    {eManDetail.professionalCertificationDetail?.status === 3 && '已完成'}
                                    {eManDetail.professionalCertificationDetail?.status === 4 && '待修改'}
                                </View>
                                <Icon size={pxTransform(28)} name='arrow' color='#C3C1C1' />
                            </View>
                        </View>
                        <View className={styles.bottom_msg_content_item} onClick={noOpen}>
                            <View className={styles.left}>
                                <Image src={icon3} fit='cover' width={pxTransform(45)} height={pxTransform(45)} />
                                <View className={styles.bottom_msg_content_text}>记忆库</View>
                            </View>
                            <View className={styles.right}>
                                <View className={(styles.bottom_msg_contentStatus, styles.no_completed)}>待完成</View>
                                <Icon size={pxTransform(28)} name='arrow' color='#C3C1C1' />
                            </View>
                        </View>
                    </View>
                </View>
                <View
                    className={classNames(styles.bottom, {
                        [styles.bottom_ios]: isIOS
                    })}
                >
                    {/* 1  待审核和 4 已驳回 */}
                    <Button
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                        onClick={openSceneExercise}
                        block
                        round
                    >
                        前往对话
                    </Button>
                </View>
            </View>
            <Popup
                position='bottom'
                round
                style={{
                    '--popup-close-icon-size': pxTransform(48),
                    '--popup-close-icon-color': '#272C47',
                    '--popup-close-icon-margin': pxTransform(48),
                    right: pxTransform(32)
                }}
                safeAreaInsetBottom
                show={occupationPopup}
                closeable
                closeIcon='cross'
                onClose={onCancel}
            >
                <View className={styles.popup_title}>请先完成以下认证</View>
                <View className={styles.popup_item} onClick={gotoCertification}>
                    <View className={styles.bottom_msg_content_item}>
                        <View className={styles.left}>
                            <Image src={icon2} fit='cover' width={pxTransform(45)} height={pxTransform(45)} />
                            <View className={styles.bottom_msg_content_text}>
                                <span>职业认证</span>
                                <span className={styles.tips}>上架或对话需完成职业认证</span>
                            </View>
                        </View>
                        <View className={styles.right}>
                            <View className={(styles.bottom_msg_contentStatus, styles.no_completed)}>
                                {!eManDetail.professionalCertificationDetail && '待完成'}
                                {eManDetail.professionalCertificationDetail?.status === 1 && '待审核'}
                                {eManDetail.professionalCertificationDetail?.status === 4 && '待修改'}
                            </View>
                            <Icon size={pxTransform(28)} name='arrow' color='#C3C1C1' />
                        </View>
                    </View>
                </View>
            </Popup>
            <Popup
                position='bottom'
                round
                style={{
                    '--popup-close-icon-size': pxTransform(48),
                    '--popup-close-icon-color': '#272C47',
                    '--popup-close-icon-margin': pxTransform(48),
                    right: pxTransform(32)
                }}
                safeAreaInsetBottom
                show={eManActionPopup}
                closeable
                closeIcon='cross'
                onClose={() => setEManActionPopup(false)}
            >
                {/* 未上架 */}
                {(eManDetail.status === 1 || eManDetail.status === 4) && (
                    <View className={styles.popup_title}>确认上架吗？</View>
                )}
                {/* 审核中 */}
                {eManDetail.status === 2 && <View className={styles.popup_title}>确认撤销审核吗？</View>}
                {/* 已完成 */}
                {eManDetail.status === 3 && <View className={styles.popup_title}>确认下架吗？</View>}
                {(eManDetail.status === 1 || eManDetail.status === 4) && (
                    <View className={styles.action_tips}>审核通过后将会公开至市场，将根据用户使用情况获得收益~</View>
                )}

                <View className={styles.actions}>
                    <Button
                        onClick={debounce(() => {
                            onConfirm();
                        }, 500)}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        round
                        block
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        确定
                    </Button>
                    <Button
                        onClick={() => setEManActionPopup(false)}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96),
                            color: '#777777'
                        }}
                        round
                        block
                        color='#F6F6F6'
                    >
                        取消
                    </Button>
                </View>
            </Popup>
        </Page>
    );
};

export default App;
