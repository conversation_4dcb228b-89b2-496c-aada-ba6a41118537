@import '@/styles/index.less';

page {
  --nav-bar-background-color: transparent;

  box-sizing: border-box;
  position: relative;
}

.container {
  position: relative;
  overflow: hidden;
}

.background{
  position: relative;
  z-index: 1;
}
.avatar_image{
  width: 100%;
  height:600px;
  background-position: top;
  background-size: cover;
  background-repeat: no-repeat;
}


.empty{
  width: 100%;
  height: 50px;
}
.wrap{
  width: 100%;
  position: absolute;
  z-index: 2;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(40px);
  left: 0;
  display: flex;
  flex-direction: column;
  top: 0;
}
.top_msg{
  width: 100%;
  box-sizing: border-box;
  padding:32px;
  color: #fff;
  margin-top: 30px;
  font-size: 24px;
}

.user_info{
  display: flex;
  align-items: center;
  gap:0 24px;
  .avatar_box {
    width: 150px;
    height: 150px;
    position: relative;
  }
  .avatar{
    width: 100%;
    height: 100%;
    border-radius: 29px;
    overflow: hidden;
  }
  .mark_3d {
    position: absolute;
    z-index: 1;
    display: block;
    width: 32px;
    height: 32px;
    right:15px;
    bottom: 15px;
    border-radius:32px;
  }
  .info{
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 12px 0;
  }
  .name{
    font-size: 40px;
  }
  .account{
    opacity: 0.7;
  }


}
.other_msg{
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap:8px 0;
  .other_msg_item{
    width: 100%;
    line-height: 32px;
  }
  .description{
    position: relative;
    .multi-ellipsis(2);
  }
//   .description::before{
//     content: '';
//     float: right;
//     height: 100%;
//     margin-bottom: -32px;
//   }
//   .description::after {
//     content: '';
//     width: 100%;
//      background-color: rgba(0, 0, 0, 0);
//       backdrop-filter: blur(40px);
//     height: 100%;
//     position: absolute;
//     // background: #fff;
// }
  .detail{
    float: right;
    clear: both;
    // background-color: aqua;
  
  }

  .fans_container{
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
    margin-top: 42px;
    height: 64px;
 
  }
  .left{
    display: flex;
    opacity: 0.7;
    gap: 0 32px;

    .item{
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap:6px 0;
    }
  }
  .up{
    font-size: 24px;
    width: 155px;
    border-radius: 107px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap:0 4px;
    background-color: #fff;
    color: #272C47;
  }
  .completed{
    font-size: 24px;
    width: 155px;
    border-radius: 107px;
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap:0 4px;
    border: 1px solid #fff;
    color: #fff;
  }
}
.bottom_msg{
  flex:1;
  width: 100%;
  border-radius: 32px 32px 0 0;
  background: #fff;
  box-sizing: border-box;
  padding: 32px;
  letter-spacing: -1px;
}

.bottom_msgTitle{
  font-size: 36px;
  font-weight: 500;
  margin-bottom: 41px;
}
.bottom_msg_content_item{
  display: flex;
  background-color: #F6F6F6;
  justify-content: space-between;
  padding: 32px;
  box-sizing: border-box;
  font-size: 32px;
  border-radius: 24px;
  margin-bottom: 24px;
}
.right {
  display: flex;
  align-items: center;
  gap: 0 26px;
}
.left{
  display: flex;
  align-items: flex-start;
  gap: 0 24px;
}
.bottom_msg_content_text{
  display: flex;
  flex-direction: column;
  gap:10px 0;
}
.tips{
  color: #9597a0;
  font-size: 28px;
}
.no_completed{
  color: #9597A0;
}

.bottom {
  .footer-fixed;

  box-sizing: border-box;
  padding: 21px 36px 0px 36px;
  bottom: 36px;
  z-index: 100;
  background-color: #fff;
  &_ios {
    bottom: constant(safe-area-inset-bottom);
    bottom: env(safe-area-inset-bottom);
  }
}
.popup_title {
  text-align: center;
  font-size: 40px;
  font-weight: 600;
  margin-top: 48px;
}

.popup_item{ 
  padding: 32px;
  box-sizing: border-box;
}
.actions{
  padding:22px 62px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap:24px 0;
}
.action_tips{
  padding: 32px 87px;
  box-sizing: border-box;
  text-align: center;
  color: #9597A0;
  font-size: 32px;
  line-height: 45px;
}
.refuse_container{
  // height: 80px;
  padding:24px 30px;
  display: flex;
  align-items: center;
  gap:0 18px;
  background: #FFFBE8;
  border-radius: 16px;
  margin-bottom: 32px;
}
.refuse_text{
  display: flex;
  color: #323233;
  letter-spacing: 0;
  .detail{
    color: #FF8F28;
  }
}
