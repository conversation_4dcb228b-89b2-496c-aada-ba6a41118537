import { Storage } from '@/common';
import { Page } from '@/components';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { getRandomName } from '@/services/common';
import { createEMan, getMyEMan, getMyEManDetail, updateEMan } from '@/services/eman';
import type { EManDetailVo } from '@/types/eman';
import { ActionSheet, Form, FormItem, Icon, Image, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Input, Textarea, View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import classNames from 'classnames';
import { useCallback, useEffect, useRef, useState } from 'react';
import FormAvatar from '../components/formAvatar';
import FormPersonality from '../components/formPersonality';
import FormVoice from '../components/formVoice';
import styles from './index.less';

const IconRefresh = `${config.cdnPrefix}eman/icon_refresh.svg`;
const App = () => {
    const initialValues = {
        name: '',
        personality: '',
        skill: '',
        tone: '',
        personalityArray: []
    };
    const [detail, setdetail] = useState<EManDetailVo & { personalityArray: string[] }>();
    const [title, setTitle] = useState('');
    const status = useRef<number>(0);
    const { params } = useRouter<{ type: 'create' | 'edit'; occupation: string; id?: string }>();
    const formIt = Form.useForm();
    const [showActionSheet, setShowActionSheet] = useState(false);

    const [submitDisabled, setSubmitDisabled] = useState<boolean>(true);

    const [avatarObj, setAvatarObj] = useState<{
        avatar: string;
        background: string;
        gender: number;
        zipFileUrl: string;
        videoUrl: string;
    }>({
        avatar: '',
        background: '',
        zipFileUrl: '',
        videoUrl: '',
        gender: 0
    });
    const _getEManDetail = useCallback(async () => {
        const res = await getMyEManDetail(params.id as string);
        const { data } = res.data;
        setdetail({
            ...data,
            personalityArray: data.personality.split(',')
        });

        status.current = data.status;
        setAvatarObj({
            ...avatarObj,
            avatar: data.avatar,
            background: data.background,
            zipFileUrl: data.zipFileUrl,
            videoUrl: data.videoUrl,
            gender: data.gender
        });
        Taro.nextTick(() => {
            formIt.setFields({
                name: data.name,
                personality: data.personality,
                skill: data.skill
            });
            formIt.setFieldsValue('personalityArray', data.personality.split(','));
            formIt.setFieldsValue('tone', data.tone);
        });
    }, [params.id]);

    useEffect(() => {
        if (params.type === 'create') {
            setTitle('创建E人');
            formIt.setFieldsValue('personalityArray', []);
            formIt.setFieldsValue('name', '');
            formIt.setFieldsValue('tone', '');
        } else if (params.type === 'edit') {
            setTitle('基础人设');
            _getEManDetail();
        }
    }, [params, _getEManDetail]);

    const ruleName = {
        rule: (values: string, callback: (errMess: string) => void) => {
            const regex = /^[a-zA-Z\u4e00-\u9fa5]+$/;
            if (!regex.test(values)) {
                callback(`支持2-12字，不要使用数字或特殊符号`);
            }
            if (values.length > 12 || values.length < 2) {
                callback(`支持2-12字，不要使用数字或特殊符号`);
            }
        }
    };

    const ruleSkill = {
        rule: (values: string, callback: (errMess: string) => void) => {
            if (values.length > 100) {
                callback(`文字太多啦${values.length}/100`);
            }
        }
    };

    const handleSubmit = () => {
        if (avatarObj.avatar === '') {
            Taro.showToast({ title: '请选择头像', icon: 'none' });
            return;
        }
        formIt.validateFields(async (errorMessage, fieldValues: any) => {
            console.log(fieldValues);
            if (errorMessage && errorMessage.length) {
                return console.info('errorMessage', errorMessage);
            }
            // fieldValues.tone =
            const param = {
                ...fieldValues,
                ...avatarObj,
                occupation: decodeURIComponent(params.occupation),
                personality: fieldValues.personalityArray.join(',')
            };
            if (params.type === 'edit') {
                if ([2, 3].includes(status.current)) {
                    Taro.showToast({
                        title: '请先下架E人或撤销E人审核',
                        icon: 'none'
                    });
                    return;
                }

                const res = await updateEMan({ ...param, id: params.id as string });
                if (res.data.code === 200) {
                    Taro.showToast({ title: '修改成功' });
                    setTimeout(() => {
                        getMyEMan().then((res) => {
                            const { data } = res;
                            Taro.navigateTo({
                                url: `/pages/eman/home/<USER>
                            });
                            Storage.set(StorageEnvKey.IS_GROUNDING, 'grounding');
                        });

                        // Taro.navigateBack({ delta: 2 });
                    }, 2000);
                }
            } else if (params.type === 'create') {
                console.log(formIt.getFieldValue('tone)'));
                const res = await createEMan(param);
                if (res.data.code === 200) {
                    Taro.showToast({ title: '创建成功' });
                    setTimeout(() => {
                        getMyEMan().then((res) => {
                            const { data } = res;
                            Taro.navigateTo({
                                url: `/pages/eman/home/<USER>
                            });
                            Storage.set(StorageEnvKey.IS_GROUNDING, 'grounding');
                        });

                        // Taro.navigateBack({ delta: 2 });
                    }, 2000);
                }
            }
        });
    };

    const goBack = () => {
        Taro.navigateBack();
    };

    const handleBack = () => {
        if (![2, 3].includes(status.current) && params.type === 'edit') {
            let bool = false;
            Object.keys(initialValues).forEach((key) => {
                if (
                    formIt.getFieldValue(key) &&
                    JSON.stringify(formIt.getFieldValue(key)) !== JSON.stringify(detail[key])
                ) {
                    bool = true;
                    return;
                }
            });
            if (bool) {
                // 修改过但未保存
                setShowActionSheet(true);
            } else {
                Taro.navigateBack();
            }
        } else if (params.type === 'create') {
            let bool = false;
            Object.keys(initialValues).forEach((key) => {
                if (
                    JSON.stringify(formIt.getFieldValue(key)) !== JSON.stringify('') &&
                    JSON.stringify(formIt.getFieldValue(key)) !== 'null' &&
                    JSON.stringify(formIt.getFieldValue(key)) !== '[]'
                ) {
                    bool = true;
                    return;
                }
            });
            if (avatarObj.avatar !== '') {
                bool = true;
            }
            if (bool) {
                // 修改过但未保存
                setShowActionSheet(true);
            } else {
                Taro.navigateBack();
            }
        } else {
            goBack();
        }
    };
    const randomNickname = async () => {
        // 设置随机昵称
        const res = await getRandomName();
        formIt.setFieldsValue('name', res.data.data);
    };
    const avatarConfirm = (e: any) => {
        setAvatarObj({
            ...avatarObj,
            ...e
        });
        const allValues = formIt.getFieldsValue();
        console.log(avatarObj, allValues);
        if (
            allValues.name &&
            allValues.skill &&
            allValues.personalityArray &&
            allValues.personalityArray.length > 0 &&
            allValues.tone
        ) {
            setSubmitDisabled(false);
        } else {
            setSubmitDisabled(true);
        }
    };

    const handleResetVoice = () => {
        formIt.setFieldsValue('tone', '');
    };
    const handleResetAvatar = (gender: number) => {
        setAvatarObj({
            ...avatarObj,
            avatar: '',
            background: '',
            gender
        });
    };
    const handleResetGender = (gender: number) => {
        setAvatarObj({
            ...avatarObj,
            gender
        });
    };
    // Taro.enableAlertBeforeUnload({
    //     message: '操作未保存，确定退出吗？'
    // });

    const onChange = (_: any, allValues: any) => {
        console.log('change', avatarObj.avatar, allValues);
        if (
            allValues.name &&
            allValues.skill &&
            allValues.personalityArray &&
            allValues.personalityArray.length > 0 &&
            allValues.tone
        ) {
            setSubmitDisabled(false);
        } else {
            setSubmitDisabled(true);
        }
    };
    return (
        <Page className={styles.page}>
            <NavBar className={styles.navbar} title={title} leftArrow safeAreaInsetTop onClickLeft={handleBack} />
            <View className={styles.page_content}>
                {[2, 3].includes(status.current) && (
                    <View className={styles.page_tips_info}>
                        <Icon name='info-o' size={pxTransform(32)} color='#4C82FB' className='icon' />
                        <View className={styles.t1}>如需修改，请先下架E人或撤销E人审核</View>
                    </View>
                )}
                <View className={styles.form}>
                    <FormAvatar
                        onConfirm={(e: any) => avatarConfirm(e)}
                        value={avatarObj}
                        status={status.current}
                        oldGender={avatarObj.gender}
                        tone={formIt.getFieldValue('tone')}
                        handleReset={handleResetVoice}
                    />
                    <Form
                        initialValues={initialValues}
                        form={formIt}
                        onFinish={(errs, res) => console.info(errs, res)}
                        onChange={onChange}
                    >
                        <FormItem
                            label='昵称'
                            name='name'
                            required
                            rules={ruleName}
                            requiredIcon
                            trigger='onInput'
                            validateTrigger='onBlur'
                            // taro的input的onInput事件返回对应表单的最终值为e.detail.value
                            valueFormat={(e) => e.detail.value}
                            renderRight={
                                ![2, 3].includes(status.current) && (
                                    <View
                                        onClick={randomNickname}
                                        style={{ height: '100%', display: 'flex', alignItems: 'center' }}
                                    >
                                        <Image className={styles.icon_right} src={IconRefresh} fit='contain' />
                                    </View>
                                )
                            }
                            className={styles.form_item}
                            labelClassName={styles.label}
                        >
                            <Input
                                readOnly={[2, 3].includes(status.current)}
                                className={styles.input}
                                placeholder='随机昵称'
                                placeholderClass={styles.placeholder}
                            />
                        </FormItem>
                        <FormItem
                            label='性格'
                            name='personalityArray'
                            labelClassName={styles.label}
                            required
                            valueKey='value'
                            valueFormat={(e) => e}
                            trigger='onConfirm'
                        >
                            <FormPersonality status={status.current} />
                        </FormItem>
                        <FormItem
                            label='声音'
                            name='tone'
                            labelClassName={styles.label}
                            required
                            valueKey='value'
                            valueFormat={(e) => e}
                            trigger='onConfirm'
                        >
                            <FormVoice
                                avatar={avatarObj.avatar}
                                status={status.current}
                                oldGender={avatarObj.gender}
                                handleReset={(gender: any) => handleResetAvatar(gender)}
                                handleGender={(gender: any) => handleResetGender(gender)}
                            />
                        </FormItem>
                        <FormItem
                            label='擅长方向'
                            name='skill'
                            layout='vertical'
                            required
                            rules={ruleSkill}
                            requiredIcon
                            trigger='onInput'
                            validateTrigger='onBlur'
                            // taro的input的onInput事件返回对应表单的最终值为e.detail.value
                            valueFormat={(e) => e.detail.value}
                            className={styles.form_item}
                            labelClassName={styles.label}
                        >
                            <Textarea
                                cursorSpacing={80}
                                readOnly={[2, 3].includes(status.current)}
                                className={classNames(styles.input, styles.input_skill)}
                                maxlength={200}
                                autoHeight
                                placeholder='如：睑腺炎、睑板腺囊肿、泪囊炎、近视、倒睫、泪道阻塞、结膜炎、干眼、眨眼、飞蚊症、结膜肿物、眼脸肿物'
                                placeholderClass={classNames(styles.placeholder, styles.placeholder_textarea)}
                            />
                        </FormItem>
                    </Form>
                </View>
            </View>
            <View className={styles.bottom}>
                {/* {formIt.getFieldsValue().name} */}
                <Button
                    onClick={handleSubmit}
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    disabled={submitDisabled}
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                >
                    {params.type === 'edit' ? '保存' : '完成'}
                </Button>
                <View className={styles.step}>2/2</View>
            </View>

            <ActionSheet
                show={showActionSheet}
                zIndex={110}
                onClose={() => setShowActionSheet(false)}
                title='操作未保存，确定退出吗？'
                closeOnClickOverlay={false}
                // @ts-ignore
                style={{ '--action-sheet-header-height': pxTransform(124) }}
            >
                <Button.Group className={styles.actionSheet_group} direction='vertical'>
                    <Button block round color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)' onClick={goBack}>
                        确定
                    </Button>
                    <Button
                        block
                        round
                        color='#F6F6F6'
                        style={{ color: '#777777' }}
                        onClick={() => setShowActionSheet(false)}
                    >
                        取消
                    </Button>
                </Button.Group>
            </ActionSheet>
        </Page>
    );
};

export default App;
