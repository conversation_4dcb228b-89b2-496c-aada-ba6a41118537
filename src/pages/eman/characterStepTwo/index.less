page {
  background: #F3F3F3;

  --nav-bar-background-color: transparent;
  --nav-bar-icon-color: #333;
  --nav-bar-arrow-size: 48px;
}
:global {
  .vant-form-formItem-controll-item{
    background-color: #ffffff;
  }
  .vant-form-formItem-horizontal {
   height: 108px;
   padding: 0;
   display: flex;
   align-items: center;
   .vant-form-formItem-controll{
    height: 100%;
    margin-right: 0;
    .vant-form-formItem-controll-item{
      height: 100%;
      flex-direction: row;
    }
   }
  }
  .vant-form-message-show {
    line-height: 1;
  }
  .vant-form-formItem-wrapper {
    border-radius: 24px;
    margin-bottom: 40px;
  }
  .vant-form-required-box {
    display: none;
  }
  .vant-form-message {
    text-align: right;
  }
  .vant-form-formItem-vertical {
    .vant-form-formItem-controll {
      margin-left: 0;
    }
  }
}
.navbar {
  &::after {
    border: none;
  }
}
.page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  box-sizing: border-box;
}
.page_content {
  flex: 1;
  height: 0;
  overflow-y: auto;
  box-sizing: border-box;
  // margin-bottom: 100px + 21px + 38px + 28px + 24px + 80px;
}
.bottom {
  position: fixed;
  bottom: calc(10px + constant(safe-area-inset-bottom));
  bottom: calc(10px + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  z-index: 1;
  padding: 21px 36px 0 36px;
}
.step {
  color: #9597A0;
  font-size: 24px;
  text-align: center;
  margin-top: 28px;
}
.icon_right_container{
  display: flex;
  height: 100%;
  align-items: center;

}
.icon_right {
  width: 40px;
  height: 40px;
  padding-left: 13px;
}
.form {
  margin-left: 32px;
  margin-right: 32px;
  margin-bottom: calc(200px + constant(safe-area-inset-bottom));
  margin-bottom: calc(200px + env(safe-area-inset-bottom));
}
.form_item {
  margin: 0 32px;
  overflow: visible;
}
.label {
  font-size: 32px;
  color: #272C47;
  font-weight: 400;
}
.input {
  flex: 1;
  font-size: 32px;
  // display: flex;
  // align-items: center;
  // justify-self: flex-end;
text-align: right;
  &_skill {
    text-align: left;
    min-height: 125px;
    background-color: #ffffff;
    color: #272C47;
    display: block;
  }
}
.placeholder {
  font-size: 32px;
  color: #9597A0;

  &_textarea {
    color: #CCCED7;
  }
}

.avatar_label {
  display: none;
}
.avatar_controll {
  margin-left: 0;
}
.avatar_form_item {
  background-color: transparent;
  margin: 0;
  padding: 0;
}
.avatar_form {
  background-color: transparent;
}
.page_tips_info {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  box-sizing: border-box;
  width: 686px;
  height: 80px;
  margin: 28px auto;
  padding: 20px 30px;
  font-size: 28px;
  background: #e6eeff;
  border-radius: 32px;
  .t1 {
      margin-left: 15px;
  }
}
.textarea{
  padding-bottom: 300px;
}