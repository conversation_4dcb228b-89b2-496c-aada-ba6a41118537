@import '@/styles/index.less';
@import './assets/style.less';

page {
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%),
        linear-gradient(158deg, #cff7f4 0%, #d1ebff 12%, #fff 35%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
    --dropdown-menu-background-color: transparent;
    --dropdown-menu-title-font-size: 24px;
    --dropdown-menu-height: 56px;
    --tabs-bottom-bar-width: 54px;
    :global {
        .van-tab--active {
            font-weight: bold;
        }
        .van-tabs__scroll {
            background: none;
        }
        .van-dropdown-menu {
            gap: 26px;
            padding-bottom: 18px;
        }
        .van-dropdown-menu__item {
            padding: 0 20px;
            border: 2px solid rgba(39, 44, 71, 0.2);
            border-radius: 16px;
        }

        .van-dropdown-menu__title {
            flex: 1;
            padding-left: 0;
            &::after {
                right: 0;
                width: 12px;
                height: 12px;
                border: 2px solid #272c47;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                // 向下的三角箭头
                content: '';
            }
        }
        .van-dropdown-menu__title--active {
            color: #333;
            &::after {
                transform: rotate(-135deg);
            }
        }
        .van-tab--active-line {
            background: #4f66ff;
          
        }
       
        .at-calendar__list.flex .flex__item--selected {
            background-color: #4f66ff !important;
        }
    }
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.history_icon{
    width: 48px;
    height: 48px;
}
.content {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-top: 20px;
    margin-bottom: 20px;
    overflow-y: auto;
  }
  .card {
    background-color: #fff;
    border-radius: 24px;
    margin: 28px 32px 0;
    padding: 32px  32px 32px 22px;
    box-sizing: border-box;
  }
  .checkpoint_item {
    display: flex;
  }
  .load_status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
    color: #9597a0;
    font-size: 26px;
  }
  
  

.points_empty{display: flex;
flex-direction: column;
justify-content: center;
position: relative;
align-items: center;
height: 100%; /* 确保占据整个容器高度 */}
.empty_icon{
//   margin-bottom: 20px;
width: 400px;
height: 205px;

}

.empty_text{
// margin-top: -20px;
font-size: 28px;
color: #9597A0;
}
.title_tab_item{
font-size: 34px;
font-weight: bold;
text-align: center;
color: #000000;
}
.login {
    width: 350px;
    margin: 40px auto;
}