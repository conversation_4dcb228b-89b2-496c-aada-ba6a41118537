import { Storage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import type { LevelStageItemVO } from '@/types/activity';
import { Image, pxTransform } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import { navigateBack, navigateTo } from '@tarojs/taro';
import PPTiconNew from './PPT_icon_New.png';
import styles from './styles.less';
export interface CheckPointItemProps extends LevelStageItemVO {
    departmentLine: number;
    onClick?: (id: string) => void;
    onDepartmentClick?: () => void;
    activeTab?: number;
}

const Index: React.FC<CheckPointItemProps> = (props: any) => {
    const { name, type, timeLimit, id, activeTab } = props;
    const newname = name.substring(0, 32);
    const goScriptList = () => {
        Storage.set(StorageEnvKey.Script_Type, activeTab);
        if (type === 3) {
            navigateTo({
                url: `/pages/practicePPT/detail/index?pptId=${id}&from=scriptList`
            });
        } else {
            navigateTo({
                url: `/pages/chat/sceneExercise/index?scriptId=${id}&from=scriptList`
            });
        }
    };
    return (
        <View className={styles.card} onClick={goScriptList}>
            <View className={styles.productList_style}>
                {type == 1 ? (
                    <View className={styles.productList_img}>
                        <Image
                            fit='cover'
                            width={pxTransform(100)}
                            height={pxTransform(100)}
                            src={`${config.cdnPrefix}x/group_573.png`}
                        />
                    </View>
                ) : type == 2 ? (
                    <View className={styles.productList_img}>
                        <Image
                            fit='cover'
                            width={pxTransform(100)}
                            height={pxTransform(100)}
                            src={`${config.cdnPrefix}x/group_572.png`}
                        />
                    </View>
                ) : (
                    <View className={styles.productList_img}>
                        <Image fit='cover' width={pxTransform(100)} height={pxTransform(100)} src={PPTiconNew} />
                    </View>
                )}

                <View>
                    <View className={styles.productList_productName}>{name.length > 33 ? `${newname}...` : name}</View>
                    <View className={styles.productList_text}>
                        <View className={styles.productList_type}>
                            {type == 1 ? '技巧类' : type == 2 ? '答题类' : '幻灯片演练'}
                        </View>
                        <View className={styles.productList_time}>{timeLimit}分钟</View>
                    </View>
                </View>
            </View>
        </View>
    );
};

export default Index;
