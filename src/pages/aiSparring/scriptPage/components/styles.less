 
.card {
  border-radius: 32px;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px 0px rgba(194, 205, 240, 0.2);
  box-sizing: border-box;
  margin:  24px 16px ;
  padding: 28px;
  position: relative;
  .productList_style{
    display: flex;
    align-items: center;
    justify-content: start
  }
   .productList_img{
    border-radius: 16px;
    margin-right: 24px;
    align-items: center;
    text-align: center;
    display: flex;
   
   }
   .productList_productName{
    display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2; /* 设置显示的行数 */
    margin-bottom: 10px;
    font-size: 32px;
    width: 520px;
    overflow: hidden;
    text-overflow: ellipsis;
    color: rgba(39, 44, 71, 1);
    font-weight: 600;
   }
   .productList_text{
    font-size: 24px;
    display: flex;
    text-align: center;

    align-items: center;
    justify-content: start;
    color: #9597A0;
    .productList_type{
      height: 43px;display: flex;
      text-align: center;
      align-items: center;
      margin-right: 16px;
      border-radius: 8px;
      padding:3px 12px   ;
      color: #4F66FF;
      background: rgba(79, 102, 255, 0.1);
    }
    .productList_time{
      text-align: center;    display: flex;
      align-items: center;
      height: 43px;
      margin-right: 16px;
      border-radius: 8px;
      padding:2px 6px   ;
    }
      }
      
  }
  
  