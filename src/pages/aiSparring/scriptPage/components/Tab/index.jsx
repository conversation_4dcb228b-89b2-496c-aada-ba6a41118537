import { ScrollView, Text, View } from '@tarojs/components';
import React, { useState } from 'react';
import styles from './index.less';

const ScrollableTab = ({ tabs, activeTab, onTabChange }) => {
    const handleTabClick = (index) => {
        onTabChange(index);
    };
    return (
        <View className={styles.container}>
            <ScrollView className={styles.tab_scroll} scrollX scrollWithAnimation scrollIntoView={`tab${activeTab}`}>
                {tabs.map((tab, index) => (
                    <View
                        key={index}
                        id={`tab${index}`}
                        className={`${styles.tab_item}  ${activeTab === index ? styles.active : ''}`}
                        onClick={() => handleTabClick(index)}
                    >
                        <Text>{tab.tabName}</Text>
                    </View>
                ))}
            </ScrollView>
        </View>
    );
};

export default ScrollableTab;
