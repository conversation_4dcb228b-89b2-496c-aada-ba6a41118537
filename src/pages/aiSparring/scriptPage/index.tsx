import { Storage, useEvent, useShow, useStorage } from '@/common';
import { Page } from '@/components';
import config from '@/config';
import { EventKey } from '@/constants/eventKey';
import { StorageEnvKey } from '@/constants/storage';

import ChatList from '@/components/chatList';
import { ChatMode } from '@/constants/voicetype';
import { useLogin } from '@/hooks';
import { getAllScript } from '@/services/chat';
import { checkHarmony, checkRecordPermission } from '@/utils/permission';
import { Dialog, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro, { pxTransform } from '@tarojs/taro';
import { useMount } from 'ahooks';
import { useCallback, useMemo, useRef, useState } from 'react';
import ScriptItem from './components/scriptList';
import ScrollableTab from './components/Tab';
import styles from './index.less';
const App = () => {
    const IconHistory = `${config.cdnPrefix}icon_history.png`;
    const [list, setList] = useState<any>([]);
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [userphone, setUserphone] = useState<string>('');
    const refBase = useRef<any>();
    const [chatListShow, setChatListShow] = useState(false);
    const Dialog_ = Dialog.createOnlyDialog();
    const [isLogin, setIsLogin] = useState(true);
    const isHarmonyRef = useRef<boolean>(false);
    // 分页处理
    const scriptMore = async (type?: any) => {
        const result = await getAllScript(undefined, type == 0 ? undefined : type);
        const records = result.data.data;
        setList(records);
    };

    const [activeTab, setActiveTab] = useState(0);
    const tabsList = [{ tabName: '全部' }, { tabName: '技巧类' }, { tabName: '答题类' }, { tabName: '幻灯片演练' }];

    const handleTabChange = (index: any) => {
        setActiveTab(index);
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);

            if (token) {
                scriptMore(index);
            }
        } else {
            // 聊天结束返回需要调用
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            console.log('onShow', userInfo);
            if (userInfo) {
                scriptMore(index);
            }
        }
    };
    const loginFn = useEvent(async () => {
        const realTimeStatus = Storage.get(StorageEnvKey.Script_Type);
        setActiveTab(realTimeStatus || 0);
        scriptMore(realTimeStatus);
        Storage.del(StorageEnvKey.Script_Type);
    });
    const { login } = useLogin({
        onSuccess: (userInfo) => {
            setUserphone(userInfo.phoneNumber);
            loginFn();
            Taro.showToast({
                icon: 'none',
                title: '登录成功'
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });

    const initData = useCallback(async () => {
        const isHarmony = await checkHarmony();
        console.log('isHarmony', isHarmony);
        isHarmonyRef.current = isHarmony;
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);
            if (token) {
                if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                    if (chatListShow) {
                        refBase!.current.onInitQuery();
                    }

                    // 刷新过后要把刷新标记设置为0
                    Storage.set(StorageEnvKey.REFRESH_HOME, 0);
                }
                loginFn();
            } else {
                login();
            }
        } else {
            // 聊天结束返回需要调用
            if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                if (chatListShow) {
                    refBase!.current.onInitQuery();
                }

                // 刷新过后要把刷新标记设置为0
                Storage.set(StorageEnvKey.REFRESH_HOME, 0);
            }
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            console.log('onShow', userInfo);
            if (!userInfo) {
                setChatListShow(false);
                setUserphone('');
                setIsLogin(false);
            } else {
                setUserphone(userInfo.phoneNumber);
                loginFn();
            }
        }
    }, [isWework, chatListShow]);

    useShow(() => {
        initData();
    });
    useMount(() => {
        Storage.del(StorageEnvKey.HOME_DATA);
        Taro.eventCenter.on(EventKey.LOGOUT, () => {
            setUserphone('');
        });
    });
    const handleLogin = useCallback(
        (cb: () => void) => {
            if (isWework === '1') {
                const token = Storage.get(StorageEnvKey.TOKEN);

                if (token) {
                    cb();
                } else {
                    login();
                }
            } else {
                if (userphone) {
                    cb();
                } else {
                    login();
                }
            }
        },
        [isWework, userphone]
    );
    const setShowDialog = () => {
        handleLogin(() => setChatListShow(true));
    };

    const onLoginClick = () => {
        handleLogin(() => initData());
    };

    const handleHistoryClick = async (item: any) => {
        if (item.type == 3) {
            if (!item.done) {
                // 跳转到进行中的ppt页面
                Taro.navigateTo({ url: `/pages/practicePPT/practice/index?chatId=${item.id}&from=history` });
            } else {
                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/practicePPT/historyPPT/index?chatId=${item.id}&from=history` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}`
                    });
                }
            }
        } else {
            if (item.emanType === 1) {
                // done：是否结束对话。
                // status:1-无报告：看历史聊天记录，2-生成成功：看报告详情，3-生成失败：报告详情，4-生成中：报告详情点击重试后。
                if (!item.done) {
                    if (item.zipFileUrl && item.show3dFlag) {
                        if (
                            isHarmonyRef.current ||
                            Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                            Storage.get(StorageEnvKey.CHAT_MODE) === null
                        ) {
                            Taro.navigateTo({ url: `/pages/shuziren/index?chatId=${item.id}&from=history` });
                        } else {
                            try {
                                await checkRecordPermission(Dialog_);
                                Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.id}` });
                            } catch (error) {
                                // 拒绝授权
                            }
                        }
                    } else {
                        try {
                            await checkRecordPermission(Dialog_);
                            Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.id}` });
                        } catch (error) {
                            // 拒绝授权
                        }
                    }

                    return;
                }

                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${item.id}` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}&to=product`
                    });
                }
            }
            if (item.emanType === 2) {
                // 企业智脑
                Taro.navigateTo({
                    url: `/pages/chat/enterpriseIntelligence/index?chatId=${item.id}&from=history`
                });
            }
        }
    };

    return (
        <Page className={styles.page}>
            <NavBar
                title={<View className={styles.title_tab_item}>AI陪练</View>}
                safeAreaInsetTop
                renderLeft={<Image src={IconHistory} className={styles.history_icon} mode='aspectFit' />}
                onClickLeft={() => setShowDialog()}
            />

            <ScrollableTab tabs={tabsList} activeTab={activeTab} onTabChange={handleTabChange} />
            <ScrollView className={styles.content} scrollY lowerThreshold={50}>
                {!list || list.length === 0 ? (
                    <View className={styles.points_empty}>
                        <View>
                            <Image className={styles.empty_icon} src={`${config.cdnPrefix}x/noscript.png`} />
                        </View>
                        <Text className={styles.empty_text}> 暂无脚本</Text>
                        {!isLogin && (
                            <Button
                                className={styles.login}
                                style={{
                                    '--padding-md': pxTransform(28),
                                    '--button-normal-height': pxTransform(80)
                                }}
                                onClick={onLoginClick}
                                round
                                block
                                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                            >
                                登录
                            </Button>
                        )}
                    </View>
                ) : (
                    list.map((item: any) => (
                        <ScriptItem key={item.id} {...item} departmentLine={1} activeTab={activeTab} />
                    ))
                )}
            </ScrollView>

            <ChatList
                show={chatListShow}
                refBase={refBase}
                onCloseFn={() => setChatListShow(false)}
                onClick={handleHistoryClick}
            />
        </Page>
    );
};

export default App;
