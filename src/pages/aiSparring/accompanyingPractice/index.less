page {
    background: linear-gradient(191deg, #dddeff99 1.1%, #eef3ff99 22%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

    --nav-bar-background-color: rgba(255, 255, 255, 0);
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.page_body {
    height: 100%;
    overflow-y: auto;

}
.swiper {
    padding-top: 45px;
    height:calc(100vh - 45px - 97px - 98px - 113px);
}
.eman_touch {
    flex:1;
}
.history_icon{
    width: 48px;
    height: 48px;
}
.button{
    background: transparent;
    padding: 0 !important;
    display: flex;
    align-items: center;
}
.button::after{
    border: 0; 
}
.title_tab {
    display: flex;
    justify-content: center;
    align-items: center;

    &_item {
        width: 148px;
        color: #61626a;
        text-align: center;
        font-size: 32px;
        font-weight: normal;
        background: transparent;
        padding: 0 !important;
        display: flex;
        align-items: center;
        &::after {
            border: none;
        }
        &_active {
            color: #272c47;
            font-size: 40px;
            font-weight: bold;
        }
    }
}
.product_name{
  
width: 100%;
margin: 24px;
height: 71px;
border-radius: 8px;
opacity: 1;
background: #FFFFFF;
box-shadow: 0px 4px 10px 0px rgba(194, 205, 240, 0.2);
    margin-bottom: 24px ;
}

.content {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-top: 20px;
    margin-bottom: 20px;
    overflow-y: auto;
    margin-bottom: env(safe-area-inset-bottom);
  }
  .card {
    background-color: #fff;
    border-radius: 24px;
    margin: 28px 32px 0;
    padding: 32px  32px 32px 22px;
    box-sizing: border-box;
  }
  .checkpoint_item {
    display: flex;
  }
  .load_status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
    color: #9597a0;
    font-size: 26px;
  }
  .points_empty{display: flex;
    flex-direction: column;
    margin: 32px  32px 32px 22px;
    justify-content: center;
    border-radius: 32px;
    background: #FFFFFF;
    position: relative;
    align-items: center;
    height: 100%; /* 确保占据整个容器高度 */}
  .empty_icon{
//   margin-bottom: 20px;
  width: 400px;
  height: 205px;

  }
  
  .empty_text{
    // margin-top: -20px;
    font-size: 28px;
    color: #9597A0;
  }
  .title_tab_item{
    font-size: 34px;
    font-weight: bold;
    text-align: center;
    color: #000000;
  }