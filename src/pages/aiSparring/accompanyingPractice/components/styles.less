 
.card {
  border-radius: 32px;
  background: #FFFFFF;
  box-shadow: 0px 4px 10px 0px rgba(194, 205, 240, 0.2);
  box-sizing: border-box;
  margin: 0px 24px 16px 24px;
  padding: 28px;
  position: relative;
  .productList_style{
    display: flex;
    align-items: center;
    justify-content: start
  }
   .productList_img{
    width: 100px;
    margin-right: 24px;
    background-size: cover; /* 或者 contain, 根据需要选择 */
    background-position: center;
    background-repeat: no-repeat;
    height: 100px;
   }
   .img_text{
    font-size: 44px;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    margin-top: 30px;
    letter-spacing: -2.01px;
    background: linear-gradient(180deg, #FFFFFF 11%, rgba(255, 255, 255, 0.1) 100%);
    -webkit-background-clip: text;
     -webkit-text-fill-color: transparent;
    background-clip: text;
     text-fill-color: transparent;
   }
   .productList_productName{
    margin-bottom: 16px;
    font-size: 32px;
    color: rgba(39, 44, 71, 1);
    font-weight: 600;
   }
   .productList_text{
    font-size: 24px;
    color: #9597A0;
      }
  }
  
  