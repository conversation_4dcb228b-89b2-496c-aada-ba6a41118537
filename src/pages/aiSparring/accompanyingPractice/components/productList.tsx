import type { LevelStageItemVO } from '@/types/activity';
import { View } from '@tarojs/components';
import { navigateTo } from '@tarojs/taro';
import styles from './styles.less';
import config from "@/config";

export interface CheckPointItemProps extends LevelStageItemVO {
    departmentLine: number;
    onClick?: (id: string) => void;
    onDepartmentClick?: () => void;
}

const Index: React.FC<CheckPointItemProps> = (props: any) => {
    const { productName, scriptNum } = props;
    const goScriptList = () => {
        navigateTo({
            url: `/pages/scriptPage/index?productName=${productName}`
        });
    };
    const newname = productName?.substring(0, 1);

    return (
        <View className={styles.card} onClick={goScriptList}>
            <View className={styles.productList_style}>
                <View className={styles.productList_img} style={{backgroundImage: `url(${config.cdnPrefix}x/frame.png)`}}>
                    <View className={styles.img_text}>{newname}</View>
                </View>
                <View>
                    <View className={styles.productList_productName}>{productName}</View>
                    <View className={styles.productList_text}>{scriptNum}个脚本</View>
                </View>
            </View>
        </View>
    );
};

export default Index;
