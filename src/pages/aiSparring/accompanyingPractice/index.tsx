import { Storage, useEvent, useShow, useStorage } from '@/common';
// import { EventKey } from '@/constants/eventKey';
import ChatList from '@/components/chatList';
import config from '@/config';
import { EventKey } from '@/constants/eventKey';
import { StorageEnvKey } from '@/constants/storage';
import { ChatMode } from '@/constants/voicetype';
import { useLogin } from '@/hooks';
import { scriptProductList } from '@/services/chat';
import type { ChatHistoryVO } from '@/types/chat';
import { checkRecordPermission } from '@/utils/permission';
import { Dialog, NavBar } from '@antmjs/vantui';
import { Image, ScrollView, View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useMount } from 'ahooks';
import { useCallback, useRef, useState } from 'react';
import ProductItem from './components/productList';
import styles from './index.less';

const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    const IconHistory = `${config.cdnPrefix}icon_history.png`;
    const [chatListShow, setChatListShow] = useState(false);
    const [userphone, setUserphone] = useState<string>('');
    const refBase = useRef<any>();

    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [productList, setproductList] = useState<any>([]);
    const isHarmonyRef = useRef<boolean>(false);

    const productMore = async () => {
        const result = await scriptProductList();
        const records = result.data.data;
        setproductList(records);
    };
    const loginFn = useEvent(async () => {
        productMore();
    });

    const { login } = useLogin({
        onSuccess: (userInfo) => {
            setUserphone(userInfo.phoneNumber);
            console.log(userInfo, 'login success');
            loginFn();
            Taro.showToast({
                icon: 'none',
                title: '登录成功'
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });

    useShow(() => {
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);

            if (token) {
                // 聊天结束返回需要调用
                if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                    if (chatListShow) {
                        refBase!.current.onInitQuery();
                    }

                    // 刷新过后要把刷新标记设置为0
                    Storage.set(StorageEnvKey.REFRESH_HOME, 0);
                }
                loginFn();
            } else {
                login();
            }
        } else {
            // 聊天结束返回需要调用
            if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                if (chatListShow) {
                    refBase!.current.onInitQuery();
                }

                // 刷新过后要把刷新标记设置为0
                Storage.set(StorageEnvKey.REFRESH_HOME, 0);
            }
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            console.log('onShow', userInfo);
            if (!userInfo) {
                setChatListShow(false);
                setUserphone('');
            } else {
                setUserphone(userInfo.phoneNumber);
                loginFn();
            }
        }
    });
    useMount(() => {
        Taro.eventCenter.on(EventKey.LOGOUT, () => {
            setUserphone('');
        });
    });

    const handleLogin = useCallback(
        (cb: () => void) => {
            if (isWework === '1') {
                const token = Storage.get(StorageEnvKey.TOKEN);

                if (token) {
                    cb();
                } else {
                    login();
                }
            } else {
                if (userphone) {
                    cb();
                } else {
                    login();
                }
            }
        },
        [isWework, userphone]
    );
    const setShowDialog = () => {
        handleLogin(() => setChatListShow(true));
    };

    const handleHistoryClick = async (item: ChatHistoryVO) => {
        if (item.type == 3) {
            if (!item.done) {
                // 跳转到进行中的ppt页面
                Taro.navigateTo({ url: `/pages/practicePPT/practice/index?chatId=${item.id}&from=history` });
            } else {
                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/practicePPT/historyPPT/index?chatId=${item.id}&from=history` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}`
                    });
                }
            }
        } else {
            if (item.emanType === 1) {
                // done：是否结束对话。
                // status:1-无报告：看历史聊天记录，2-生成成功：看报告详情，3-生成失败：报告详情，4-生成中：报告详情点击重试后。
                if (!item.done) {
                    if (
                        item.zipFileUrl &&
                        item.show3dFlag &&
                        (Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                            Storage.get(StorageEnvKey.CHAT_MODE) === null)
                    ) {
                        Taro.navigateTo({ url: `/pages/shuziren/index?chatId=${item.id}&from=history` });
                    } else {
                        try {
                            await checkRecordPermission(Dialog_);
                            Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.id}` });
                        } catch (error) {
                            // 拒绝授权
                        }
                    }

                    return;
                }

                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${item.id}` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}&to=product`
                    });
                }
            }
            if (item.emanType === 2) {
                // 企业智脑
                Taro.navigateTo({
                    url: `/pages/chat/enterpriseIntelligence/index?chatId=${item.id}&from=history`
                });
            }
        }
    };

    return (
        <View className={styles.page}>
            <NavBar
                title={<View className={styles.title_tab_item}>AI陪练</View>}
                safeAreaInsetTop
                renderLeft={<Image src={IconHistory} className={styles.history_icon} mode='aspectFit' />}
                onClickLeft={() => setShowDialog()}
            />

            {!productList || productList.length === 0 ? (
                <View className={styles.points_empty}>
                    {/* <View>
                            <Image className={styles.empty_min} src={Logo} />
                        </View> */}
                    <View>
                        <Image className={styles.empty_icon} src={`${config.cdnPrefix}x/empty_script.png`} />
                    </View>
                    <View className={styles.empty_text}>暂无产品</View>
                </View>
            ) : (
                <ScrollView className={styles.content} scrollY lowerThreshold={50}>
                    {productList.map((item: any, index: any) => (
                        <View key={index}>
                            <ProductItem key={item.id} {...item} departmentLine={1} />
                        </View>
                    ))}
                    {productList && productList.length > 0 && <View className={styles.load_status}>已加载全部</View>}
                </ScrollView>
            )}

            <ChatList
                show={chatListShow}
                refBase={refBase}
                onCloseFn={() => setChatListShow(false)}
                onClick={handleHistoryClick}
            />
            {/* <Dialog_ /> */}
        </View>
    );
};

export default App;
