import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import { StorageEnvKey } from '@/constants/storage';
import { tenantsettings } from '@/services/common';
import { useDidShow } from '@tarojs/taro';
import { useState } from 'react';
import AccompanyingPractice from './accompanyingPractice';
import styles from './index.less';
import ScriptPage from './scriptPage';
const App = () => {
    const [script, setscript] = useState<string>('');
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);

    const requestConfig = async () => {
        let aiPractisePage = '';
        try {
            const res = await tenantsettings();
            if (res.data.data) {
                aiPractisePage = res.data.data.aiPractisePage;
            } else {
                aiPractisePage = 'SCRIPT';
            }
        } catch (e) {
            aiPractisePage = 'SCRIPT';
        }

        setscript(aiPractisePage);
    };

    useDidShow(async () => {
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);

            if (token) {
                // 聊天结束返回需要调用
                requestConfig();
            } else {
                setscript('SCRIPT');
            }
        } else {
            // 聊天结束返回需要调用
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            console.log('onShow', userInfo);
            if (!userInfo) {
                setscript('SCRIPT');
            } else {
                requestConfig();
            }
        }
    });

    return <Page className={styles.page}>{script === 'SCRIPT' ? <ScriptPage /> : <AccompanyingPractice />}</Page>;
};

export default App;
