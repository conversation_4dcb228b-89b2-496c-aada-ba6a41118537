@import '@/styles/index.less';
@import '../assets/style.less';

page {
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%),
        linear-gradient(158deg, #cff7f4 0%, #d1ebff 12%, #fff 35%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
    --dropdown-menu-background-color: transparent;
    --dropdown-menu-title-font-size: 24px;
    --dropdown-menu-height: 56px;
    --tabs-bottom-bar-width: 54px;
    :global {
        .van-tab--active {
            font-weight: bold;
        }
        .van-tabs__scroll {
            background: none;
        }
        .van-dropdown-menu {
            gap: 26px;
            padding-bottom: 18px;
        }
        .van-dropdown-menu__item {
            padding: 0 20px;
            border: 2px solid rgba(39, 44, 71, 0.2);
            border-radius: 16px;
        }

        .van-dropdown-menu__title {
            flex: 1;
            padding-left: 0;
            &::after {
                right: 0;
                width: 12px;
                height: 12px;
                border: 2px solid #272c47;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                // 向下的三角箭头
                content: '';
            }
        }
        .van-dropdown-menu__title--active {
            color: #333;
            &::after {
                transform: rotate(-135deg);
            }
        }
        .van-tab--active-line {
            background: #4f66ff;
          
        }
       
        .at-calendar__list.flex .flex__item--selected {
            background-color: #4f66ff !important;
        }
    }
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.content {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-bottom: env(safe-area-inset-bottom);
}
.data_type_box {
    margin-left: 10%;
    margin-right: 10%;
}
.query_dropdown {
    margin-top: 20px;
    margin-right: 32px;
    margin-left: 32px;
}

.data_types {
    display: flex;
    gap: 22px;
    margin-right: 32px;
    margin-left: 32px;
}

.data_type {
    flex: 1;
    box-sizing: border-box;
    height: 56px;
    color: #666;
    font-size: 24px;
    line-height: 56px;
    text-align: center;
    background-color: #ebebeb;
    border: 2px solid #ebebeb;
    border-radius: 12px;

    &_active {
        color: #4f66ff;
        font-weight: bold;
        border: 2px solid #4f66ff;
    }
}
.table_tr {
    height: 112px;
    margin-bottom: 16px;
    background: linear-gradient(90deg, #fff 0%, #fafafa 100%);
    border-radius: 16px;
}

.dept_tree {
    max-height: 50vh;
    overflow-y: auto;
    margin-left: 32px;
    margin-right: 32px;
    margin-top: 32px;
}