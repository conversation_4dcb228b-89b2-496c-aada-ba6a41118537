import { Page } from '@/components';
import { DropdownItem, DropdownMenu, NavBar, Tab, Tabs } from '@antmjs/vantui';
import { Block, ScrollView, View } from '@tarojs/components';

import { navigateBack, navigateTo, pxTransform, useLoad, useUnload } from '@tarojs/taro';

import { Storage, useShow } from '@/common';
import Tree from '@/components/treeSelect';
import { StorageEnvKey } from '@/constants/storage';
import { getDepartment, getUserDataAnalyse, getUserDataList, userPointList } from '@/services/kanban';
import '@/styles/tree.global.less';
import type { UserDataItem, UserPointItem } from '@/types/kanban';
import { secondsToHMS } from '@/utils/timeFormat';
import { Button } from '@hygeia/ui';
import { useDebounceFn } from 'ahooks';
import classNames from 'classnames';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AtCalendar } from 'taro-ui';
import 'taro-ui/dist/style/components/calendar.scss';
import styles from './index.less';
dayjs.locale('zh-cn');
const App = () => {
    const [dataActive, setDataActive] = useState(0);
    const memberDataTypes = [
        {
            label: '最高分',
            value: 'max_score'
        },
        {
            label: '总时长',
            value: 'total_time'
        },
        {
            label: '总次数',
            value: 'total_count'
        },

        {
            label: '平均分',
            value: 'avg_score'
        }
    ];
    const [activeMemberDataType, setActiveMemberDataType] = useState(memberDataTypes[0].value);
    const [totalData, setTotalData] = useState({
        uv: 0,
        averageScore: 0,
        averageTime: 0,
        averageCount: 0
    });
    const [averageTime, setAverageTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0
    });

    const [deptList, setDeptList] = useState<any[]>();
    const [deptSelected, setDeptSelected] = useState<any[]>();
    const [deptSelectTemp, setDeptSelectTemp] = useState<any[]>();
    const dayNow = dayjs();
    const [startTime, setStartTime] = useState<number>(dayNow.valueOf());
    const [endTime, setEndTime] = useState<number>(dayNow.valueOf());
    const [selectTime, setSelecteTime] = useState<any>({
        start: dayNow.valueOf()
    });
    const [selectTimeTemp, setSelecteTimeTemp] = useState<any>({});

    const deptRef = useRef<any>();
    const timeRangeRef = useRef<any>();
    const [memberDatalist, setMemberDataList] = useState<UserDataItem[]>([]);
    const pageMemberList = useRef(1);
    const [loadStateMemberList, setLoadStateMemberList] = useState<'loading' | 'complete' | 'more'>();
    const loadStateMemberListRef = useRef<'loading' | 'complete' | 'more'>();
    const [scoreList, setScoreList] = useState<UserPointItem[]>([]);
    const [loadStateScoreList, setLoadStateScoreList] = useState<'loading' | 'complete' | 'more'>();
    const loadStateScoreListRef = useRef<'loading' | 'complete' | 'more'>();
    const pageScoreList = useRef(1);
    const deptTreeRef = useRef<any>();

    const getTotalData = useCallback(async () => {
        const params = {
            startTime: `${dayjs(startTime).format('YYYY-MM-DD')} 00:00:00`,
            endTime: `${dayjs(endTime).format('YYYY-MM-DD')} 23:59:59`
        };
        if (deptSelected?.length > 0) {
            params.deptIds = deptSelected?.map((item: any) => item.id);
        }
        const res = await getUserDataAnalyse(params);
        setTotalData(res.data.data);
        const { hours, minutes, seconds } = secondsToHMS(res.data.data.averageTime);
        setAverageTime({
            hours,
            minutes,
            seconds
        });
    }, [startTime, endTime, deptSelected]);

    // 获取学员数据
    const getListData = useCallback(
        async (type?: string) => {
            console.log(
                'getListData',
                type,
                loadStateMemberList,
                loadStateMemberListRef.current,
                dayjs(startTime).format('YYYY-MM-DD'),
                dayjs(endTime).format('YYYY-MM-DD')
            );
            if (loadStateMemberListRef.current === 'loading' || loadStateMemberListRef.current === 'complete') return;

            setLoadStateMemberList('loading');
            loadStateMemberListRef.current = 'loading';
            const params: any = {
                startTime: `${dayjs(startTime).format('YYYY-MM-DD')} 00:00:00`,
                endTime: `${dayjs(endTime).format('YYYY-MM-DD')} 23:59:59`,
                pageNo: pageMemberList.current,
                pageSize: 10,
                isAsc: 'desc',
                orderByColumn: type || activeMemberDataType
            };
            if (deptSelected?.length > 0) {
                params.deptIds = deptSelected?.map((item: any) => item.id);
            }
            const res = await getUserDataList(params);
            const { records, total } = res.data.data;
            if (records.length > 0) {
                let newData = [];
                if (pageMemberList.current === 1) {
                    newData = records;
                    setMemberDataList(records);
                } else {
                    newData = memberDatalist.concat(records);
                    console.log(newData);
                    setMemberDataList(newData);
                }

                pageMemberList.current += 1;
                setLoadStateMemberList('more');
                loadStateMemberListRef.current = 'more';

                if (Number(total) === newData.length) {
                    setLoadStateMemberList('complete');
                    loadStateMemberListRef.current = 'complete';
                }
            } else {
                if (pageMemberList.current === 1) {
                    setMemberDataList(records);
                }
                setLoadStateMemberList('complete');
                loadStateMemberListRef.current = 'complete';
            }
        },
        [startTime, endTime, memberDatalist, loadStateMemberList, deptSelected]
    );

    const getScoreList = useCallback(async () => {
        if (loadStateScoreListRef.current === 'loading' || loadStateScoreListRef.current === 'complete') return;
        setLoadStateScoreList('loading');
        loadStateScoreListRef.current = 'loading';
        const params = {
            startTime: `${dayjs(startTime).format('YYYY-MM-DD')}`,
            endTime: `${dayjs(endTime).format('YYYY-MM-DD')}`,
            pageNo: pageScoreList.current,
            pageSize: 12,
            isAsc: 'desc',
            orderByColumn: 'points'
        };
        if (deptSelected?.length > 0) {
            params.deptIds = deptSelected?.map((item: any) => item.id);
        }
        console.log(params);

        const res = await userPointList(params);
        const { records, total } = res.data.data;
        if (records.length > 0) {
            let newData = [];
            if (pageScoreList.current === 1) {
                setScoreList(records);
                newData = records;
            } else {
                const newData = scoreList.concat(records);
                console.log(newData);
                setScoreList(newData);
            }
            pageScoreList.current += 1;
            setLoadStateScoreList('more');
            loadStateScoreListRef.current = 'more';
            if (Number(total) === newData.length) {
                setLoadStateScoreList('complete');
                loadStateScoreListRef.current = 'complete';
            }
        } else {
            if (pageScoreList.current === 1) {
                setScoreList(records);
            }
            setLoadStateScoreList('complete');
            loadStateScoreListRef.current = 'complete';
        }
    }, [startTime, endTime, scoreList, loadStateScoreList, deptSelected]);

    const requestDepartment = async () => {
        try {
            const res = await getDepartment();
            console.log('dept', res.data.data);
            setDeptList(res.data.data);
        } catch (error) {
            console.log(error);
        }
    };

    useEffect(() => {
        console.log('useEffect');

        getTotalData();
        pageMemberList.current = 1;
        setLoadStateMemberList('more');
        loadStateMemberListRef.current = 'more';
        getListData();
        pageScoreList.current = 1;
        setLoadStateScoreList('more');
        loadStateScoreListRef.current = 'more';
        getScoreList();
    }, [startTime, endTime, deptSelected]);

    const changeActiveMemberDataType = (type: string) => {
        setActiveMemberDataType(type);
        pageMemberList.current = 1;
        getListData(type);
    };

    const goBack = () => {
        navigateBack();
    };
    const onDataChange = (e: any) => {
        console.log('onDataChange', e);
        setDataActive(e.detail.index);
    };

    const onDeptOpen = () => {
        /*  const { page } = getCurrentInstance();
        const compoent = page.selectComponent('#deptTree');
        console.log('compoent', compoent, deptSelected);
        compoent?.setSelectKey(deptSelected ? deptSelected.map((item: any) => item) : []); */
        console.log('onDeptOpen', deptSelected);
        setDeptSelectTemp(deptSelected?.map((item) => ({ ...item })));
    };
    const onCheck = (keys: string[], data: any) => {
        console.log('onSelect', keys, data);
        setDeptSelectTemp(data.map((item: any) => ({ ...item })));
    };
    const onDeptCancel = () => {
        deptRef.current?.toggle();
        setDeptSelectTemp([]);
    };
    const onDeptConfirm = () => {
        deptRef.current?.toggle();
        console.log('onDeptConfirm', deptSelectTemp);
        setDeptSelected(deptSelectTemp?.map((item: any) => ({ ...item })));
    };

    const onTimeOpen = () => {
        console.log('onTimeOpen', startTime, endTime);

        setSelecteTime({
            start: startTime,
            end: endTime
        });
    };

    const onTimeConfirm = () => {
        if (selectTimeTemp.start && selectTimeTemp.end) {
            setStartTime(selectTimeTemp.start);
            setEndTime(selectTimeTemp.end);
            timeRangeRef.current?.toggle();
            Storage.set(
                StorageEnvKey.DATA_TIME_RANGE,
                JSON.stringify({ start: selectTimeTemp.start, end: selectTimeTemp.end })
            );
        }
    };
    const onTimeCancel = () => {
        timeRangeRef.current?.toggle();
    };
    const onDateChange = (e: any) => {
        const { start, end } = e.value;
        console.log('onDateChange', start, end);

        const d: any = {};

        if (start) {
            d.start = dayjs(start).valueOf();
        }
        if (end) {
            d.end = dayjs(end).valueOf();
        }
        setSelecteTimeTemp(d);
    };

    const goDetail = (userId: string | number) => {
        navigateTo({ url: `/pages/data/detail/index?id=${userId}` });
    };
    const goPoints = (item: UserPointItem) => {
        let url = `/pages/data/points/index?id=${item.id}&name=${item.name}`;
        if (item.avatar) {
            url += `&avatar=${item.avatar}`;
        }
        navigateTo({ url });
    };

    const { run: onMemberListScroll } = useDebounceFn(
        () => {
            getListData();
        },
        {
            wait: 500
        }
    );

    const { run: onScoreListScroll } = useDebounceFn(
        () => {
            getScoreList();
        },
        {
            wait: 500
        }
    );

    useLoad(() => {
        requestDepartment();
    });

    useUnload(() => {
        Storage.del(StorageEnvKey.DATA_TIME_RANGE);
    });

    useShow(() => {
        const timeRangeStore = Storage.get(StorageEnvKey.DATA_TIME_RANGE);
        console.log(timeRangeStore, 'timeRangeStore');
        if (timeRangeStore) {
            try {
                const timeRange = JSON.parse(timeRangeStore);
                setStartTime(timeRange.start);
                setEndTime(timeRange.end);
            } catch (error) {
                console.log(error);
            }
        }
    });

    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title='数据看板'
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => goBack()}
            />
            <Tabs className={styles.data_type_box} color='#4F66FF' onChange={onDataChange}>
                <Tab title='学员数据' />
                <Tab title='积分数据' />
            </Tabs>

            <DropdownMenu className={styles.query_dropdown}>
                <DropdownItem
                    ref={deptRef}
                    title={
                        deptSelected?.length > 0 ? deptSelected?.map((item: any) => item.label).join(',') : '请选择部门'
                    }
                    onOpened={onDeptOpen}
                >
                    <View className={styles.dept_tree}>
                        {/* <x-tree
                            id='deptTree'
                            dataTree={deptList}
                            props={{
                                label: 'label',
                                children: 'children',
                                value: 'id',
                                disabled: 'selected'
                            }}
                            onSelect={onSelect}
                        /> */}
                        <Tree
                            ref={deptTreeRef}
                            treeData={deptList}
                            checkedKeys={deptSelectTemp?.map((item: any) => item.id)}
                            multiple
                            checkable
                            selectable={false}
                            fieldNames={{
                                title: 'label',
                                children: 'children',
                                key: 'id',
                                disabled: 'selected'
                            }}
                            onCheck={onCheck}
                            onSelect={(e) => console.log('onselect', e)}
                        />
                    </View>
                    <View className={styles.query_line} />
                    <Button.Group>
                        <Button
                            onClick={onDeptCancel}
                            style={{
                                '--padding-md': pxTransform(28),
                                '--button-normal-height': pxTransform(76),
                                color: '#777777'
                            }}
                            round
                            block
                            color='#F6F6F6'
                        >
                            取消
                        </Button>
                        <Button
                            onClick={onDeptConfirm}
                            style={{
                                '--padding-md': pxTransform(28),
                                '--button-normal-height': pxTransform(76)
                            }}
                            round
                            block
                            color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                        >
                            确定
                        </Button>
                    </Button.Group>
                </DropdownItem>
                <DropdownItem
                    ref={timeRangeRef}
                    title={`${
                        dayjs(startTime).isSame(dayNow, 'year')
                            ? dayjs(startTime).format('MM-DD')
                            : dayjs(startTime).format('YYYY-MM-DD')
                    }至${
                        dayjs(endTime).isSame(dayNow, 'year')
                            ? dayjs(endTime).format('MM-DD')
                            : dayjs(endTime).format('YYYY-MM-DD')
                    }`}
                    onOpened={onTimeOpen}
                >
                    {/* <View className={styles.time_range_box}>
                        <View className={styles.time_range_item}>
                            {startSelectTime && dayjs(startSelectTime).format('YYYY-MM-DD')}
                        </View>
                        <View className={styles.time_range_split} />
                        <View className={styles.time_range_item}>
                            {endSelectTime && dayjs(endSelectTime).format('YYYY-MM-DD')}
                        </View>
                    </View> */}

                    <AtCalendar
                        className={styles.time_range_calendar}
                        isMultiSelect
                        maxDate={dayNow}
                        currentDate={selectTime}
                        onSelectDate={onDateChange}
                    />

                    <View className={styles.query_line} />
                    <Button.Group>
                        <Button
                            onClick={onTimeCancel}
                            style={{
                                '--padding-md': pxTransform(28),
                                '--button-normal-height': pxTransform(76),
                                color: '#777777'
                            }}
                            round
                            block
                            color='#F6F6F6'
                        >
                            取消
                        </Button>
                        <Button
                            onClick={onTimeConfirm}
                            style={{
                                '--padding-md': pxTransform(28),
                                '--button-normal-height': pxTransform(76)
                            }}
                            round
                            block
                            color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                        >
                            确定
                        </Button>
                    </Button.Group>
                </DropdownItem>
            </DropdownMenu>
            <ScrollView
                className={styles.content}
                style={{ display: dataActive === 0 ? 'block' : 'none' }}
                onScrollToLower={onMemberListScroll}
                scrollY
                lowerThreshold={50}
            >
                <View className={styles.section_title} style={{ marginTop: 0, paddingTop: `${pxTransform(32)}` }}>
                    整体数据
                </View>
                <ScrollView scrollX className={styles.data_list}>
                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.uv}
                            <text className={styles.data_item_unit}>人</text>
                        </View>
                        <View className={styles.data_item_label}>练习人数</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.averageScore}
                            <text className={styles.data_item_unit}>分</text>
                        </View>
                        <View className={styles.data_item_label}>每日人均得分</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {averageTime.hours === 0 && averageTime.minutes === 0 && averageTime.seconds === 0 && '0'}

                            {averageTime.hours > 0 && (
                                <Block>
                                    {averageTime.hours}
                                    <text className={styles.data_item_unit}>时</text>
                                </Block>
                            )}
                            {averageTime.minutes > 0 && (
                                <Block>
                                    {averageTime.minutes}
                                    <text className={styles.data_item_unit}>分</text>
                                </Block>
                            )}
                            {averageTime.seconds > 0 && (
                                <Block>
                                    {averageTime.seconds}
                                    <text className={styles.data_item_unit}>秒</text>
                                </Block>
                            )}
                        </View>
                        <View className={styles.data_item_label}>每日人均练习时长</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.averageCount.toFixed(0)}
                            <text className={styles.data_item_unit}>次</text>
                        </View>
                        <View className={styles.data_item_label}>每日人练习次数</View>
                    </View>
                </ScrollView>

                <View className={styles.section_title}>学员数据</View>
                <View className={styles.data_types}>
                    {memberDataTypes.map((item) => {
                        return (
                            <View
                                className={classNames(
                                    styles.data_type,
                                    activeMemberDataType === item.value && styles.data_type_active
                                )}
                                key={item.value}
                                onClick={() => changeActiveMemberDataType(item.value)}
                            >
                                {item.label}
                            </View>
                        );
                    })}
                </View>
                <View className={styles.table_th}>
                    <View className={styles.table_td_order}>序号</View>
                    <View className={styles.table_td_main}>姓名</View>
                    {activeMemberDataType === 'max_score' && <View className={styles.table_td_score}>最高分</View>}
                    {activeMemberDataType === 'total_time' && <View className={styles.table_td_time}>总时长</View>}
                    {activeMemberDataType === 'total_count' && <View className={styles.table_td_score}>总次数</View>}
                    {activeMemberDataType === 'avg_score' && <View className={styles.table_td_score}>平均分</View>}
                </View>
                {!memberDatalist || memberDatalist.length === 0 ? (
                    <View className={styles.points_empty}>暂无记录</View>
                ) : (
                    memberDatalist.map((item, index) => {
                        let d: any;
                        if (activeMemberDataType === 'max_score') {
                            d = (
                                <View className={styles.table_td_score}>
                                    {item.maxScore} <text className={styles.table_td_unit}>分</text>
                                </View>
                            );
                        } else if (activeMemberDataType === 'total_time') {
                            const { hours, minutes, seconds } = secondsToHMS(item.totalTime);
                            d = (
                                <View className={styles.table_td_time}>
                                    {hours === 0 && minutes === 0 && seconds === 0 && '0'}
                                    {hours > 0 && (
                                        <Block>
                                            {hours}
                                            <text className={styles.table_td_unit}>小时</text>
                                        </Block>
                                    )}
                                    {minutes > 0 && (
                                        <Block>
                                            {minutes}
                                            <text className={styles.table_td_unit}>分</text>
                                        </Block>
                                    )}
                                    {seconds > 0 && (
                                        <Block>
                                            {seconds}
                                            <text className={styles.table_td_unit}>秒</text>
                                        </Block>
                                    )}
                                </View>
                            );
                        } else if (activeMemberDataType === 'total_count') {
                            d = (
                                <View className={styles.table_td_score}>
                                    {item.totalCount}
                                    <text className={styles.table_td_unit}>次</text>
                                </View>
                            );
                        } else if (activeMemberDataType === 'avg_score') {
                            d = (
                                <View className={styles.table_td_score}>
                                    {item.avgScore.toFixed(0)}
                                    <text className={styles.table_td_unit}>分</text>
                                </View>
                            );
                        }

                        return (
                            <View className={styles.table_tr} key={item.id} onClick={() => goDetail(item.id)}>
                                <View className={styles.table_td_order}>{index + 1}</View>
                                <View className={styles.table_td_main}>
                                    <View className={styles.table_td_name}>{item.name}</View>
                                    <View className={styles.table_td_dept}>{item.deptName}</View>
                                </View>
                                {d}
                            </View>
                        );
                    })
                )}
                {memberDatalist && memberDatalist.length > 0 && (
                    <View className={styles.load_status}>
                        {loadStateMemberList === 'loading' && '加载中...'}
                        {loadStateMemberList === 'complete' && '已加载全部记录'}
                        {loadStateMemberList === 'more' && '加载更多'}
                    </View>
                )}
            </ScrollView>

            <ScrollView
                className={styles.content}
                style={{ display: dataActive === 1 ? 'block' : 'none' }}
                scrollY
                lowerThreshold={50}
                onScrollToLower={onScoreListScroll}
            >
                <View className={styles.table_th}>
                    <View className={styles.table_td_order}>序号</View>
                    <View className={styles.table_td_main}>姓名</View>
                    <View className={styles.table_td_score}>新增积分</View>
                    <View className={styles.table_td_score}>总积分</View>
                </View>
                {!scoreList || scoreList.length === 0 ? (
                    <View className={styles.points_empty}>暂无记录</View>
                ) : (
                    scoreList.map((item, index) => {
                        return (
                            <View className={styles.table_tr} key={item.id} onClick={() => goPoints(item)}>
                                <View className={styles.table_td_order}>{index + 1}</View>
                                <View className={styles.table_td_main}>
                                    <View className={styles.table_td_name}>{item.name}</View>
                                    <View className={styles.table_td_dept}>{item.deptName}</View>
                                </View>
                                <View className={styles.table_td_score}>{item.filterPoints}</View>
                                <View className={styles.table_td_score}>{item.points}</View>
                            </View>
                        );
                    })
                )}
                {scoreList && scoreList.length > 0 && (
                    <View className={styles.load_status}>
                        {loadStateScoreList === 'loading' && '加载中...'}
                        {loadStateScoreList === 'complete' && '已加载全部记录'}
                        {loadStateScoreList === 'more' && '加载更多'}
                    </View>
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
