@import '@/styles/index.less';
@import '../assets/style.less';

page {
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%),
        linear-gradient(158deg, #cff7f4 0%, #d1ebff 12%, #fff 35%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
    --dropdown-menu-background-color: transparent;
    --dropdown-menu-title-font-size: 24px;
    --dropdown-menu-height: 56px;

    :global {
        .van-dropdown-menu {
            gap: 26px;
            padding-top: 35px;
            padding-bottom: 35px;
        }
        .van-dropdown-menu__item {
            padding: 0 20px;
            border: 2px solid rgba(39, 44, 71, 0.2);
            border-radius: 16px;
        }

        .van-dropdown-menu__title {
            flex: 1;
            padding-left: 0;
            &::after {
                right: 0;
                width: 12px;
                height: 12px;
                border: 2px solid #272c47;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                // 向下的三角箭头
                content: '';
            }
        }
        .van-dropdown-menu__title--active {
            color: #333;
            &::after {
                transform: rotate(-135deg);
            }
        }
        .at-calendar__list.flex .flex__item--selected {
            background-color: #4f66ff !important;
        }
    }
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}

.content {
    flex: 1;
    height: 0;
    margin-top: 15px;
}
.table_th {
    background: #ebebeb;
}
.table_tr {
    height: 108px;
    background: #fafafa;
    border-bottom: 1px solid #eeeff3;
}
.time_dropdown {
    width: 330px;
}
