import { Storage } from '@/common';
import { Page } from '@/components';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { getReport } from '@/services/chat';
import { getUserChatList, getUserDataDetailAnalyse } from '@/services/kanban';
import type { UserDataDetailAnalyseVo, UserDetailChatItem } from '@/types/kanban';
import { secondsToHMS } from '@/utils/timeFormat';
import { DropdownItem, DropdownMenu, NavBar } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Image, ScrollView, Text, View } from '@tarojs/components';
import Taro, { navigateBack, navigateTo, nextTick, pxTransform, useLoad, useRouter } from "@tarojs/taro";
import { useDebounceFn } from 'ahooks';
import dayjs from 'dayjs';
import { useCallback, useEffect, useRef, useState } from 'react';
import { AtCalendar } from 'taro-ui';
import 'taro-ui/dist/style/components/calendar.scss';
import styles from './index.less';
import { useEvent } from "@hera/react-utils";

const App = () => {
    const { id } = useRouter().params;
    console.log('id', id);
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const [totalData, setTotalData] = useState<UserDataDetailAnalyseVo>({
        name: '',
        avatar: '',
        maxScore: 0,
        averageScore: 0,
        totalTime: 0,
        averageTime: 0,
        totalCount: 0
    });

    const [totalTime, setTotalTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0
    });

    const [averageTime, setAverageTime] = useState({
        hours: 0,
        minutes: 0,
        seconds: 0
    });
    const dayNow = dayjs();
    const timeRangeRef = useRef<any>();
    const [startTime, setStartTime] = useState<number>();
    const [endTime, setEndTime] = useState<number>();
    const [selectTime, setSelecteTime] = useState<any>({
        start: undefined,
        endTime: undefined
    });
    const [selectTimeTemp, setSelecteTimeTemp] = useState<any>({});

    const page = useRef(1);
    const [list, setList] = useState<UserDetailChatItem[]>([]);
    const [loadState, setLoadState] = useState<'loading' | 'complete' | 'more'>();
    const loadStateRef = useRef<'loading' | 'complete' | 'more'>();

    const goBack = () => {
        navigateBack();
    };
    const goToReport = async (item: UserDetailChatItem) => {
        if (item.status === 1) {
            const reportRes = await getReport(item.id.toString());
            if (item.scriptType === 3) {
                Taro.navigateTo({ url: `/pages/practicePPT/historyPPT/index?chatId=${reportRes.data.data.chatId}` });
            } else {
                navigateTo({ url: `/pages/chat/history/index?chatId=${reportRes.data.data.chatId}` });
            }
        } else if (item.status === 2 || item.status === 3 || item.status === 4) {
            navigateTo({ url: `/pages/chat/report/index?id=${item.id}&list_status=${item.status}` });
        }
    };

    const getAnalysisData = useEvent(async () => {
        console.log('getAnalysisData', startTime, endTime);
        const res = await getUserDataDetailAnalyse({
            startTime: `${dayjs(startTime).format('YYYY-MM-DD')} 00:00:00`,
            endTime: `${dayjs(endTime).format('YYYY-MM-DD')} 23:59:59`,
            userId: id
        });
        setTotalData(res.data.data);
        setTotalTime({ ...secondsToHMS(res.data.data.totalTime) });
        setAverageTime({ ...secondsToHMS(res.data.data.averageTime) });
    });

    const getListData = useEvent(async () => {
        console.log('getListData', page.current, startTime, endTime, loadStateRef.current);
        if (loadStateRef.current === 'loading' || loadStateRef.current === 'complete') return;
        setLoadState('loading');
        loadStateRef.current = 'loading';
        const res = await getUserChatList({
            startTime: `${dayjs(startTime).format('YYYY-MM-DD')}`,
            endTime: `${dayjs(endTime).format('YYYY-MM-DD')}`,
            userId: id,
            pageNo: page.current,
            pageSize: 12
        });
        const { records, total } = res.data.data;

        if (records.length > 0) {
            let newData = [];
            if (page.current === 1) {
                setList(records);
                newData = records;
            } else {
                newData = list.concat(records);
                console.log(newData);
                setList(newData);
            }
            page.current += 1;
            setLoadState('more');
            loadStateRef.current = 'more';
            console.log('complete', total, newData.length)
            if (Number(total) <= newData.length) {
                setLoadState('complete');
                loadStateRef.current = 'complete';
            }
        } else {
            if (page.current === 1) {
                setList(records)
                setLoadState('complete');
                loadStateRef.current = 'complete';
            }

        }
    });

    const onTimeOpen = () => {
        console.log('onTimeOpen', startTime, endTime);
        setSelecteTime({
            start: startTime,
            end: endTime
        });
    };

    const timeChange = useEvent(() => {
        nextTick(() => {
            console.log('timeChange', startTime, endTime);
            getAnalysisData();
            page.current = 1;
            setLoadState('more');
            loadStateRef.current = 'more';
            getListData();
        })
    });

    const onTimeConfirm = () => {
        console.log('onTimeConfirm', selectTimeTemp);
        if (selectTimeTemp.start && selectTimeTemp.end) {
            setStartTime(selectTimeTemp.start);
            setEndTime(selectTimeTemp.end);
            timeRangeRef.current?.toggle();
            Storage.set(
                StorageEnvKey.DATA_TIME_RANGE,
                JSON.stringify({ start: selectTimeTemp.start, end: selectTimeTemp.end })
            );
            timeChange();
        }
    };
    const onTimeCancel = () => {
        timeRangeRef.current?.toggle();
    };

    const onDateChange = (e: any) => {
        const { start, end } = e.value;
        console.log('onDateChange', start, end);

        const d: any = {};

        if (start) {
            d.start = dayjs(start).valueOf();
        }
        if (end) {
            d.end = dayjs(end).valueOf();
        }
        setSelecteTimeTemp(d);
    };

    const { run: onScroll } = useDebounceFn(
        () => {
            console.log('onScroll', loadStateRef.current)
            getListData();
        },
        {
            wait: 500
        }
    );

    useLoad(() => {
        const timeRangeStore = Storage.get(StorageEnvKey.DATA_TIME_RANGE);
        console.log(timeRangeStore, 'timeRangeStore');
        if (timeRangeStore) {
            try {
                const timeRange = JSON.parse(timeRangeStore);
                setStartTime(timeRange.start);
                setEndTime(timeRange.end);
                setSelecteTime({
                    start: timeRange.start,
                    end: timeRange.end,
                })
                timeChange();
            } catch (error) {
                setStartTime(dayNow.valueOf());
                setEndTime(dayNow.valueOf());
                setSelecteTime({
                    start: dayNow.valueOf(),
                    end: dayNow.valueOf(),
                })
                timeChange();
                console.log(error);
            }
        } else {
            setStartTime(dayNow.valueOf());
            setEndTime(dayNow.valueOf());
            setSelecteTime({
                start: dayNow.valueOf(),
                end: dayNow.valueOf(),
            })
            timeChange();
        }
    });

    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title='学员详情'
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => goBack()}
            />
            <View className={styles.member_info}>
                <Image src={totalData.avatar || AvatarDefault} className={styles.avatar_image} mode='aspectFill' />
                <View className={styles.member_info_name}>{totalData.name}</View>
                <DropdownMenu className={styles.time_dropdown}>
                    <DropdownItem
                        ref={timeRangeRef}
                        title={`${
                            dayjs(startTime).isSame(dayNow, 'year')
                                ? dayjs(startTime).format('MM-DD')
                                : dayjs(startTime).format('YYYY-MM-DD')
                        }至${
                            dayjs(endTime).isSame(dayNow, 'year')
                                ? dayjs(endTime).format('MM-DD')
                                : dayjs(endTime).format('YYYY-MM-DD')
                        }`}
                        onOpen={onTimeOpen}
                    >
                        <AtCalendar
                            className={styles.time_range_calendar}
                            isMultiSelect
                            maxDate={dayNow}
                            currentDate={selectTime}
                            onSelectDate={onDateChange}
                        />
                        <View className={styles.query_line} />
                        <Button.Group>
                            <Button
                                onClick={onTimeCancel}
                                style={{
                                    '--padding-md': pxTransform(28),
                                    '--button-normal-height': pxTransform(76),
                                    color: '#777777'
                                }}
                                round
                                block
                                color='#F6F6F6'
                            >
                                取消
                            </Button>
                            <Button
                                onClick={onTimeConfirm}
                                style={{
                                    '--padding-md': pxTransform(28),
                                    '--button-normal-height': pxTransform(76)
                                }}
                                round
                                block
                                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                            >
                                确定
                            </Button>
                        </Button.Group>
                    </DropdownItem>
                </DropdownMenu>
            </View>
            <ScrollView className={styles.content} onScrollToLower={onScroll} scrollY lowerThreshold={50}>
                <View className={styles.section_title}>整体数据</View>
                <ScrollView scrollX className={styles.data_list}>
                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.maxScore === null ? 0 : totalData.maxScore}

                            <text className={styles.data_item_unit}>分</text>
                        </View>
                        <View className={styles.data_item_label}>最高分</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.averageScore}
                            <text className={styles.data_item_unit}>分</text>
                        </View>
                        <View className={styles.data_item_label}>平均分</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalTime.hours === 0 && totalTime.minutes === 0 && totalTime.seconds === 0 && '0'}
                            {totalTime.hours > 0 && (
                                <Block>
                                    {totalTime.hours}
                                    <text className={styles.data_item_unit}>时</text>
                                </Block>
                            )}
                            {totalTime.minutes > 0 && (
                                <Block>
                                    {totalTime.minutes}
                                    <text className={styles.data_item_unit}>分</text>
                                </Block>
                            )}
                            {totalTime.seconds > 0 && (
                                <Block>
                                    {totalTime.seconds}
                                    <text className={styles.data_item_unit}>秒</text>
                                </Block>
                            )}
                        </View>
                        <View className={styles.data_item_label}>总时长</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {averageTime.hours === 0 && averageTime.minutes === 0 && averageTime.seconds === 0 && '0'}
                            {averageTime.hours > 0 && (
                                <Block>
                                    {averageTime.hours}
                                    <text className={styles.data_item_unit}>时</text>
                                </Block>
                            )}
                            {averageTime.minutes > 0 && (
                                <Block>
                                    {averageTime.minutes}
                                    <text className={styles.data_item_unit}>分</text>
                                </Block>
                            )}
                            {averageTime.seconds > 0 && (
                                <Block>
                                    {averageTime.seconds}
                                    <text className={styles.data_item_unit}>秒</text>
                                </Block>
                            )}
                        </View>
                        <View className={styles.data_item_label}>平均时长</View>
                    </View>

                    <View className={styles.data_item}>
                        <View className={styles.data_item_value}>
                            {totalData.totalCount}
                            <text className={styles.data_item_unit}>次</text>
                        </View>
                        <View className={styles.data_item_label}>总次数</View>
                    </View>
                </ScrollView>

                <View className={styles.section_title}>练习数据</View>
                <View className={styles.table_th}>
                    <View className={styles.table_td_main}>脚本</View>
                    <View className={styles.table_td_time}>时长</View>
                    <View className={styles.table_td_score}>得分</View>
                </View>

                {!list || list.length === 0 ? (
                    <View className={styles.points_empty}>暂无记录</View>
                ) : (
                    list.map((item) => {
                        const { hours, minutes, seconds } = secondsToHMS(item.timeLong);
                        let statusText = null;
                        if (item.status === 1) {
                            statusText = <Text className={styles.table_td_text}>无报告</Text>;
                        } else if (item.status === 2) {
                            statusText = (
                                <Block>
                                    {item.score}
                                    <Text className={styles.table_td_unit}>分</Text>
                                </Block>
                            );
                        } else if (item.status === 3) {
                            statusText = <Text className={styles.table_td_text}>生成失败</Text>;
                        } else if (item.status === 4) {
                            statusText = <Text className={styles.table_td_text}>生成中</Text>;
                        } else {
                            statusText = <Text className={styles.table_td_text}>进行中</Text>;
                        }
                        return (
                            <View className={styles.table_tr} key={item.id} onClick={() => goToReport(item)}>
                                <View className={styles.table_td_main}>
                                    <View className={styles.table_td_name}>{item.scriptName}</View>
                                    <View className={styles.table_td_dept}>
                                        {dayjs(item.createTime).isSame(dayjs(), 'year')
                                            ? dayjs(item.createTime).format('MM月DD日 HH:mm')
                                            : dayjs(item.createTime).format('YYYY年MM月DD日 HH:mm')}
                                    </View>
                                </View>
                                <View className={styles.table_td_time}>
                                    {hours === 0 && minutes === 0 && seconds === 0 && '0'}
                                    {hours > 0 && (
                                        <Block>
                                            {hours}
                                            <text className={styles.table_td_unit}>小时</text>
                                        </Block>
                                    )}
                                    {minutes > 0 && (
                                        <Block>
                                            {minutes}
                                            <text className={styles.table_td_unit}>分</text>
                                        </Block>
                                    )}
                                    {seconds > 0 && (
                                        <Block>
                                            {seconds}
                                            <text className={styles.table_td_unit}>秒</text>
                                        </Block>
                                    )}
                                </View>
                                <View className={styles.table_td_score}>{statusText}</View>
                            </View>
                        );
                    })
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
