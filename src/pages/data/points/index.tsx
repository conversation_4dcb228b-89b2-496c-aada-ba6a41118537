import { Page } from '@/components';
import config from '@/config';
import { getUserPointRecord } from '@/services/kanban';
import type { PointRecordVo } from '@/types/points';
import { NavBar } from '@antmjs/vantui';
import { Image, ScrollView, View } from '@tarojs/components';
import { navigateBack, useLoad, useRouter } from '@tarojs/taro';
import { useDebounceFn } from 'ahooks';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import styles from './index.less';
const App = () => {
    const { id, name, avatar } = useRouter().params;
    console.log('id', id, name, avatar);
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const page = useRef(1);
    const [data, setData] = useState<PointRecordVo[]>([]);
    const [loadState, setLoadState] = useState<'loading' | 'complete' | 'more'>('more');

    const loadMore = async () => {
        if (loadState === 'loading' || loadState === 'complete') return;
        setLoadState('loading');

        const result = await getUserPointRecord({ pageNo: page.current, pageSize: 12, userId: id });

        const { records, total } = result.data.data;
        if (records.length > 0) {
            page.current += 1;
            const newData = data.concat(records);
            console.log(newData);
            setData(newData);
            setLoadState('more');
            if (Number(total) === newData.length) {
                setLoadState('complete');
            }
        } else {
            setLoadState('complete');
        }
    };
    const goBack = () => {
        navigateBack();
    };
    const { run: onScroll } = useDebounceFn(
        () => {
            console.log('onScroll');
            loadMore();
        },
        {
            wait: 500
        }
    );
    useLoad(() => {
        loadMore();
    });
    return (
        <Page className={styles.page}>
            <NavBar
                className={styles.navbar}
                title='积分记录'
                leftArrow
                safeAreaInsetTop
                onClickLeft={() => goBack()}
            />

            <View className={styles.member_info}>
                <Image src={avatar || AvatarDefault} className={styles.avatar_image} mode='aspectFill' />
                <View className={styles.member_info_name}>{name}</View>
            </View>
            <View className={styles.section_title}>积分数据</View>
            <View className={styles.table_th}>
                <View className={styles.table_td_main}>任务名称</View>
                <View className={styles.table_td_score}>积分</View>
            </View>
            <ScrollView className={styles.points_list} onScrollToLower={onScroll} scrollY lowerThreshold={30}>
                {!data || data.length === 0 ? (
                    <View className={styles.points_empty}>暂无记录</View>
                ) : (
                    data.map((item) => (
                        <View className={styles.table_tr} key={item.pointTime}>
                            <View className={styles.table_td_main}>
                                <View className={styles.table_td_name}>{item.missionName}</View>
                                <View className={styles.table_td_dept}>
                                    {dayjs(item.pointTime).isSame(dayjs(), 'year')
                                        ? dayjs(item.pointTime).format('MM月DD日 HH:mm')
                                        : dayjs(item.pointTime).format('YYYY年MM月DD日 HH:mm')}
                                </View>
                            </View>
                            <View className={styles.table_td_score}>
                                {item.points > 0 ? '+' : '-'}
                                {item.points}
                            </View>
                        </View>
                    ))
                )}
                {data && data.length > 0 && (
                    <View className={styles.load_status}>
                        {loadState === 'loading' && '加载中...'}
                        {loadState === 'complete' && '已加载全部记录'}
                        {loadState === 'more' && '加载更多'}
                    </View>
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
