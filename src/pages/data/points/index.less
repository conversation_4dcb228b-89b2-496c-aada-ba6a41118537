@import '@/styles/index.less';
@import '../assets/style.less';

page {
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%),
        linear-gradient(158deg, #cff7f4 0%, #d1ebff 12%, #fff 35%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.points_list {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    padding-bottom: constant(safe-area-inset-bottom);
    padding-bottom: env(safe-area-inset-bottom);
}

.table_th {
    background: #ebebeb;
}
.table_tr {
    height: 108px;
    background: #fafafa;
    border-bottom: 1px solid #eeeff3;
}
