import { Page } from '@/components';
import { getPointsList } from '@/services/points';
import type { PointRecordVo } from '@/types/points';

import { ScrollView, View } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';
import { useDebounceFn } from 'ahooks';

import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import styles from './index.less';
const App = () => {
    const page = useRef(1);
    const [data, setData] = useState<PointRecordVo[]>([]);
    const [loadState, setLoadState] = useState<'loading' | 'complete' | 'more'>('loading');

    const loadMore = async () => {
        setLoadState('loading');

        const result = await getPointsList({ pageNo: page.current, pageSize: 12 });
        const { records, total } = result.data.data;
        if (records.length > 0) {
            page.current += 1;
            const newData = data.concat(records);
            console.log(newData);
            setData(newData);
            setLoadState('more');
            if (Number(total) === newData.length) {
                setLoadState('complete');
            }
        } else {
            setLoadState('complete');
        }
    };

    const { run: onScroll } = useDebounceFn(
        () => {
            console.log('onScroll');
            loadMore();
        },
        {
            wait: 1000
        }
    );
    useLoad(() => {
        loadMore();
    });
    return (
        <Page className={styles.page}>
            <ScrollView className={styles.points_list} onScrollToLower={onScroll} scrollY lowerThreshold={30}>
                {!data || data.length === 0 ? (
                    <View className={styles.points_empty}>暂无记录</View>
                ) : (
                    data.map((item) => (
                        <View key={item.pointTime} className={styles.points_item}>
                            <View className={styles.points_item_content}>
                                <View className={styles.points_item_title}>{item.missionName}</View>
                                <View className={styles.points_item_time}>
                                    {dayjs(item.pointTime).isSame(dayjs(), 'year')
                                        ? dayjs(item.pointTime).format('MM月DD日 HH:mm')
                                        : dayjs(item.pointTime).format('YYYY年MM月DD日 HH:mm')}
                                </View>
                            </View>
                            <View className={styles.points_item_score}>
                                {item.points > 0 ? '+' : '-'}
                                {item.points}
                            </View>
                        </View>
                    ))
                )}
                {data && data.length > 0 && (
                    <View className={styles.load_status}>
                        {loadState === 'loading' && '加载中...'}
                        {loadState === 'complete' && '已加载全部记录'}
                        {loadState === 'more' && '加载更多'}
                    </View>
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
