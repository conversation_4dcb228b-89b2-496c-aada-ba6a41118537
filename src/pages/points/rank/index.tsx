import { Page } from '@/components';
import { getRankList } from '@/services/points';
import type { RankVo } from '@/types/points';

import config from '@/config';
import { Image, View } from '@tarojs/components';
import { useDidShow } from '@tarojs/taro';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import styles from './index.less';

const App = () => {
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const rankTags = [
        {
            name: '上周榜',
            key: '上周'
        },
        {
            name: '本周榜',
            key: '本周'
        },

        {
            name: '上月榜',
            key: '上月'
        },
        {
            name: '本月榜',
            key: '本月'
        }
    ];
    const [activeTag, setActiveTag] = useState<string>();
    const [dataLastWeek, setDataLastWeek] = useState<RankVo[]>([]);
    const [dataThisWeek, setDataThisWeek] = useState<RankVo[]>([]);
    const [dataLastMonth, setDataLastMonth] = useState<RankVo[]>([]);
    const [dataThisMonth, setDataThisMonth] = useState<RankVo[]>([]);

    const requestData = async (period: string) => {
        console.log('requestData', period);

        const result = await getRankList(period);
        const { data } = result.data;
        switch (period) {
            case '上周':
                setDataLastWeek(data);
                break;
            case '本周':
                setDataThisWeek(data);
                break;
            case '上月':
                setDataLastMonth(data);
                break;
            case '本月':
                setDataThisMonth(data);
                break;
        }
    };

    useEffect(() => {
        if (activeTag) {
            requestData(activeTag);
        }
    }, [activeTag]);

    useDidShow(() => {
        setActiveTag(rankTags[0].key);
    });

    return (
        <Page className={styles.page}>
            <View className={styles.rank_tags}>
                {rankTags.map((item) => {
                    return (
                        <View
                            key={item.key}
                            onClick={() => setActiveTag(item.key)}
                            className={classNames(styles.rank_tag, {
                                [styles.active]: activeTag === item.key
                            })}
                        >
                            {item.name}
                        </View>
                    );
                })}
            </View>
            <View className={styles.rank_list}>
                {rankTags.map((tag: any) => (
                    <View
                        key={tag.key}
                        className={classNames(styles.rank_scroll, tag.key === activeTag ? styles.show : styles.hide)}
                    >
                        {(tag.key === '上周'
                            ? dataLastWeek
                            : tag.key === '本周'
                            ? dataThisWeek
                            : tag.key === '上月'
                            ? dataLastMonth
                            : tag.key === '本月'
                            ? dataThisMonth
                            : []
                        ).map((item: RankVo, index: number) => (
                            <View key={index} className={styles.rank_item}>
                                <View className={styles.rank_index}>{index + 1}</View>
                                <Image src={item.avatar ? item.avatar : AvatarDefault} className={styles.rank_avatar} />
                                <View className={styles.rank_name}>{item.userName}</View>
                                <View className={styles.rank_points}>{item.points}积分</View>
                            </View>
                        ))}
                    </View>
                ))}
            </View>
        </Page>
    );
};

export default App;
