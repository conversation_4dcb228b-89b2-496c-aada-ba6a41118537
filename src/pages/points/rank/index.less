@import '@/styles/index.less';

.page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.rank_tags {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 13px;
  margin: 32px 36px 44px;
}
.rank_tag {
  flex: 1;
  height: 66px;
  border-radius: 16px;
  border: 2px solid #ccced7;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28px;
  color: #9597A0;

  &.active {
    border-color: #06F;
    background-color: #06F;
    color: #fff;
  }
}
.rank_list {
  flex: 1;
  height: 0;
  position: relative;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
}
.rank_scroll {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top:0;
  overflow-y: auto;
}
.visible {
  display: block;
  z-index: 1;
}
.hide {
  visibility: hidden;
  z-index: 0;
}
.rank_index {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #272C47;
  background-size: cover;
  background-repeat: no-repeat;
}
.rank_item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 112px;
  padding: 0 48px;

  &:nth-child(1) {
    .rank_index {
      color: rgba(0, 0, 0, 0);
      background-image: url('../../points/assets/rank_1.svg');
    }
  }
  &:nth-child(2) {
   .rank_index {
      color: rgba(0, 0, 0, 0);
      background-image: url('../../points/assets/rank_2.svg');
    }
  }
  &:nth-child(3) {
   .rank_index {
      color: rgba(0, 0, 0, 0);
      background-image: url('../../points/assets/rank_3.svg');
    }
  }
}


.rank_avatar {
  width: 64px;
  height: 64px;
  margin-left: 32px;
  margin-right: 12px;
  border-radius: 64px;
}
.rank_name {
  font-size: 28px;
  flex:1;
  font-weight: 500;
}
.rank_points {
  font-size: 32px;
  color: #ECAD34;
  font-weight: bold;
}

