@import '@/styles/index.less';

.page {
    box-sizing: border-box;
    padding: 32px;
}
.section {
    margin-bottom: 16px;
    padding: 32px;
    overflow: hidden;
    background: rgba(255, 255, 255, 1);
    border-radius: 16px;

    &:last-child {
        margin-bottom: 0;
    }
}
.section_title {
    font-weight: bold;
    font-size: 32px;
}
.line_wrap {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.rank {
    padding-left: 16px;
    color: #9597a0;
    font-weight: normal;
    font-size: 28px;
    line-height: 40px;
    display: flex;
    align-items: center;

    &_icon {
        width: 28px;
        height: 28px;
        margin-right: 8px;
    }
}
.points_detail {
    margin-top: 48px;
    .icon {
        width: 58px;
        height: 58px;
        margin-right: 15px;
        vertical-align: middle;
    }
}
.points_month {
    font-weight: bold;
    font-size: 56px;
    vertical-align: middle;
}
.points_other {
    gap: 16px;
}
.points_block {
    display: flex;
    flex: 1;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    margin-top: 60px;
    padding: 0 28px;
    background-color: #fafafa;
    border-radius: 16px;

    &_title {
        color: #9597a0;
        font-size: 24px;
    }
    &_content {
        font-weight: bold;
        font-size: 32px;
    }
}
.task_list {
    margin-top: 32px;
}
.task_item {
    display: flex;
    margin-bottom: 24px;
    &:last-child {
        margin-bottom: 0;
    }
}
.task_info {
    flex: 1;
    margin-top: 16px;
    margin-bottom: 10px;
    margin-left: 16px;
}
.task_title {
    font-weight: bold;
    font-size: 28px;
}
.task_desc {
    display: flex;
    align-items: center;
    margin-top: 12px;
    font-size: 24px;
    .icon {
        width: 23px;
        height: 23px;
        margin-right: 5px;
    }
}
.task_points {
    margin-right: 16px;
    color: #ecad34;
}
.task_tip {
    color: #9597a0;
}
.task_button {
    --padding-md: 0;
    --button-normal-height: 54px;

    align-self: center;
    width: 144px;
    margin: 0;
    font-size: 28px;

    &_disabled {
        --button-disabled-opacity: 1;
        --button-default-background-color: #CCCED7;

        color: #fff;
    }
}
.weeks {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 8px;
    margin-top: 28px;
    margin-bottom: 16px;
}
.week {
    flex: 1;
    color: #9597a0;
    font-size: 20px;
    text-align: center;
}
.calendar {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 24px 8px;

    &_sign {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 96px;
        background: #fafafa;

        &_not {
            background: none;
        }
    }
    &_status {
        width: 40px;
        height: 40px;

        &_no {
            width: 30px;
            height: 30px;
        }
    }
    &_day {
        margin-top: 8px;
        color: #9597a0;
        font-size: 20px;
        text-align: center;
    }
    &_more {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 140px;
        height: 30px;
        padding-top: 25px;
        margin: 0 auto;
        color: #9597a0;
        font-size: 20px;

        &_icon {
            transition: all 0.2s ease;
        }
    }
}

.show {
    display: grid;
}
.hide {
    display: none;
}

.collapse {
    // 中心翻转
    transform: rotate(180deg);
}
