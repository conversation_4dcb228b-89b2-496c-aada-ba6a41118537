// import iconRanking from '@/pages/points/assets/icon_ranking.svg';
import iconSignMiss from '@/pages/points/assets/sign_miss.svg';
import iconSignNo from '@/pages/points/assets/sign_no.svg';

import { Page } from '@/components';
import { Icon, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Image, Text, View } from '@tarojs/components';
import { navigateTo, pxTransform, switchTab, useDidShow } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import classNames from 'classnames';

import config from '@/config';
import { daySignIn, getPointMission, getPointsStat, monthSignIn } from '@/services/points';
import type { MonthSignInVo, PointMissionVo } from '@/types/points';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import weekday from 'dayjs/plugin/weekday';
import { useState } from 'react';
import styles from './index.less';
dayjs.extend(weekday);
dayjs.locale('zh-cn');

const Toast_ = Toast.createOnlyToast();
const App = () => {
    const IconCheckActive = `${config.cdnPrefix}check_active.svg`;
    const iconPoints = `${config.cdnPrefix}icon_points.svg`;
    const [missionList, setMissionList] = useState<PointMissionVo[]>([]);
    const [showSign, setShowSign] = useState(false);
    const [signWeek, setSignWeek] = useState<any>([]);
    const [signMonth, setSignMonth] = useState<any>([]);
    const [monthShow, setMonthShow] = useState(false);
    const [monthPoints, setMonthPoints] = useState<number>(0);
    const [totalPoints, setTotalPoints] = useState<number>(0);
    const [consumePoints, setConsumePoints] = useState<number>(0);

    const weekDays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const gotoDetail = () => {
        navigateTo({ url: '/pages/points/detail/index' });
    };
    // const gotoRank = () => {
    //     navigateTo({ url: '/pages/points/rank/index' });
    // };

    const toggleMonthShow = () => {
        setMonthShow(!monthShow);
    };

    const generateWeek = (list: MonthSignInVo[]) => {
        const today = dayjs();
        const startOfWeek = today.startOf('week');
        const endOfWeek = today.endOf('week');

        const datesInWeek = [];

        // 遍历list，查找本周的签到情况
        for (const item of list) {
            const signInDate = dayjs(item.date);
            if (
                signInDate.isSame(startOfWeek, 'day') ||
                signInDate.isSame(endOfWeek, 'day') ||
                (signInDate.isAfter(startOfWeek) && signInDate.isBefore(endOfWeek))
            ) {
                datesInWeek.push({
                    date: signInDate,
                    status: item.signInFlag ? 1 : signInDate.isAfter(today.subtract(1, 'day')) ? 2 : 0
                });
            }
        }

        const firstDayWeek = datesInWeek[0].date.day();
        console.log(firstDayWeek, 'firstDayWeek');
        for (let i = 0; i < firstDayWeek - 1; i++) {
            datesInWeek.unshift({
                date: '',
                status: -1
            });
        }

        // for (let day = startOfWeek; day.isBefore(endOfWeek); day = day.add(1, 'day')) {
        //     datesInWeek.push({
        //         date: day,
        //         status: Math.floor(Math.random() * 3)
        //     });
        // }
        setSignWeek(datesInWeek);

        console.log(datesInWeek);
    };

    const generateMonth = (list: MonthSignInVo[]) => {
        const datesInMonth = [];
        const today = dayjs();

        // 判断list第一个是星期几，
        const firstDayWeek = dayjs(list[0].date);
        console.log(firstDayWeek.weekday(), 'firstDayWeek');
        for (let i = 0; i < firstDayWeek.weekday(); i++) {
            datesInMonth.push({
                date: '',
                status: -1
            });
        }

        // 遍历list
        for (const item of list) {
            const signInDate = dayjs(item.date);
            datesInMonth.push({
                date: signInDate,
                status: item.signInFlag ? 1 : signInDate.isAfter(today.subtract(1, 'day')) ? 2 : 0
            });
        }

        // for (let day = 1; day <= today.daysInMonth(); day++) {
        //     datesInMonth.push({
        //         date: dayjs().date(day),
        //         status: Math.floor(Math.random() * 3)
        //     });
        // }
        setSignMonth(datesInMonth);

        console.log(datesInMonth);
    };

    const getPointsStatic = async () => {
        try {
            const res = await getPointsStat();
            const { monthPoints, totalPoints, consumePoints } = res.data.data;
            setMonthPoints(monthPoints);
            setTotalPoints(totalPoints);
            setConsumePoints(consumePoints);
        } catch (error) {
            setMonthPoints(0);
            setTotalPoints(0);
            setConsumePoints(0);
        }
    };
    const getMonthSignIn = async () => {
        try {
            const res = await monthSignIn();
            generateWeek(res.data.data);
            generateMonth(res.data.data);
            console.log(res);
        } catch (error) {
            console.log(error);
        }
    };
    const getPointMissionData = async () => {
        try {
            const res = await getPointMission();
            const { data } = res.data;
            setMissionList(data);
            const fd = data.findIndex((item) => item.userPointType === 'DAILY_LOGIN');
            if (fd !== -1) {
                getMonthSignIn();
                setShowSign(true);
            } else {
                setShowSign(false);
            }
        } catch (error) {
            setMissionList([]);
        }
    };

    const { run: handleSign } = useThrottleFn(
        async () => {
            console.log('handleSign');
            try {
                const res = await daySignIn();
                if (res.data.data) {
                    Toast_.show({
                        message: '签到成功',
                        forbidClick: true,
                        duration: 1000,
                        onClose: () => {
                            getPointsStatic();
                            getPointMissionData();
                        }
                    });
                }
            } catch (error) {
                console.log(error);
            }
        },
        { wait: 5000 }
    );

    const { run: gotoExcise } = useThrottleFn(
        () => {
            switchTab({
                url: '/pages/aiSparring/index'
            });
        },
        { wait: 1500 }
    );

    useDidShow(() => {
        getPointsStatic();
        getPointMissionData();
    });

    return (
        <Page className={styles.page}>
            <View className={styles.section}>
                <View className={classNames(styles.line_wrap, styles.section_title)}>
                    <Text>本月积分</Text>
                    {/* <View className={styles.rank} onClick={gotoRank}>
                        <Image src={iconRanking} className={styles.rank_icon} />
                        <Text>排行榜</Text>
                    </View> */}
                </View>
                <View className={classNames(styles.line_wrap, styles.points_detail)}>
                    <View>
                        <Image src={iconPoints} className={styles.icon} mode='aspectFit' />
                        <Text className={styles.points_month}>{monthPoints}</Text>
                    </View>
                    <Button
                        className={styles.task_button}
                        round
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                        onClick={gotoDetail}
                    >
                        明细
                    </Button>
                </View>
                <View className={classNames(styles.line_wrap, styles.points_other)}>
                    <View className={styles.points_block}>
                        <View className={styles.points_block_title}>积分余额</View>
                        <View className={styles.points_block_content}>{totalPoints}</View>
                    </View>
                    <View className={styles.points_block}>
                        <View className={styles.points_block_title}>消耗积分</View>
                        <View className={styles.points_block_content}>{consumePoints}</View>
                    </View>
                </View>
            </View>
            <View className={styles.section}>
                <View className={styles.section_title}>积分任务</View>
                <View className={styles.task_list}>
                    {missionList.map((item) => (
                        <View className={styles.task_item} key={item.missionName}>
                            <View className={styles.task_info}>
                                <View className={styles.task_title}>
                                    {item.missionName}{' '}
                                    {item.targetCount >= 999 ? '' : `（${item.completeCount}/${item.targetCount}）`}
                                </View>
                                <View className={styles.task_desc}>
                                    <Image src={iconPoints} className={styles.icon} mode='aspectFit' />
                                    <Text className={styles.task_points}>+{item.points}</Text>
                                    <Text className={styles.task_tip}>{item.missionDesc}</Text>
                                </View>
                            </View>
                            {item.targetCount >= 999 ? (
                                <Button
                                    className={classNames(styles.task_button)}
                                    round
                                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                    onClick={gotoExcise}
                                >
                                    去练习
                                </Button>
                            ) : item.completeCount >= item.targetCount ? (
                                <Button
                                    className={classNames(styles.task_button, styles.task_button_disabled)}
                                    round
                                    disabled
                                >
                                    已完成
                                </Button>
                            ) : item.userPointType === 'DAILY_LOGIN' ? (
                                <Button
                                    onClick={handleSign}
                                    className={styles.task_button}
                                    round
                                    loading={false}
                                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                >
                                    签到
                                </Button>
                            ) : (
                                <Button
                                    className={styles.task_button}
                                    round
                                    onClick={gotoExcise}
                                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                                >
                                    待完成
                                </Button>
                            )}
                        </View>
                    ))}
                </View>
            </View>

            {showSign && (
                <View className={styles.section}>
                    <View className={styles.section_title}>签到日历</View>
                    <View className={styles.weeks}>
                        {weekDays.map((item) => (
                            <View key={item} className={styles.week}>
                                {item}
                            </View>
                        ))}
                    </View>
                    <View className={classNames(styles.calendar, monthShow ? styles.hide : styles.show)}>
                        {signWeek.map((item: any) => (
                            <View key={item.date}>
                                <View
                                    className={classNames(styles.calendar_sign, {
                                        [styles.calendar_sign_not]: item.status === -1
                                    })}
                                >
                                    {item.status === 0 && (
                                        <Image className={styles.calendar_status} src={iconSignMiss} />
                                    )}
                                    {item.status === 1 && (
                                        <Image className={styles.calendar_status} src={IconCheckActive} />
                                    )}

                                    {item.status === 2 && (
                                        <Image
                                            className={classNames(styles.calendar_status, styles.calendar_status_no)}
                                            src={iconSignNo}
                                        />
                                    )}
                                </View>
                                <View className={styles.calendar_day}>{item.date && item.date.format('M.D')}</View>
                            </View>
                        ))}
                    </View>

                    <View className={classNames(styles.calendar, monthShow ? styles.show : styles.hide)}>
                        {signMonth.map((item: any) => (
                            <View key={item.date}>
                                <View
                                    className={classNames(styles.calendar_sign, {
                                        [styles.calendar_sign_not]: item.status === -1
                                    })}
                                >
                                    {item.status === 0 && (
                                        <Image className={styles.calendar_status} src={iconSignMiss} />
                                    )}
                                    {item.status === 1 && (
                                        <Image className={styles.calendar_status} src={IconCheckActive} />
                                    )}

                                    {item.status === 2 && (
                                        <Image
                                            className={classNames(styles.calendar_status, styles.calendar_status_no)}
                                            src={iconSignNo}
                                        />
                                    )}
                                </View>
                                <View className={styles.calendar_day}>{item.date && item.date.format('M.D')}</View>
                            </View>
                        ))}
                    </View>

                    <View className={classNames(styles.calendar_more)} onClick={toggleMonthShow}>
                        <Icon
                            className={classNames(styles.calendar_more_icon, {
                                [styles.collapse]: monthShow
                            })}
                            name='arrow-down'
                            size={pxTransform(24)}
                            color='#9597A0'
                        />
                    </View>
                </View>
            )}
            <Toast_ />
        </Page>
    );
};

export default App;
