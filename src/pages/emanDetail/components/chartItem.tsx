import config from '@/config';
import type { EmanChatHistoryVo } from '@/types/chat';
import { Icon, Image } from '@antmjs/vantui';
import { Text, View } from '@tarojs/components';
import { pxTransform } from '@tarojs/taro';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import styles from './index.less';

interface Props extends EmanChatHistoryVo {
    onClick: (id: number | string) => void;
}

const chatIcon = `${config.cdnPrefix}eman/chat_icon.svg`;

const Index: React.FC<Props> = (props) => {
    const { createTime, chatId, status, score } = props;
    const [scoreContent, setScoreContent] = useState<any>();
    useEffect(() => {
        if (status === 1) {
            setScoreContent(<Text className={styles['item-score-none']}>无报告</Text>);
        } else if (status === 2) {
            setScoreContent(
                <View>
                    <Text className={styles['item-score-num']}>{score}</Text>
                    <Text className={styles['item-score-unit']}>分</Text>
                </View>
            );
        } else if (status === 3) {
            setScoreContent(<Text className={styles['item-score-fail']}>生成失败</Text>);
        } else if (status === 4) {
            setScoreContent(<Text className={styles['item-score-fail']}>生成中</Text>);
        } else {
            setScoreContent(<Text className={styles['item-score-afoot']}>进行中</Text>);
        }
    }, [score, status]);

    return (
        <View className={styles.item} onClick={() => props.onClick(chatId)}>
            <View className={styles['item-avatar']}>
                <Image round width={pxTransform(36)} height={pxTransform(36)} src={chatIcon} />
            </View>
            <View className={styles['item-content']}>
                <View className={styles['item-name']}>{props.scriptName}</View>
                <View className={styles['item-time']}>
                    {dayjs(createTime).isSame(dayjs(), 'year')
                        ? dayjs(createTime).format('MM月DD日 HH:mm')
                        : dayjs(createTime).format('YYYY年MM月DD日 HH:mm')}
                </View>
            </View>
            <View className={styles['item-score']}>{scoreContent}</View>

            <Icon className={styles['item-icon']} name='arrow' size={pxTransform(24)} color='#9597A0' />
        </View>
    );
};

export default Index;
