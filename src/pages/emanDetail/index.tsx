import { Page } from '@/components';
import config from '@/config';
import { getEnam } from '@/services/chat';
import type { EmanChatHistoryVo, EmanVO } from '@/types/chat';
import emanJob from '@/utils/emanJob';
import { Dialog, Icon, Image, NavBar } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import Taro, { pxTransform, useDidShow, useRouter } from '@tarojs/taro';
import { useRef, useState } from 'react';
import ChartItem from './components/chartItem';
import styles from './index.less';
import Star from "@/components/star";
import { AppIdConsts } from "@/constants/appid";
const HistoryEmpty = `${config.cdnPrefix}history_empty.png`;

const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    const { params } = useRouter<{ id: string }>();
    const { id } = params; // eManId
    const timer = useRef<any>();
    const [eManDetail, setEManDetail] = useState<EmanVO>({} as EmanVO); // 提示职业认证
    const [list, setList] = useState<EmanChatHistoryVo[]>([]);
    const [showStar, setShowStar] = useState(false);
    const accountInfo = Taro.getAccountInfoSync();
    const _getMyEManDetail = async () => {
        try {
            const res = await getEnam(id);
            setEManDetail(res.data.data);
            setList(res.data.data.chatList);

            setShowStar(accountInfo.miniProgram.appId === AppIdConsts.qnq && res.data.data.emanDifficult !== 0);
        } catch (error) {
            setList([]);
        }
    };
    useDidShow(() => {
        if (timer.current) {
            clearTimeout(timer.current);
        }
        timer.current = setTimeout(() => {
            _getMyEManDetail();
        }, 350);
    });

    const showDescript = () => {
        if (eManDetail.skill && eManDetail.skill.length > 20) {
            Dialog_.alert({
                title: '擅长方向',
                message: eManDetail.skill,
                messageAlign: 'left',
                confirmButtonText: '关闭',
                confirmButtonColor: '#4F66FF'
            }).then(() => {});
        }
    };
    const goDetail = (item: EmanChatHistoryVo) => {
        // done：是否结束对话。
        // status:1-无报告：看历史聊天记录，2-生成成功：看报告详情，3-生成失败：报告详情，4-生成中：报告详情点击重试后。
        if (eManDetail.type === 1) {
            if (item.status === 1) {
                Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${item.chatId}` });
            } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                Taro.navigateTo({ url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}` });
            } else {
                Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.chatId}` });
            }
        }
        if (eManDetail.type === 2) {
            Taro.navigateTo({
                url: `/pages/chat/enterpriseIntelligence/index?chatId=${item.chatId}`
            });
        }
    };

    return (
        <Page className={styles.page}>
            <Dialog_ />
            <View className={styles.container}>
                <View className={styles.background}>
                    <View
                        className={styles.avatar_image}
                        style={{
                            backgroundImage: `url(${eManDetail.background})`
                        }}
                    />
                    <View className={styles.empty} />
                </View>
            </View>
            <View className={styles.wrap}>
                <NavBar
                    safeAreaInsetTop
                    border={false}
                    onClickLeft={() => Taro.navigateBack()}
                    renderLeft={<Icon size={pxTransform(48)} name='cross' color='#ffffff' />}
                />
                <View className={styles.top_msg}>
                    <View className={styles.user_info}>
                        <Image
                            src={eManDetail.avatar}
                            fit='cover'
                            width={pxTransform(150)}
                            height={pxTransform(150)}
                            round
                            className={styles.avatar}
                        />
                        <View className={styles.info}>
                            <View className={styles.name}>{eManDetail.name}</View>
                            <View className={styles.account}>ID：{eManDetail.number}</View>
                        </View>
                    </View>
                    <View className={styles.other_msg}>
                        <View className={styles.job_line}>
                            <View className={styles.job_txt}>{eManDetail && emanJob(eManDetail.occupation, eManDetail.title, eManDetail.department)}</View>
                            {showStar && <View className={styles.eman_difficult}>
                                <View>拜访难度：</View>
                                <Star value={eManDetail.emanDifficult} />
                            </View>}
                        </View>

                        <View className={styles.other_msg_item}>
                            <View className={styles.description} onClick={showDescript}>
                                擅长方向: {eManDetail.skill}
                            </View>
                        </View>
                    </View>
                </View>
                <View className={styles.bottom_msg}>
                    <View className={styles.bottom_msgTitle}>对话记录</View>
                    <View className={styles.paginationList}>
                        {list && list.length ? (
                            list?.map((item) => (
                                <ChartItem key={item.chatId} {...item} onClick={() => goDetail(item)} />
                            ))
                        ) : (
                            <View className={styles.history_empty}>
                                <Image
                                    className={styles.history_empty_img}
                                    width={pxTransform(400)}
                                    height={pxTransform(205)}
                                    src={HistoryEmpty}
                                />
                                <View className={styles.history_empty_txt}>暂无对话记录</View>
                            </View>
                        )}
                    </View>
                </View>
            </View>
        </Page>
    );
};

export default App;
