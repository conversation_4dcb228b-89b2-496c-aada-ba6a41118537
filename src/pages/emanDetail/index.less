@import '@/styles/index.less';

page {
  --nav-bar-background-color: transparent;

  box-sizing: border-box;
  position: relative;
}

.container {
  position: relative;
  overflow: hidden;
}

.background{
  position: relative;
  z-index: 1;
}
.avatar_image{
  width: 100%;
  height:600px;
  background-position: top;
  background-size: cover;
  background-repeat: no-repeat;
}
.empty{
  width: 100%;
  height: 50px;
}
.wrap{
  width: 100%;
  position: absolute;
  z-index: 2;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(40px);
  left: 0;
  display: flex;
  flex-direction: column;
  top: 0;
}
.top_msg{
  width: 100%;
  box-sizing: border-box;
  padding:32px;
  color: #fff;
  margin-top: 30px;
  font-size: 24px;
}

.user_info{
  display: flex;
  align-items: center;
  gap:0 24px;

  .avatar{
    border: 1px solid #fff;
  }
  .info{
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 12px 0;
  }
  .name{
    font-size: 40px;
  }
  .account{
    opacity: 0.7;
  }


}
.other_msg{
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap:8px 0;
  .job {
    &_line {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .font(24px, rgba(255, 255, 255, 0.7),400);
    }

  }
  .other_msg_item{
    width: 100%;
    line-height: 32px;
  }
  .description{
    position: relative;
    .multi-ellipsis(2);
  }
.eman_difficult {
  .font(24px, rgba(255, 255, 255, 0.7),400);

  display: flex;
  flex-shrink: 0;
  align-items: center;
}
//   .description::before{
//     content: '';
//     float: right;
//     height: 100%;
//     margin-bottom: -32px;
//   }
//   .description::after {
//     content: '';
//     width: 100%;
//      background-color: rgba(0, 0, 0, 0);
//       backdrop-filter: blur(40px);
//     height: 100%;
//     position: absolute;
//     // background: #fff;
// }
  .detail{
    float: right;
    clear: both;
    // background-color: aqua;
  
  }

}

.bottom_msg{
  flex:1;
  height: 0;
  width: 100%;
  border-radius: 32px 32px 0 0;
  background: #fff;
  box-sizing: border-box;
  padding: 32px;
  letter-spacing: -1px;
  display: flex;
  flex-direction: column;
}

.bottom_msgTitle{
  line-height: 50px;
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 41px;
  position: relative;
  &::after {
    content: '';
    position: absolute;
    left: 2em;
    margin-left: -18px;
    display: block;
    width: 36px;
    height: 6px;
    background-color: #4F66FF;
    border-radius: 32px;
  }
}
.paginationList {
  flex: 1;
  height: 0;
  overflow-y: auto;
}
.history {
  &_empty {
    text-align: center;
    position: relative;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -40%);
    &_img {
        margin: auto;
    }

    &_txt {
        margin-top: 24px;
        color: #9597a0;
        text-align: center;
        font-size: 28px;
        line-height: 40px;
    }
}
}