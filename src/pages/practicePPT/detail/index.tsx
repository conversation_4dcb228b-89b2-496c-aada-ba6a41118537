import { useEvent, useStorage } from '@/common';
import { Page } from '@/components';
import HarmonyDialog from '@/components/HarmonyDialog';
import { useLockFn } from '@/hooks';
import { createChatppt, doneChat, getChat, getScript } from '@/services/chat';
import { checkHarmony, checkRecordPermission } from '@/utils/permission';
import { ActionSheet, Cell, CellGroup, Dialog, Icon, Image, NavBar, Radio, RadioGroup, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, ScrollView, Text, View } from '@tarojs/components';
import Taro, { pxTransform, useLoad, useRouter } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import { useCallback, useEffect, useState } from 'react';
import styles from './index.less';
const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    const { pptId, from, stageId, goChallenge } = useRouter().params;
    function goBack() {
        Taro.navigateBack();
    }

    const [listDetail, setListDetail] = useState<any>();
    const [requireVisible, setRequireVisible] = useState<boolean | null>(true); // 后台是否显示演练要求
    const [showActionSheetUnend, setShowActionSheetUnend] = useState(false);
    const [unCloseChatId, setUnCloseChatId] = useState<string>('');
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    // useEffect(() => {
    //     getScript()

    //     setListDetail(aaa);
    // }, []);

    useEffect(() => {
        try {
            getScript(pptId || '').then((listRes: any) => {
                setListDetail(listRes.data.data);
                setRequireVisible(listRes.data.data.requirementShowFlag);
            });
        } catch (error) {
            console.log(error);
        }
    }, [pptId]);
    const goPPt = () => {
        // navigateTo({
        //     url: `/pages/practicePPT/detail/index?scriptId=${id}&from=scriptList`
        // });
    };
    const checkUndone = useEvent(async () => {
        try {
            try {
                const params: any = {
                    scriptId: pptId
                };
                if (from === 'checkpoint') {
                    params.stageId = stageId;
                }
                const { data } = await createChatppt(params, false);

                if (data.code === 200) {
                    const url = `/pages/practicePPT/practice/index?chatId=${data?.data?.id}&pptId=${pptId}&text=${goChallenge}`;
                    console.log('跳转幻灯片录音页面');
                    Taro.navigateTo({
                        url
                    });
                }
            } catch (error: any) {
                const { data } = error;
                if (data) {
                    if (data.code === 10002) {
                        // 有未结束的对话
                        setShowActionSheetUnend(true);
                        setUnCloseChatId(data.data);
                    } else if (data.code === 401) {
                        Taro.showToast({
                            icon: 'none',
                            title: '登录过期，重新登录',
                            mask: true
                        });
                    } else {
                        Taro.showToast({
                            icon: 'none',
                            title: data.message
                        });
                    }
                } else {
                    Taro.showToast({
                        icon: 'none',
                        title: '网络错误'
                    });
                }
            }
        } catch (error) {
            Taro.showToast({
                title: '录音授权失败',
                icon: 'none'
            });
        }
    });
    const [handleStartPractice, startPracticeLoading] = useLockFn(async () => {
        try {
            console.log('asjknaassjndskjsnka');
            // Taro.navigateTo({
            //     url: '/pages/practicePPT/practice/index'
            // });
            const isHarmony = await checkHarmony();
            console.log('isHarmony', isHarmony);
            if (isHarmony) {
                setShowHarmonyDialog(true);
            } else {
                await checkRecordPermission(Dialog_);
                await checkUndone();
            }
        } catch (error) {
            Taro.showToast({
                title: '录音授权失败',
                icon: 'none'
            });
        }
    });
    const { run: continueChat } = useThrottleFn(
        async () => {
            // 继续对话
            try {
                // const res = await getChat(unCloseChatId);
                // const { data } = res.data;
                // let url;
                const url = `/pages/practicePPT/practice/index?chatId=${unCloseChatId}`;

                Taro.navigateTo({ url });
                setUnCloseChatId('');
                setShowActionSheetUnend(false);
            } catch (error) {
                console.log(error);
            }
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    const { run: newChat } = useThrottleFn(
        async () => {
            // 结束并开启新对话
            try {
                await doneChat(unCloseChatId, false);
                checkUndone();
            } catch (error) {
                console.log(error);
            }
            setUnCloseChatId('');
            setShowActionSheetUnend(false);
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    // 处理关键词拼接
    const getKeywordsStr = (keywords: string) => {
        const keywordList = JSON.parse(keywords || '[]');
        if (Array.isArray(keywordList)) {
            return keywordList
                .reduce((pre: string[], cur: { name: string; synonyms: string[]; remark: string }) => {
                    const synonyms = cur.synonyms || [];
                    const name = cur.name || '';
                    const kwsList = [name, ...synonyms].filter(Boolean).join('/');
                    return [...pre, kwsList];
                }, [])
                .join('、');
        }
        return '无';
    };
    return (
        <Page className={styles.page}>
            <NavBar title='幻灯片详情' leftArrow safeAreaInsetTop onClickLeft={() => goBack()} />

            <ScrollView className={styles.content} scrollY lowerThreshold={50}>
                {listDetail && <View className={styles.title_font}>{listDetail.name}</View>}

                {listDetail &&
                    listDetail?.ppt?.map((item: any, index: any) => {
                        return (
                            <View
                                key={index}
                                className={index + 1 == listDetail?.ppt.length ? styles.cardone : styles.card}
                                onClick={goPPt}
                            >
                                <View className={styles.tip_litter}>P{index + 1}</View>
                                <View className={styles.history_user}>
                                    <Image
                                        style={{
                                            borderRadius: `${pxTransform(16)} ${pxTransform(16)} 0 0`,
                                            width: '100%'
                                        }}
                                        className={styles.imagey}
                                        radius={pxTransform(16)}
                                        height={pxTransform(376)}
                                        fit='cover'
                                        src={item.imageUrl}
                                    />
                                </View>
                                {requireVisible && (
                                    <Block>
                                        {listDetail.pptScoringType === 1 && (
                                            <View className={styles.request_img}>
                                                <View className={styles.request_text}>演练要求</View>
                                                <View className={styles.request_context}>
                                                    {item.requirement || '无'}
                                                </View>
                                            </View>
                                        )}
                                        {[2, 3].includes(listDetail.pptScoringType) && (
                                            <View className={styles.request_img}>
                                                <View className={styles.request_text}>关键词</View>
                                                <View className={styles.request_context}>
                                                    {getKeywordsStr(item.keywords) || '无'}
                                                </View>
                                            </View>
                                        )}
                                    </Block>
                                )}
                            </View>
                        );
                    })}
            </ScrollView>
            <Button
                className={styles.btn_start_practice}
                round
                block
                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                onClick={handleStartPractice}
                loading={startPracticeLoading}
            >
                <Text>开始练习</Text>
            </Button>
            <ActionSheet
                show={showActionSheetUnend}
                onClose={() => setShowActionSheetUnend(false)}
                title={
                    <View className={styles.actionsheet_title}>
                        <View>检测到未结束的练习</View>
                    </View>
                }
                closeOnClickOverlay={false}
                style={{
                    '--action-sheet-close-icon-color': '#272C47',
                    '--action-sheet-close-icon-padding': `${pxTransform(0)} ${pxTransform(32)} 0 ${pxTransform(32)}`
                }}
            >
                <Button.Group direction='vertical'>
                    <Button
                        block
                        round
                        color='linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)'
                        onClick={continueChat}
                    >
                        继续练习
                    </Button>
                    <Button block round color='#F6F6F6' style='color: #777777' onClick={newChat}>
                        结束并开启新练习
                    </Button>
                </Button.Group>
            </ActionSheet>
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='B' />
            <Dialog_ />
        </Page>
    );
};
export default App;
