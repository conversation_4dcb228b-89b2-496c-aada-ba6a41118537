page {
    background: linear-gradient(197deg, rgba(221, 222, 255, 0.6) 0%, rgba(246, 246, 246, 0.6) 24%),
        linear-gradient(158deg, #cff7f4 0%, #d1ebff 12%, #fff 35%);

    --nav-bar-background-color: transparent;
    --nav-bar-icon-color: #333;
    --nav-bar-arrow-size: 48px;
    --dropdown-menu-background-color: transparent;
    --dropdown-menu-title-font-size: 24px;
    --dropdown-menu-height: 56px;
    --tabs-bottom-bar-width: 54px;
    :global {
        .van-tab--active {
            font-weight: bold;
        }
        .van-tabs__scroll {
            background: none;
        }
        .van-dropdown-menu {
            gap: 26px;
            padding-bottom: 18px;
        }
        .van-dropdown-menu__item {
            padding: 0 20px;
            border: 2px solid rgba(39, 44, 71, 0.2);
            border-radius: 16px;
        }

        .van-dropdown-menu__title {
            flex: 1;
            padding-left: 0;
            &::after {
                right: 0;
                width: 12px;
                height: 12px;
                border: 2px solid #272c47;
                border-width: 0 2px 2px 0;
                transform: rotate(45deg);
                // 向下的三角箭头
                content: '';
            }
        }
        .van-dropdown-menu__title--active {
            color: #333;
            &::after {
                transform: rotate(-135deg);
            }
        }
        .van-tab--active-line {
            background: #4f66ff;
          
        }
       
        .at-calendar__list.flex .flex__item--selected {
            background-color: #4f66ff !important;
        }
    }
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.content {
    margin-top: 20px;
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-bottom: env(safe-area-inset-bottom);
  }
.title_font{
font-size: 30px;
font-weight: 500;
line-height: normal;
letter-spacing: 0px;
color: #333333;
margin: 24rpx 30rpx 0rpx 30rpx;
}
.content {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-top: 20px;
    margin-bottom: 20px;
    overflow-y: auto;
  }
.card {
    background-color: #fdfdfd;
    border-radius: 16px   ;
    position: relative;
    margin: 28px 30px 0;
    // padding: 32px  32px 32px 22px;
    box-sizing: border-box;
  }
 .cardone{
    background-color: #fdfdfd;
    border-radius: 16px   ;
    position: relative;
    padding-bottom: 170px;
    margin: 28px 30px 0;
    // padding: 32px  32px 32px 22px;
    box-sizing: border-box;
 }
  .request_img{
    display: flex;
    padding: 16px  24px 24px 24px;
    .request_text{
        margin-right: 10px;
        font-size: 24px;
        color: #272C47;
    }
    .request_context{
        // width: 574rpx;
        flex: 1;
        font-size:  24px;
        color: #67686F;
    }
  }
  .imagey{
    .van-image__img{
        border-radius: 16px  16px 0px 0px !important;
    }
    .image-class  {
        border-radius: 16px  16px 0px 0px !important;
    }
  }
  .history_user{
    border-radius: 16px  16px 0px 0px !important;
  }
  .tip_litter{
    position: absolute;
    top: 0;
    color: #fff;
    width:84px;
    display: flex;
    justify-content: center;
    // text-align: center;
    align-items: center;
    z-index: 1;
    height: 52px;
    border-radius: 16px 0px 16px 0px;
    background: rgba(12, 12, 12, 0.6);
  }
  .btn_start_practice{
    height: 88px;
    width: 690px;
    z-index: 2;
 right: 32px;
   position: fixed;
    bottom: 100px;
  }