 
  .page {
    position: fixed;
  width: '100%';
  height: '100%';
  background-color: #000; // 与页面背景一致
  overflow: hidden 
  }
 
  .loading {
    &_box {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 500;
      width: 100%;
      height: 100vh;
      // background-color: #fff;
    }
    &_hide {
      display: none;
    }
  }
.history_box {
  display: flex;
  flex-direction: column;
}
.history_context {
  flex: 1;
  height: 0;
  overflow-y: auto;
}
.navbar {
    z-index: 0;
}
.container_page_portrait{
  width: 100vw;
  height: 100vh;
}

.container_page_landscape{
  width: 100vw;
  height: 100vh;
}
 