import config from '@/config';
import { ActionSheet, Image } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import styles from './index.less';
type Props = {
    show: boolean;
    showCleanContext?: boolean;
    onClose: () => void;
    onScript?: () => void;
    require?: string;
    onSetting: () => void;
    onCleanContext?: () => void;
};

const Index: React.FC<Props> = (props) => {
    const { show, onClose, require } = props;
    return (
        <ActionSheet
            className={styles.action_more}
            zIndex={110}
            show={show}
            onClose={onClose}
            closeOnClickOverlay={false}
            style={{
                // @ts-ignore
                '--action-sheet-close-icon-color': '#272C47'
            }}
            title='演练要求'
        >
            <View className={styles.action_more_content}>
                <View className={styles.action_more_item}>{require}</View>
            </View>
        </ActionSheet>
    );
};

export default Index;
