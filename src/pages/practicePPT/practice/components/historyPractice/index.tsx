import config from '@/config';
import { DateTimeFormatEnum } from '@/constants/dateTime';
import { Text, View } from '@tarojs/components';
import dayjs from 'dayjs';

import AudioPlayer from '@/components/audioPlayer';
import { Image, pxTransform } from '@antmjs/vantui';
import styles from './index.less';
const App = (props: any) => {
    const { scriptTime, summary, url, chatId } = props;
    return (
        <View className={styles.history_context}>
            {scriptTime && dayjs(scriptTime).isSame(dayjs(), 'day') ? (
                <View className={styles.history_context_time}>{dayjs(scriptTime).format(DateTimeFormatEnum.HM)}</View>
            ) : dayjs(scriptTime).isSame(dayjs().subtract(1, 'day'), 'day') ? (
                <View className={styles.history_context_time}>
                    昨天 {dayjs(scriptTime).format(DateTimeFormatEnum.HM)}
                </View>
            ) : dayjs(scriptTime).isSame(dayjs(), 'year') ? (
                <View className={styles.history_context_time}>
                    {dayjs(scriptTime).format(DateTimeFormatEnum.MONTHTIMEMIN)}
                </View>
            ) : (
                <View className={styles.history_context_time}>
                    {dayjs(scriptTime).format(DateTimeFormatEnum.DATETIMEMIN)}
                </View>
            )}

            {summary ? (
                <View className={styles.history_text}>
                    {url && <AudioPlayer url={url} id={chatId} />}
                    <View className={styles.history_text_context}>{summary}</View>
                </View>
            ) : (
                <View className={styles.history_empty}>
                    <Image
                        fit='cover'
                        width={pxTransform(400)}
                        height={pxTransform(200)}
                        src={`${config.cdnPrefix}slide/no_record.png`}
                    />
                    <View className={styles.history_empty_txt}>无演练记录</View>
                </View>
            )}
        </View>
    );
};

export default App;
