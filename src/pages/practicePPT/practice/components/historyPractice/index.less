 
.history_context {
    padding: 24px 32px; // 微信顶部高度+'内容由ai生成'tips栏
    &_time {
        font-size: 24px;
        color: #9597a0;
        margin-bottom: 0;
        text-align: center;
    }
    
}
.history_text{
    margin-right: 32px;
    height: calc(100vh - 350px);
    margin-left: 32px;
font-size: 28px;
color: #272C47;
    white-space: pre-wrap;
}
.history_text_context{
    padding-bottom: 60px;
}
.history_textp{
    margin-bottom: 32px;
}
.nohistory_text{
    display: flex;
    justify-content: center;
    height: calc(100vh - 350px);
    align-items: center;
}
.nohistory_textStylee{
    display: flex;
    justify-content: center;
    color: #9597a0;
}
.history {
    padding-top: 40px;

 
    &_empty {
        margin-top: 250px;
        text-align: center;

        &_img {
            margin: auto;
        }

        &_txt {
            margin-top: 24px;
            color: #9597a0;
            text-align: center;
            font-size: 28px;
            line-height: 40px;
        }
    }
}
