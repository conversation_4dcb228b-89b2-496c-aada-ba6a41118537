import config from '@/config';
import { ActionSheet, Image } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import styles from './index.less';
type Props = {
    show: boolean;
    onClose: () => void;
    onFeedback: () => void;
};

const IconFeedback = `${config.cdnPrefix}svg/icon_feedback.svg`;

const Index: React.FC<Props> = (props) => {
    const { show, onClose, onFeedback } = props;
    return (
        <ActionSheet
            className={styles.action_more}
            zIndex={110}
            show={show}
            onClose={onClose}
            closeOnClickOverlay={false}
            style={{
                // @ts-ignore
                '--action-sheet-close-icon-color': '#272C47'
            }}
            title='更多'
        >
            <View className={styles.action_more_content}>
                <View className={styles.action_more_item} onClick={onFeedback}>
                    <Image src={IconFeedback} className={styles.action_more_item_icon} />
                    <View className={styles.action_more_item_text}>问题反馈</View>
                </View>
            </View>
        </ActionSheet>
    );
};

export default Index;
