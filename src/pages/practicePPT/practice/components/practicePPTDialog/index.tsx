import IconHelpOff from '@/assets/icon_help_off.svg';
import IconLoading from '@/assets/loading.svg';
import { Storage, useEvent, useRequest, useStorage } from '@/common';
import Aihelp from '@/components/aihelp';
import Aihelpheng from '@/components/aihelp/aihlepDiolog';
import HarmonyDialog from '@/components/HarmonyDialog';
import ImagePreview from '@/components/imagePreview';
import VoiceLoading from '@/components/voiceLoading';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import type { RecognizeStatus } from '@/constants/voicetype';
import { VoiceStatus } from '@/constants/voicetype';
import { doneChat, doneGenerateReport, generateReport, pptPageDone, savePPTSummary } from '@/services/chat';
import { getServerTime } from '@/services/common';
import type { PPTItem } from '@/types/chat';
import { ChatActionSheetType, type ChatVO } from '@/types/chat';
import type { LogInfo } from '@/types/common';
import { checkHarmony } from '@/utils/permission';
import { RecordUploader } from '@/utils/RecordUploader';
import SpeechRecognizer from '@/utils/speechRecognizer/SpeechRecognizer';
import { guid } from '@/utils/speechRecognizer/utils';
import { ActionSheet, Dialog, Icon, Image as Imagea, NavBar, Popup, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Block, Image, ScrollView, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import Taro, { Events, getDeviceInfo, pxTransform, useDidShow } from '@tarojs/taro';
import { useCountDown, useThrottleFn, useUnmount } from 'ahooks';
import { default as classnames, default as classNames } from 'classnames';
import dayjs from 'dayjs';
import dayjsDuration from 'dayjs/plugin/duration';
import type { ReactNode } from 'react';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import IconRequirementHorizonOff from '../../../assets/requirement_horizon_off.svg';
import IconRequirementHorizonOn from '../../../assets/requirement_horizon_on.svg';
import IconRequirementOff from '../../../assets/requirement_off.svg';
import VoiceWave from './generateBars';
import styles from './index.less';
const IconHelp = `${config.cdnPrefix}aihelp/icon_help.svg`;
const IconChat = `${config.cdnPrefix}dialogue/icon_chat.png`;
const IconComplete = `${config.cdnPrefix}slide/complete_icon.png`;
const IconNextStep = `${config.cdnPrefix}slide/nextStep_icon.png`;
const IconRequirementOn = `${config.cdnPrefix}slide/Frame_icon.png`;
const IconHangupMini = `${config.cdnPrefix}svg/icon_hangup_mini.svg`;
const IconMore = `${config.cdnPrefix}svg/icon_more.svg`;
const IconSpeak = `${config.cdnPrefix}svg/icon_speak.svg`;

type Props = {
    listDetail?: ChatVO;
    pptId?: any;
    showHistoryPractice: () => void;
    showMore: () => void;
    addLog: (log: LogInfo) => void;
    summary: string;
    saveSummary: (type: 'temp' | 'sentence', txt: string) => string;
    texturl: string;
    orientation: any;
};
dayjs.extend(dayjsDuration);
const events = new Events();
const ToastVoiceDialog_ = Toast.createOnlyToast();
const Dialog_ = Dialog.createOnlyDialog();
const App = (props: Props) => {
    const FINISH_EVENT_NAME = 'finish';
    const RESTART_EVENT_NAME = 'restart';
    const { listDetail, showHistoryPractice, showMore, addLog, saveSummary, summary, pptId, texturl, orientation } =
        props;
    const pptData = useRef<PPTItem[]>();

    const [status, setStatus] = useState<any>();
    const [btnText, setBtnText] = useState(''); // 按钮文字
    const [btnIcon, setBtnIcon] = useState(''); // 按钮图标
    const [currentIndex, setCurrentIndex] = useState<number>(); // 当前显示的ppt
    const [currentPPTIndex, setCurrentPPTIndex] = useState<number>(); // 当前录音的ppt
    const currentPPTIndexRef = useRef<number>(); // 当前录音的ppt
    const currentPPTIndexIdRef = useRef<string>(''); // 当前录音的pptId
    const [currentPPTIndexId, setCurrentPPTIndexId] = useState<string>('');

    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);
    const [aiHelpShow, setAiHelpShow] = useState<boolean>(false);
    const statusRef = useRef<VoiceStatus>(); // 当前要开始的录音状态
    const [statusLoading, setStatusLoading] = useState<ReactNode>();
    const [canGenerate, setCanGenerate] = useState<boolean>(true);
    const isIOS = getDeviceInfo().platform === 'iOS' || getDeviceInfo().platform === 'ios';
    const createTime = useRef<string>('');
    const timeLimit = useRef(0);
    const [aiHelpEnable, setAiHelpEnable] = useState(true);

    const [leftTime, setLeftTime] = useState<number>();
    const [leftTimeText, setLeftTimeText] = useState<string>('');
    const isTimeout = useRef(false); // 倒计时结束

    const chatDone = useRef(false); // 对话是否结束

    const [currentRequirement, setCurrentRequirement] = useState<string>('');
    const [requirementVisible, setRequirementVisible] = useState<boolean>(true); // 开启关闭
    const [requireVisible, setRequireVisible] = useState<boolean | null>(true); // 后台是否显示演练要求

    const [decibelPlay, setdecibelPlay] = useState<any>('');
    const [isZore, setisZore] = useState<any>('');
    let isadd = 1;

    const swiperRef = useRef<any>(null);

    const [showImagePreview, setShowImagePreview] = useState<boolean>(false);
    const [currentPreviewImage, setCurrentPreviewImage] = useState<string>();

    const speechRecognizerManager = useRef<SpeechRecognizer>(); // 获取实时录音识别管理器
    const recorder = useRef<Taro.RecorderManager>();
    const recordUploader = useRef<RecordUploader>();
    const isCanSendData = useRef(false);
    const [chatActionSheetType, setChatActionSheetType] = useState<ChatActionSheetType>(); // 结束报告弹窗中按钮显示控制
    const [showActionSheet, setShowActionSheet] = useState(false); // 对话结束报告弹窗

    const pageStatus = useRef<'mount' | 'unmount'>('mount');
    const recordStatus = useRef<'start' | 'pause' | 'stop'>(); // 录音管理器状态
    const recognizeStatus = useRef<boolean>(false); // 识别管理器状态
    const recorderUploaderStatus = useRef<boolean>(false); // 录音上传器状态
    const isFinish = useRef(false); // 是否是结束
    const finishType = useRef<'report' | 'end' | 'timeout' | ''>('');
    const stopType = useRef<'active' | 'passive' | ''>(''); // 主动/被动结束
    const pageDoneStatus = useRef<boolean>(false);
    const tempSentence = useRef('');
    const [isQnq, setIsQnq] = useState(false);
    const finishTimer = useRef<any>();
    const recognizeErrorCount = useRef(0);
    const [showHarmonyDialog, setShowHarmonyDialog] = useState<boolean>(false);
    const recorderParams = {
        duration: 600000,
        // duration: 60000,
        format: 'PCM',
        sampleRate: 16000,
        encodeBitRate: 48000,
        frameSize: 0.32,
        numberOfChannels: 1 // 实时识别只支持单声道
    };
    const scrollRef = useRef<any>(null);

    const touchStartX = useRef(0);

    const uploadSummaryStatus = useRef<boolean>(false); // 上传总结状态

    // 处理关键词拼接
    const getKeywordsStr = (keywords: string) => {
        const keywordList = JSON.parse(keywords || '[]');
        if (Array.isArray(keywordList)) {
            return keywordList
                .reduce((pre: string[], cur: { name: string; synonyms: string[]; remark: string }) => {
                    const synonyms = cur.synonyms || [];
                    const name = cur.name || '';
                    const kwsList = [name, ...synonyms].filter(Boolean).join('/');
                    return [...pre, kwsList];
                }, [])
                .join('、');
        }
        return '无';
    };
    /**
     * 如果存在演练要求，展示演练要求
     * 不存在演练要求，展示关键词
     */
    const getShowContent = (pptItem: any, pptScoringType: 1 | 2 | 3) => {
        const requirement = pptItem?.requirement;
        const keywords = pptItem?.keywords;
        return pptScoringType === 1 ? requirement : getKeywordsStr(keywords);
    };

    // 重置垂直滚动
    useEffect(() => {
        Taro.createSelectorQuery()
            .select('#verticalScroll')
            .node()
            .exec((res) => {
                res[0]?.node?.scrollTo({ top: 0 });
                setCurrentRequirement(
                    getShowContent(listDetail?.pptList[currentIndex], listDetail?.script?.pptScoringType) || '无'
                );
            });
    }, [currentIndex]);

    const previewImages = useMemo(() => {
        return listDetail ? listDetail.pptList.map((item: PPTItem) => item.imageUrl) : [];
    }, [listDetail]);

    const bg = useMemo(() => {
        const bgimage =
            isWework == '1'
                ? (listDetail && currentIndex !== undefined && listDetail.pptList[currentIndex].imageUrl) || ''
                : (listDetail && currentPPTIndex !== undefined && listDetail.pptList[currentIndex].imageUrlback) || '';
        return bgimage;
    }, [listDetail, currentIndex]);

    // const currentPPTIndexId = useMemo(() => {
    //     if (listDetail && currentPPTIndex !== undefined) {
    //         return listDetail.pptList[currentPPTIndex].id;
    //     } else {
    //         return '';
    //     }
    // }, [currentPPTIndex, listDetail]);

    useEffect(() => {
        handleAiHelpClose();
        setCurrentIndex(currentPPTIndex);
    }, [currentPPTIndex]);

    const changeStatus = (status: VoiceStatus) => {
        addLog({
            level: 'debug',
            message: 'changeStatus',
            data: {
                status
            }
        });
        statusRef.current = status;
        setStatus(status);
        switch (status) {
            case VoiceStatus.Listening:
                setStatusLoading(<VoiceLoading type='barAnimateheng' />);

                break;
            default:
                setStatusLoading(null);

                break;
        }
    };
    const changePPTIndex = useEvent((index: number) => {
        addLog({
            level: 'debug',
            message: 'changePPTIndex',
            data: {
                index
            }
        });
        setCurrentPPTIndex(index);
        currentPPTIndexRef.current = index;
        if (listDetail) {
            const cId = listDetail.pptList[index].id;
            setCurrentPPTIndexId(cId);
            currentPPTIndexIdRef.current = cId;
        } else {
            setCurrentPPTIndexId('');
            currentPPTIndexIdRef.current = '';
        }
    });
    const { run: handleAIHelp } = useThrottleFn(
        () => {
            if (statusRef.current === VoiceStatus.Speaking) {
                ToastVoiceDialog_.show({
                    message: 'AI说话中无法进行求助'
                });
            } else {
                setAiHelpShow(true);
            }
        },
        {
            wait: 250,
            leading: true,
            trailing: false
        }
    );

    const uploadSummary = useCallback(
        async (summary: string, cpid: string) => {
            // addLog({
            //     level: 'debug',
            //     message: 'uploadSummary',
            //     data: {
            //         currentPPTIndexId: cpid,
            //         summary,
            //         chatDone: chatDone.current,
            //         isTimeout: isTimeout.current
            //     }
            // });
            console.log('uploadSummary', {
                currentPPTIndexId: cpid,
                summary,
                chatDone: chatDone.current,
                isTimeout: isTimeout.current
            });
            if (!chatDone.current && !isTimeout.current && pageStatus.current === 'mount' && summary) {
                try {
                    await savePPTSummary(listDetail?.id, cpid, summary);
                } catch (error) {
                    console.log('savePPTSummary', error);
                    addLog({
                        level: 'error',
                        message: 'savePPTSummary',
                        data: {
                            error
                        }
                    });
                } finally {
                    if (isFinish.current) {
                        events.trigger(FINISH_EVENT_NAME);
                    }
                }
            }
        },
        [listDetail]
    );

    const handlePageDone = useEvent(async () => {
        pageDoneStatus.current = false;
        addLog({
            level: 'debug',
            message: 'handlePageDone',
            data: {
                id: listDetail?.id,
                pptId: currentPPTIndexIdRef.current
            }
        });
        try {
            await pptPageDone(currentPPTIndexIdRef.current);
        } catch (error) {
            console.log(error);
        }
        pageDoneStatus.current = true;
        if (isFinish.current) {
            events.trigger(FINISH_EVENT_NAME);
        }
    });

    const stopRecorder = () => {
        recorder.current?.stop();
    };
    const stopRecognizer = () => {
        speechRecognizerManager.current?.stop();
    };
    const closeRecorderUploader = () => {
        if (recordUploader.current) {
            recordUploader.current.close();
        }
    };

    function stopSocket() {
        addLog({
            level: 'trace',
            message: 'stopSocket'
        });
        closeRecorderUploader();
        stopRecognizer();
    }
    function stopAll() {
        stopSocket();
        stopRecorder();
    }

    function validateBuffer(buffer: any) {
        const { byteLength } = buffer;

        // 确保长度是2的倍数（每个样本占2字节）
        if (byteLength % 2 !== 0) {
            buffer = buffer.slice(0, byteLength - 1);
        }

        return new Int16Array(buffer);
    }
    // 音量计算函数
    function calculateDecibel(buffer: any) {
        if (buffer) {
            // 转换为16位整型数组（微信小程序默认PCM格式）
            const int16Array = validateBuffer(buffer);

            // 计算均方根值（RMS）
            let sum = 0;
            for (const sample of int16Array) {
                sum += sample * sample;
            }
            const rms = Math.sqrt(sum / int16Array.length);

            // 转换为分贝值（参考公式：dB = 20 * log10(rms / 32768))
            const decibel = 20 * Math.log10(rms / 32768);

            // 标准化到0-100区间（静音≈30dB，正常说话≈60dB）
            const minDb = -90; // 最小分贝值
            const maxDb = 0; // 最大分贝值
            return Math.max(0, Math.min(100, ((decibel - minDb) / (maxDb - minDb)) * 100));
        } else {
            return 0;
        }
    }

    const handleSentence = useEvent(async (type: 'temp' | 'sentence', text: string) => {
        addLog({
            level: 'debug',
            message: 'handleSentence',
            data: {
                text,
                currentPPTIndex: currentPPTIndexRef.current,
                currentPPTIndexId: currentPPTIndexIdRef.current,
                type,
                tempSentence: !!tempSentence.current
                // answer: pptData.current[currentPPTIndex].answer
            }
        });

        if (type === 'temp') {
            try {
                tempSentence.current = text;
                // 当前页old+temp，上传
                const uploadText = `${pptData.current[currentPPTIndexRef.current].answer || ''}${text}\n\n`;
                saveSummary(type, text);
                await uploadSummary(uploadText, currentPPTIndexIdRef.current);
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'handleSentence temp',
                    data: {
                        error
                    }
                });
            }
        } else {
            uploadSummaryStatus.current = true;
            tempSentence.current = '';
            try {
                // 当前页old+=text，上传
                pptData.current[currentPPTIndexRef.current].answer = `${
                    pptData.current[currentPPTIndexRef.current].answer || ''
                }${text ? `${text}\n\n` : ''}`;
                saveSummary(type, text ? `${text}\n\n` : '');
                await uploadSummary(
                    `${pptData.current[currentPPTIndexRef.current].answer}`,
                    currentPPTIndexIdRef.current
                );
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'handleSentence sentence',
                    data: {
                        error
                    }
                });
            } finally {
                uploadSummaryStatus.current = false;
                addLog({
                    level: 'trace',
                    message: 'trigger RESTART_EVENT_NAME'
                });
                events.trigger(RESTART_EVENT_NAME);
            }
        }
    });
    const handleRecorderError = useEvent(() => {
        addLog({
            level: 'trace',
            message: 'handleRecorderError'
        });
        stopSocket();
        stopRecorder();
        Taro.redirectTo({
            url: `/pages/practicePPT/practice/index?chatId=${listDetail.id}&from=history`
        });
    });
    // 初始化创建语音识别并且开始语音识别
    const startRecognizer = () => {
        addLog({
            level: 'trace',
            message: 'startRecognizer'
        });
        if (!speechRecognizerManager.current) {
            speechRecognizerManager.current = new SpeechRecognizer(
                {
                    secretkey: config.QCloudAIVoice.secretKey,
                    secretid: config.QCloudAIVoice.secretId,
                    appid: config.QCloudAIVoice.appId, // 腾讯云账号appid（非微信appid）
                    engine_model_type: '16k_zh_medical',
                    voice_format: 1, // 音频格式，1：pcm，8：mp3，默认为1,
                    needvad: 1, // 0：关闭 vad，1：开启 vad，默认为0。如果语音分片长度超过60秒，用户需开启 vad（人声检测切分功能）
                    vad_silence_time: 2000, // 语音断句检测阈值，静音时长超过该阈值会被认为断句（多用在智能客服场景，需配合 needvad = 1 使用），取值范围：240-2000（默认1000），单位 ms
                    noise_threshold: 0.2, // 噪音参数阈值，默认为0，取值范围：[-1,1]，对于一些音频片段，取值越大，判定为噪音情况越大。取值越小，判定为人声情况越大。慎用：可能影响识别效果
                    hotword_id: config.QCloudAIVoice.hotword_id,
                    customization_id: config.QCloudAIVoice.customization_id,
                    convert_num_mode: 3 // 0：不转换，直接输出中文数字，1：根据场景智能转换为阿拉伯数字，3: 打开数学相关数字转换。默认值为1
                    // max_speak_time: 10000 // 强制断句功能
                },
                guid(),
                false
            );
            speechRecognizerManager.current.OnRecognitionStart = (res: any) => {
                addLog({
                    level: 'debug',
                    message: '开始识别',
                    data: {
                        res,
                        status: statusRef.current
                    }
                });

                if (recorder.current) {
                    isCanSendData.current = true;
                    changeStatus(VoiceStatus.Listening);
                } else {
                    speechRecognizerManager.current?.stop();
                }
            };
            speechRecognizerManager.current.OnSentenceBegin = (res: any) => {
                addLog({
                    level: 'debug',
                    message: '一句话开始',
                    data: {
                        res,
                        status: statusRef.current
                    }
                });

                const text = res.result.voice_text_str.trim();
                if (text) {
                    handleSentence('temp', text);
                }
            };
            speechRecognizerManager.current.OnRecognitionResultChange = (res: any) => {
                console.log('识别变化', res);
                const text = res.result.voice_text_str.trim();
                if (text) {
                    handleSentence('temp', text);
                }
            };
            speechRecognizerManager.current.OnSentenceEnd = (res: any) => {
                if (res.result.word_size === 0) {
                    addLog({
                        level: 'debug',
                        message: '一句话结束',
                        data: {
                            sentence: {
                                voice_id: res.voice_id,
                                message_id: res.message_id,
                                slice_type: res.result.slice_type,
                                start_time: res.result.start_time,
                                end_time: res.result.end_time,
                                voice_text_str: res.result.voice_text_str.length
                            },
                            status: statusRef.current
                        }
                    });
                    const text = res.result.voice_text_str;

                    handleSentence('sentence', text);
                } else {
                    addLog({
                        level: 'debug',
                        message: '一句话结束重复',
                        data: {
                            res,
                            status: statusRef.current
                        }
                    });
                    console.warn('一句话结束 重复', res);
                }
            };
            speechRecognizerManager.current.OnRecognitionComplete = (res: any) => {
                addLog({
                    level: 'debug',
                    message: '识别结束',
                    data: {
                        res,
                        status: statusRef.current
                    }
                });
            };
            speechRecognizerManager.current.OnError = (res: any) => {
                addLog({
                    level: 'error',
                    message: '识别错误',
                    data: res
                });

                isCanSendData.current = false;
                stopSocket();

                stopType.current = 'passive';
                if (pageStatus.current === 'mount') {
                    if (res.code) {
                        if (res.code === 4006) {
                            Taro.showToast({
                                icon: 'none',
                                title: '当前访问人数过多\r\n请稍后重试'
                            });
                        } else if (res.code === 4008) {
                            // 客户端超过15秒未发送音频数据
                            stopRecorder();
                        } else {
                            recognizeErrorCount.current += 1;
                            if (recognizeErrorCount.current >= 3) {
                                recognizeErrorCount.current = 0;
                                Dialog_.alert({
                                    title: '语音识别出问题了',
                                    message:
                                        '别担心，由于手机系统原因，录音功能有小概率启动失败，请点击重启服务，或者使用系统的返回进行重试，您的练习记录会实时保存，并且支持中断后继续。',
                                    confirmButtonColor: '#4F66FF',
                                    confirmButtonText: '重启服务',
                                    onConfirm() {
                                        stopRecorder();
                                    }
                                });
                            } else {
                                handleError();
                            }
                        }
                    } else {
                        handleError();
                    }
                }
            };
            speechRecognizerManager.current.onSocketOpen = () => {
                addLog({
                    level: 'debug',
                    message: '语音识别socket连接成功',
                    data: {
                        currentPPTIndexId: currentPPTIndexIdRef.current,
                        currentPPTIndex: currentPPTIndexRef.current
                    }
                });
                recognizeStatus.current = true;
                if (pageStatus.current === 'unmount') {
                    speechRecognizerManager.current?.close();
                }
            };
            speechRecognizerManager.current.onSocketClose = () => {
                addLog({
                    level: 'debug',
                    message: '语音识别socket断开',
                    data: {
                        currentPPTIndexId: currentPPTIndexIdRef.current,
                        currentPPTIndex: currentPPTIndexRef.current,
                        uploadSummaryStatus: uploadSummaryStatus.current,
                        stopType: stopType.current
                    }
                });
                isCanSendData.current = false;
                recognizeStatus.current = false;
                addLog({
                    level: 'trace',
                    message: 'trigger RESTART_EVENT_NAME'
                });
                events.trigger(RESTART_EVENT_NAME);
            };
        }
        // 建立websocket链接
        speechRecognizerManager.current.start();
    };

    const startRecorderUploader = useEvent(() => {
        addLog({
            level: 'trace',
            message: 'startRecorderUploader',
            data: {
                currentPPTIndexId: currentPPTIndexIdRef.current,
                currentPPTIndex: currentPPTIndexRef.current
            }
        });
        recordUploader.current = new RecordUploader({
            chatId: listDetail?.id,
            pptId: currentPPTIndexIdRef.current,
            token: Storage.get(StorageEnvKey.TOKEN)
        });
        recordUploader.current.OnSocketOpen = () => {
            recorderUploaderStatus.current = true;
            addLog({
                level: 'trace',
                message: 'startRecorderUploader onSocketOpen'
            });
            if (pageStatus.current === 'unmount') {
                recordUploader.current?.close();
            }
        };
        recordUploader.current.OnSocketClose = () => {
            addLog({
                level: 'debug',
                message: 'startRecorderUploader onSocketClose',
                data: {
                    currentPPTIndexId: currentPPTIndexIdRef.current,
                    currentPPTIndex: currentPPTIndexRef.current,
                    uploadSummaryStatus: uploadSummaryStatus.current,
                    stopType: stopType.current
                }
            });
            recordUploader.current = undefined;
            recorderUploaderStatus.current = false;
            addLog({
                level: 'trace',
                message: 'trigger RESTART_EVENT_NAME'
            });
            events.trigger(RESTART_EVENT_NAME);
        };
        recordUploader.current.OnError = (error) => {
            recorderUploaderStatus.current = false;
            recordUploader.current = undefined;
            stopSocket();

            stopType.current = 'passive';
            addLog({
                level: 'error',
                message: 'startRecorderUploader onError',
                data: {
                    error
                }
            });
            if (pageStatus.current === 'mount') {
                /* Dialog_.alert({
                    zIndex: 1000,
                    title: '录音上传服务出问题了',
                    message: '请重启服务',
                    confirmButtonColor: '#4F66FF',
                    confirmButtonText: '重启服务',
                    onConfirm() {
                        handleError();
                    }
                }); */
                handleError();
            }
        };
        recordUploader.current.connect();
    });
    const { run: handleAudioBar } = useThrottleFn(
        useEvent((frameBuffer: ArrayBuffer) => {
            // 2. 计算音量分贝值
            const decibel = calculateDecibel(frameBuffer);
            // console.log(decibel, 'decibeldecibel');
            if (isadd == 1) {
                setdecibelPlay(decibel + 10);
                setisZore(decibel);
                isadd = 2;
                // setisadd(2);
            } else {
                setdecibelPlay(decibel + 40);
                setisZore(decibel);
                // setisadd(1);
                isadd = 1;
            }
        }),
        {
            wait: 100,
            leading: true,
            trailing: false
        }
    );
    // 录音的初始化配置，并开始录音
    const startRecorder = () => {
        addLog({
            level: 'trace',
            message: 'startRecorder'
        });
        if (!recorder.current) {
            recorder.current = Taro.getRecorderManager();
            recorder.current.onStart(() => {
                addLog({
                    level: 'trace',
                    message: '录音开始'
                });
                recordStatus.current = 'start';
            });
            recorder.current.onFrameRecorded((res: any) => {
                // 3. 实时更新UI（示例）
                // setdecibelPlay({ currentVolume: `${decibel.toFixed(1)}dB` });
                // console.log(res, '录音中', res, buf2hex(res.frameBuffer));
                if (isCanSendData.current) {
                    const { frameBuffer } = res;

                    handleAudioBar(frameBuffer);
                    speechRecognizerManager.current?.write(res.frameBuffer);
                    // recordFrames.current.push(res.frameBuffer);
                    if (recordUploader.current && recorderUploaderStatus.current) {
                        recordUploader.current?.sendAudioChunk(res.frameBuffer);
                    }
                }
            });
            recorder.current.onStop((res: any) => {
                addLog({
                    level: 'trace',
                    message: '录音停止',
                    data: {
                        res,
                        isTimeout: isTimeout.current,
                        chatDone: chatDone.current,
                        isFinish: isFinish.current,
                        pageStatus: pageStatus.current,
                        recordStatus: recordStatus.current,
                        finishType: finishType.current,
                        stopType: stopType.current
                    }
                });
                recordStatus.current = 'stop';

                // events.trigger(RESTART_EVENT_NAME);
                if (
                    !isTimeout.current &&
                    !chatDone.current &&
                    !isFinish.current &&
                    pageStatus.current === 'mount' &&
                    !finishType.current
                ) {
                    if (stopType.current === 'passive') {
                        restartAll();
                    }
                    startRecorder();
                }
            });
            recorder.current.onPause((res: any) => {
                addLog({
                    level: 'trace',
                    message: '录音暂停',
                    data: res
                });
                recordStatus.current = 'pause';
                stopRecognizer();
                closeRecorderUploader();
            });
            recorder.current.onError((res: any) => {
                addLog({
                    level: 'error',
                    message: '录音失败',
                    data: res
                });

                stopType.current = 'passive';
                closeRecorderUploader();
                stopRecognizer();
                Dialog_.alert({
                    zIndex: 1000,
                    title: '录音出问题了',
                    message: '别担心，请重新进入练习即可恢复',
                    confirmButtonText: '重新进入',
                    confirmButtonColor: '#4F66FF',
                    onConfirm() {
                        handleRecorderError();
                    }
                });
            });
            recorder.current.onInterruptionBegin((res: any) => {
                // 监听录音因为受到系统占用而被中断开始事件。以下场景会触发此事件：微信语音聊天、微信视频聊天。此事件触发后，录音会被暂停。pause 事件在此事件后触发
                addLog({
                    level: 'trace',
                    message: '录音中断开始'
                });
                stopRecognizer();
                closeRecorderUploader();
            });
            recorder.current.onInterruptionEnd((res: any) => {
                // 在收到 interruptionBegin 事件之后，小程序内所有录音会暂停，收到此事件之后才可再次录音成功。
                addLog({
                    level: 'trace',
                    message: '录音中断结束'
                });
                resumeRecorder();
            });
        }
        recorder.current.start(recorderParams);
    };
    function resumeRecorder() {
        if (!recorder.current) {
            startRecorder();
        } else {
            recorder.current.resume();
        }
        startRecorderUploader();
        startRecognizer();
    }
    function restartAll() {
        addLog({
            level: 'trace',
            message: 'restartAll'
        });
        stopType.current = '';
        startRecognizer();
        startRecorderUploader();
    }
    const start = useEvent(() => {
        events.on(RESTART_EVENT_NAME, async () => {
            addLog({
                level: 'debug',
                message: 'restart',
                data: {
                    isTimeout: isTimeout.current,
                    chatDone: chatDone.current,
                    isFinish: isFinish.current,
                    recordStatus: recordStatus.current,
                    recognizeStatus: recognizeStatus.current,
                    recorderUploaderStatus: recorderUploaderStatus.current,
                    uploadSummaryStatus: uploadSummaryStatus.current,
                    pageStatus: pageStatus.current,
                    finishType: finishType.current,
                    stopType: stopType.current
                }
            });

            if (
                !recognizeStatus.current &&
                !recorderUploaderStatus.current &&
                !uploadSummaryStatus.current &&
                stopType.current === 'active'
            ) {
                events.off(RESTART_EVENT_NAME);
                tempSentence.current = '';
                changeStatus(VoiceStatus.Stop);
                if (pageStatus.current === 'mount') {
                    try {
                        await handlePageDone();
                    } catch (error) {}
                }

                if (
                    !isTimeout.current &&
                    !chatDone.current &&
                    !isFinish.current &&
                    pageStatus.current === 'mount' &&
                    !finishType.current &&
                    currentPPTIndexRef.current !== listDetail?.pptList.length - 1
                ) {
                    // setCurrentPPTIndex((value) => {
                    //     return (value === undefined ? 0 : value) + 1;
                    // });
                    const nextIndex = (currentPPTIndexRef.current === undefined ? 0 : currentPPTIndexRef.current) + 1;
                    changePPTIndex(nextIndex);
                    changeStatus(VoiceStatus.Loading);
                    Taro.nextTick(() => {
                        setTimeout(() => start(), 500);
                    });
                    addLog({
                        level: 'debug',
                        message: 'next ppt',
                        data: {
                            nextIndex
                        }
                    });
                }
            }
        });
        addLog({
            level: 'trace',
            message: 'start',
            data: {
                currentPPTIndexId: currentPPTIndexIdRef.current,
                currentPPTIndex: currentPPTIndexRef.current,
                stopType: stopType.current
            }
        });
        stopType.current = '';
        changeStatus(VoiceStatus.Loading);
        // startRecorder();
        startRecognizer();
        startRecorderUploader();
    });

    function handleError() {
        addLog({
            level: 'trace',
            message: 'handleError'
        });
        restartAll();
    }

    const handleFinish = useCallback(async () => {
        isFinish.current = true;
        if (finishType.current === 'report') {
            try {
                if (chatDone.current) {
                    const res = await generateReport(listDetail?.id, true);
                    if (res.data.code === 200) {
                        Taro.redirectTo({
                            url: `/pages/chat/report/index?id=${listDetail?.chatReportId}&to=home&from=PPT&PPTid=${pptId}`
                        });
                    }
                } else {
                    const res = await doneGenerateReport(listDetail?.id, true);
                    chatDone.current = true;
                    if (res.data.code === 200) {
                        Taro.redirectTo({
                            url: `/pages/chat/report/index?id=${listDetail?.chatReportId}&to=home&from=PPT&PPTid=${pptId}`
                        });
                    }
                }
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'openReport doneChat',
                    data: {
                        error
                    }
                });
            } finally {
                Taro.hideLoading();
            }
        } else if (finishType.current === 'end') {
            try {
                setShowActionSheet(false);
                Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                if (texturl && texturl == 'challenge') {
                    Taro.switchTab({
                        url: '/pages/mini/index'
                    });
                } else {
                    if (homePath) {
                        Taro.switchTab({ url: homePath });
                    } else {
                        Taro.redirectTo({ url: HomePath.MIDDLE });
                    }
                }
            } catch (error) {
                addLog({
                    level: 'error',
                    message: 'chatEnd doneChat',
                    data: {
                        error
                    }
                });
                Taro.hideLoading();
                Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
                const homePath = Storage.get(StorageEnvKey.HOME_PATH);
                if (homePath) {
                    Taro.switchTab({ url: homePath });
                } else {
                    Taro.redirectTo({ url: HomePath.MIDDLE });
                }
            } finally {
                Taro.hideLoading();
            }
        }
    }, [listDetail]);

    const handleEnd = useEvent(async () => {
        Taro.showLoading({
            title: '加载中',
            mask: true
        });
        await doneChat(listDetail?.id, false);
        chatDone.current = true;
        if (recordStatus.current === 'start') {
            stopType.current = 'active';
            stopAll();
            if (!tempSentence.current) {
                handleFinish();
            } else {
                // 等待识别结果上传完成再结束
                finishTimer.current = setTimeout(() => {
                    events.off(FINISH_EVENT_NAME);
                    handleFinish();
                }, 2500);
            }
        } else {
            if (tempSentence.current) {
                finishTimer.current = setTimeout(() => {
                    events.off(FINISH_EVENT_NAME);
                    handleFinish();
                }, 2500);
            } else {
                handleFinish();
            }
        }
    });

    const openReport = async () => {
        addLog({
            level: 'debug',
            message: 'openReport',
            data: {
                recordStatus: recordStatus.current,
                tempSentence: tempSentence.current,
                pageDoneStatus: pageDoneStatus.current
            }
        });
        finishType.current = 'report';
        handleEnd();
    };

    const chatEnd = async () => {
        addLog({
            level: 'debug',
            message: 'chatEnd',
            data: {
                recordStatus: recordStatus.current,
                tempSentence: tempSentence.current,
                pageDoneStatus: pageDoneStatus.current
            }
        });
        finishType.current = 'end';
        handleEnd();
    };

    /**
     * 处理时间结束
     */
    const handleTimeOut = useCallback(async () => {
        finishType.current = 'timeout';
        addLog({
            level: 'trace',
            message: 'handleTimeOut'
        });
        setCanGenerate(false);
        stopAll();
        stopType.current = 'active';
        setChatActionSheetType(ChatActionSheetType.OFF);
        setShowActionSheet(true);
    }, [listDetail]);

    const actionSheet = useMemo(() => {
        if (chatActionSheetType === ChatActionSheetType.FINISH) {
            return {
                title: '确认结束练习吗？',
                actions: [
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.COMPLETE) {
            return {
                title: '完成练习',
                actions: [
                    {
                        text: '生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.OFF) {
            return {
                title: '时间结束，练习已关闭',
                actions: [
                    {
                        text: '生成报告',
                        buttonProps: {
                            color: 'linear-gradient(270deg, #6742FF 0%, #3D83FF 100%)',
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { color: '#F6F6F6', style: { color: '#777777' }, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
    }, [chatActionSheetType]);
    const actionSheetheng = useMemo(() => {
        if (chatActionSheetType === ChatActionSheetType.FINISH) {
            return {
                title: '确认结束练习吗？',
                actions: [
                    {
                        text: '结束并生成报告',
                        buttonProps: {
                            buttonPropsbutton: styles.horizontal_buttton,
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { buttonPropsbutton: styles.horizontal_butttons, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.COMPLETE) {
            return {
                title: '完成练习',
                actions: [
                    {
                        text: '生成报告',

                        buttonProps: {
                            buttonPropsbutton: styles.horizontal_buttton,
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { buttonPropsbutton: styles.horizontal_butttons, onClick: chatEnd }
                    }
                ],
                closeAction: false // 是否隐藏关闭按钮
            };
        }
        if (chatActionSheetType === ChatActionSheetType.OFF) {
            return {
                title: '时间结束，练习已关闭',
                actions: [
                    {
                        text: '生成报告',
                        buttonProps: {
                            buttonPropsbutton: styles.horizontal_buttton,
                            onClick: openReport
                            // disabled: true
                        },
                        noChatHistoryHide: true
                    },
                    {
                        text: '直接结束',
                        buttonProps: { buttonPropsbutton: styles.horizontal_butttons, onClick: chatEnd }
                    }
                ],
                closeAction: true
            };
        }
    }, [chatActionSheetType, btnText]);
    function handleAiHelpClose() {
        setAiHelpShow(false);
    }
    const renderAction = useMemo(() => {
        return (actionSheet?.actions ?? [])
            .filter((action) => {
                if (action.noChatHistoryHide) {
                    return summary !== '' && summary !== '\n\n';
                } else {
                    return true;
                }
            })
            .map((action) => {
                return (
                    // eslint-disable-next-line taro/no-spread-in-props

                    <Button block round {...action.buttonProps} key={action.text}>
                        {action.text}
                    </Button>
                );
            });
    }, [actionSheet, summary]);
    const renderActionheng = useMemo(() => {
        return (actionSheetheng?.actions ?? [])
            .filter((action) => {
                if (action.noChatHistoryHide) {
                    return summary !== '' && summary !== '\n\n';
                } else {
                    return true;
                }
            })
            .map((action) => {
                return (
                    // eslint-disable-next-line taro/no-spread-in-props
                    <View
                        className={action.buttonProps.buttonPropsbutton}
                        key={action.text}
                        onClick={action.buttonProps.onClick}
                    >
                        {action.text}
                    </View>
                );
            });
    }, [actionSheetheng, summary]);
    const previewImage = useCallback(
        (index: number) => {
            setShowImagePreview(true);
            setCurrentPreviewImage(listDetail ? listDetail.pptList[index].imageUrl : '');
        },
        [listDetail]
    );

    useEffect(() => {
        if (!listDetail || currentPPTIndex == undefined) return;
        if (status === VoiceStatus.Start) {
            setBtnText('开始录音');
            setBtnIcon(IconSpeak);
        } else if (status === VoiceStatus.Loading) {
            setBtnText('加载中');
            setBtnIcon(IconLoading);
        } else if (VoiceStatus.Listening) {
            if (currentPPTIndex === listDetail.pptList.length - 1) {
                // 最后一张
                setBtnText('完成');
                setBtnIcon(IconComplete);
            } else {
                setBtnText('下一页');
                if (orientation == 'landscape') {
                    setBtnIcon('1');
                } else {
                    setBtnIcon(IconNextStep);
                }
            }
        } else {
            setBtnText('');
            setBtnIcon('');
        }
    }, [status, currentPPTIndex, listDetail, orientation]);

    // const uploadAudio = (frameBuffer: ArrayBuffer) => {
    //     Taro.request({
    //         url: 'http://172.16.8.188:19000/stream',
    //         method: 'POST',
    //         header: {
    //             'Content-Type': 'application/octet-stream',
    //         },
    //         data: frameBuffer,
    //         success: (res: any) => {
    //             console.log('上传成功', res);
    //         },
    //         fail: (err: any) => {
    //             console.log('上传失败', err);
    //         }
    //     })
    // }

    const toggleDemand = () => {
        setRequirementVisible((val) => {
            return !val;
        });
    };

    const caculateLeftTime = (serverTime: string) => {
        return dayjs(createTime.current).add(timeLimit.current, 'm').valueOf() - dayjs(serverTime).valueOf();
    };

    const { cancel } = useRequest(() => getServerTime(), {
        pollingInterval: 30000, // 每隔30s更新一次当前时间，避免篡改手机本地时间导致倒计时错误
        onSuccess: async (data) => {
            const serverTime = data;
            if (createTime.current) {
                const leftTime = caculateLeftTime(serverTime);
                addLog({
                    level: 'debug',
                    message: 'getServerTime-leftTime',
                    data: {
                        createTime: createTime.current,
                        serverTime,
                        leftTime
                    }
                });
                setLeftTime(leftTime);
                if (leftTime <= 0) {
                    cancel();
                    if (!isTimeout.current) {
                        isTimeout.current = true;
                        handleTimeOut();
                    }
                }
            }
        }
    });
    const [, formattedRes] = useCountDown({
        // 此处默认且只能使用当前系统时间，如何拿服务器时间去做纠正 => 使用leftTime代替targetDate
        leftTime,
        // targetDate,
        onEnd: async () => {
            addLog({
                level: 'trace',
                message: 'countdown end'
            });
            if (!isTimeout.current) {
                cancel();
                isTimeout.current = true;
                handleTimeOut();
            }
        }
    });

    useEffect(() => {
        const timeText = dayjs
            .duration({
                minutes: formattedRes.minutes,
                seconds: formattedRes.seconds
            })
            .format('mm:ss');
        // console.log(timeText, 'jksnsjknjnktimeTexttimeText');
        setLeftTimeText(timeText);
    }, [formattedRes]);

    const { run: handleTapSay } = useThrottleFn(
        useCallback(async () => {
            addLog({
                level: 'debug',
                message: 'handleTapSay',
                data: {
                    status: statusRef.current,
                    currentPPTIndex,
                    currentPPTIndexRef: currentPPTIndexRef.current,
                    pptList: pptData.current.length,
                    stopType: stopType.current
                }
            });
            const isHarmony = await checkHarmony();
            console.log('isHarmony', isHarmony);
            if (isHarmony) {
                setShowHarmonyDialog(true);
            } else {
                if (statusRef.current === VoiceStatus.Start) {
                    setCurrentIndex(currentPPTIndex);
                    startRecorder();
                    start();
                }
                if (statusRef.current === VoiceStatus.Listening) {
                    if (currentPPTIndex === pptData.current.length - 1) {
                        // stop();
                        setChatActionSheetType(ChatActionSheetType.COMPLETE);
                        setShowActionSheet(true);
                    } else {
                        stopType.current = 'active';
                        stopSocket();
                    }
                }
            }
        }, [currentPPTIndex]),
        {
            wait: 1500,
            leading: true,
            trailing: false
        }
    );

    const { run: handleHangup } = useThrottleFn(
        () => {
            addLog({
                level: 'debug',
                message: '点击挂断',
                data: {
                    status: statusRef.current
                }
            });
            setChatActionSheetType(ChatActionSheetType.FINISH);
            setShowActionSheet(true);
        },
        {
            wait: 1000,
            leading: true,
            trailing: false
        }
    );

    const handleTouchStart = (e) => {
        e.stopPropagation();
        touchStartX.current = e.touches[0].clientX;
    };

    const handleTouchEnd = useCallback(
        (e) => {
            e.stopPropagation();
            const touchEndX = e.changedTouches[0].clientX;
            const diff = touchEndX - touchStartX.current;
            console.log('handleTouchEnd', diff);
            if (currentIndex !== 0 && diff > 50) {
                // 左滑
                // ToastVoiceDialog_.show({
                //     message: <View style={{ textAlign: 'center' }}>练习过程中，\n无法向前翻页</View>
                // });
                Taro.showToast({
                    title: '练习过程中\r\n不支持翻到上一页',
                    icon: 'none'
                });
            }

            // todo: 右滑
            if (currentIndex !== pptData.current.length - 1 && diff < -50) {
                // 右滑
                handleTapSay();
            }
            touchStartX.current = 0;
        },
        [currentIndex]
    );

    function goBack() {
        setChatActionSheetType(ChatActionSheetType.FINISH);
        setShowActionSheet(true);
    }

    const init = async (data: ChatVO) => {
        // 拿到ppt限制时间
        timeLimit.current = data.script.timeLimit;
        // 拿到创建时间
        createTime.current = data.createTime;

        const { data: serverTimeData } = await getServerTime();
        // 拿到倒计时时间
        const leftTime = caculateLeftTime(serverTimeData.data);
        // 判断对话是否完成
        if (data.done) {
            addLog({
                level: 'trace',
                message: '对话已完成'
            });

            changePPTIndex(0);
            setCurrentIndex(0);
            chatDone.current = true;
            isTimeout.current = true;
            cancel();
            setLeftTime(undefined);
            setChatActionSheetType(ChatActionSheetType.OFF);
            setShowActionSheet(true);
        } else if (leftTime <= 0) {
            // 倒计时结束，未结束对话
            addLog({
                level: 'trace',
                message: '倒计时结束了'
            });

            changePPTIndex(0);
            setCurrentIndex(0);
            isTimeout.current = true;
            setLeftTimeText('00:00');
            try {
                // 调用对话结束接口
                await doneChat(data.id, false);
                chatDone.current = true;
                setChatActionSheetType(ChatActionSheetType.OFF); // 对话结束报告弹窗按钮
                setShowActionSheet(true); // 对话结束报告弹窗
            } catch (error) {
                console.log('doneChat error', error);
            }
        } else {
            // 上传文字后，再结束对话
            // 找到第一个未完成的PPT索引

            events.on(FINISH_EVENT_NAME, async () => {
                if (finishTimer.current) {
                    clearTimeout(finishTimer.current);
                }
                addLog({
                    level: 'trace',
                    message: 'openReport event finish'
                });
                if (!tempSentence.current && pageDoneStatus.current) {
                    handleFinish();
                }
            });

            setLeftTime(leftTime);
            addLog({
                level: 'trace',
                message: '还有剩余时间'
            });

            pptData.current = data.pptList;
            changeStatus(VoiceStatus.Start);

            const firstUndonePPTIndex = data.pptList.map((ppt) => ppt.done).lastIndexOf(true);
            console.log('firstUndonePPTIndex', firstUndonePPTIndex, data.pptList.length);
            let firstIndex = 0;
            if (firstUndonePPTIndex === data.pptList.length - 1) {
                // 全部ppt练过了
                cancel();
                setChatActionSheetType(ChatActionSheetType.COMPLETE);
                setShowActionSheet(true);
                firstIndex = firstUndonePPTIndex;
            } else {
                firstIndex = firstUndonePPTIndex === -1 ? 0 : firstUndonePPTIndex + 1;
            }

            changePPTIndex(firstIndex);
            setCurrentIndex(firstIndex);
        }
    };

    useEffect(() => {
        setAiHelpEnable(listDetail?.script?.aiHelpFlag || false);
        setRequireVisible(listDetail?.script?.requirementShowFlag);
        try {
            const { appId } = Taro.getAccountInfoSync().miniProgram;
            console.log('appId', appId, appId === AppIdConsts.qnq);
            setIsQnq(appId === AppIdConsts.qnq);
        } catch (error) {}

        if (listDetail) {
            init(listDetail);
        }
    }, [listDetail]);

    useEffect(() => {
        setShowImagePreview(false);
    }, [orientation]);

    useDidShow(() => {
        // 退到后台录音会暂停
        console.log('didShow', recordStatus.current);
        if (recordStatus.current === 'pause') {
            resumeRecorder();
        }
    });

    useUnmount(() => {
        pageStatus.current = 'unmount';
        addLog({
            level: 'debug',
            message: 'ppt unmount',
            data: {
                status: recordStatus.current,
                recognizeStatus: recognizeStatus.current,
                recorderUploaderStatus: recorderUploaderStatus.current,
                recordStatus: recordStatus.current
            }
        });

        if (finishTimer.current) {
            clearTimeout(finishTimer.current);
        }

        // if (recordStatus.current === 'start') {
        stopRecorder();
        // }
        if (recognizeStatus.current) {
            stopRecognizer();
        }

        if (recorderUploaderStatus.current) {
            closeRecorderUploader();
        }
        events.off(FINISH_EVENT_NAME);
        events.off(RESTART_EVENT_NAME);

        recordUploader.current = null;
        speechRecognizerManager.current = null;
        recorder.current = null;
    });
    const onSwiperChangehorizontal = (e) => {
        setCurrentIndex(e.detail.current);
        handleAiHelpClose();
    };
    const onSwiperChange = (e) => {
        setCurrentIndex(e.detail.current);
        handleAiHelpClose();
    };

    /**
     * 根据 幻灯片评分方式 展示 演练要求/关键词
     * 1： 演练要求
     * 2、3： 关键词
     * 默认为 演练要求
     */
    const pptTip = useMemo(() => {
        let tip = '演练要求';
        if ([2, 3].includes(listDetail?.script?.pptScoringType)) {
            tip = '关键词';
        }
        return tip;
    }, [listDetail]);
    return (
        <Block>
            {orientation && orientation == 'landscape' ? (
                <View className={styles.isheng}>
                    <View className={styles.containers}>
                        <Image src={bg} mode='aspectFill' className={styles.bg} />
                        {isWework == '1' ? <View className={styles.blurs} /> : <View className={styles.blur} />}
                        {/* <NavBar
                            className={styles.navbar}
                            title={
                                <View className={styles.time}>
                                    <View> {`第${currentPPTIndex + 1}/${listDetail?.pptList?.length || 0}页`}</View>
                                    <View>{leftTimeText} </View>{' '}
                                </View>
                            }
                            renderLeft={<Icon name='cross' size={pxTransform(32)} color='#ffffff' />}
                            safeAreaInsetTop
                            onClickLeft={() => {
                                goBack();
                            }}
                        /> */}
                        <View className={styles.newNavBar}>
                            <View className={styles.crossnewNavBar}>
                                <Icon
                                    name='cross'
                                    size={pxTransform(22)}
                                    color='#ffffff'
                                    onClick={() => {
                                        goBack();
                                    }}
                                />
                            </View>
                            <View className={styles.timetit}>
                                {`第${(currentIndex === undefined ? 0 : currentIndex) + 1}/${
                                    listDetail?.pptList?.length || 0
                                }页`}{' '}
                                <Text className={styles.time}>{leftTimeText} </Text>
                            </View>
                            <View className={styles.times} />
                        </View>
                        <View className={styles.horizontal_style}>
                            <View className={styles.horizontal_image}>
                                {listDetail && currentPPTIndex !== undefined && (
                                    <Aihelpheng
                                        show={aiHelpShow}
                                        // keyHeight={keyHeight}
                                        // chatId={listDetail?.id}
                                        currentPPTIndexId={currentPPTIndexId}
                                        chatId={listDetail?.id}
                                        styleType={'voice'}
                                        onClose={handleAiHelpClose}
                                        canGenerate={canGenerate}
                                    />
                                )}
                                {
                                    listDetail && (
                                        <Block>
                                            <Swiper
                                                className={styles.swiper}
                                                onChange={onSwiperChangehorizontal}
                                                autoplay={false}
                                                current={currentIndex}
                                                ref={swiperRef}
                                                // height={pxTransform(331)}
                                            >
                                                {listDetail.pptList.map((item: PPTItem, index: number) => {
                                                    return (
                                                        <SwiperItem key={item.id}>
                                                            <Image
                                                                onClick={() => previewImage(index)}
                                                                src={item.imageUrl}
                                                                className={styles.ppt_image}
                                                                mode='aspectFit'
                                                            />
                                                        </SwiperItem>
                                                    );
                                                })}
                                            </Swiper>
                                            {status !== undefined && status !== VoiceStatus.Start && (
                                                <View
                                                    className={styles.swiper_cover}
                                                    onClick={() => previewImage(currentPPTIndex)}
                                                    onTouchStart={handleTouchStart}
                                                    onTouchEnd={handleTouchEnd}
                                                />
                                            )}
                                            {(status === undefined || status === VoiceStatus.Start) && (
                                                <View className={styles.start_tip} onClick={handleTapSay}>
                                                    请先点击开始录音
                                                </View>
                                            )}
                                        </Block>
                                    )

                                    /*  <View className={styles.swiper}>
                                            <Image
                                                onClick={() => previewImage(currentPPTIndex)}
                                                src={listDetail.pptList[currentPPTIndex].imageUrl}
                                                className={styles.ppt_image}
                                                mode='aspectFit'
                                                onTouchStart={handleTouchStart}
                                                onTouchEnd={handleTouchEnd}
                                            />
                                        </View> */
                                }
                            </View>

                            <View className={isIOS ? styles.horizontal_contextss : styles.horizontal_contextssanzhuo}>
                                {requireVisible && requirementVisible ? (
                                    <View className={styles.horizontal_require}>{pptTip}</View>
                                ) : (
                                    <View className={styles.horizontal_require} style={{ opacity: 0 }}>
                                        {pptTip}
                                    </View>
                                )}

                                <View
                                    className={
                                        isIOS
                                            ? classNames(
                                                  styles.horizontal_context,
                                                  requirementVisible && requireVisible
                                                      ? styles.horizontal_context_visible
                                                      : styles.horizontal_context_hide
                                              )
                                            : classNames(
                                                  styles.horizontal_contexaa,
                                                  requirementVisible && requireVisible
                                                      ? styles.horizontal_context_visible
                                                      : styles.horizontal_context_hide
                                              )
                                    }
                                >
                                    <ScrollView
                                        enhanced
                                        id='verticalScroll'
                                        className={styles.horizontal_requiretext}
                                        ref={scrollRef}
                                        showScrollbar={false}
                                        scrollY
                                    >
                                        <Text>{currentRequirement}</Text>
                                    </ScrollView>
                                </View>

                                {/* ------------- */}
                                <View className={isIOS ? styles.action_button : styles.action_buttonanzhuo}>
                                    {btnText == '下一页' || btnText === '加载中' ? (
                                        <View style={{ position: 'relative' }}>
                                            {/* <VoiceLoading type='barAnimateheng' /> */}
                                            <VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />
                                        </View>
                                    ) : (
                                        <View className={classnames(styles.action_sayheng)} onClick={handleTapSay}>
                                            <Image
                                                className={styles.action_sayheng_icon_speak}
                                                mode='aspectFit'
                                                src={btnIcon}
                                            />
                                            {btnText}
                                        </View>
                                    )}
                                </View>

                                <View className={isIOS ? styles.action_say_boxheng : styles.action_say_boxhenganzhuo}>
                                    {!aiHelpEnable ? (
                                        <>
                                            {' '}
                                            {requireVisible && (
                                                <View className={styles.action_chathengai} onClick={toggleDemand}>
                                                    <Image
                                                        src={
                                                            requirementVisible
                                                                ? IconRequirementHorizonOn
                                                                : IconRequirementHorizonOff
                                                        }
                                                        mode='aspectFit'
                                                        className={styles.action_chathengai_icon}
                                                    />
                                                </View>
                                            )}
                                            <View className={styles.action_chatheng_miniai} onClick={handleHangup}>
                                                <Image
                                                    src={IconHangupMini}
                                                    className={styles.action_finish_icon_mini}
                                                    mode='aspectFit'
                                                />
                                            </View>
                                        </>
                                    ) : (
                                        <>
                                            {requireVisible && (
                                                <View className={styles.action_chatheng} onClick={toggleDemand}>
                                                    <Image
                                                        src={
                                                            requirementVisible
                                                                ? IconRequirementHorizonOn
                                                                : IconRequirementHorizonOff
                                                        }
                                                        mode='aspectFit'
                                                        className={styles.action_chatheng_icon}
                                                    />
                                                </View>
                                            )}
                                            {aiHelpEnable && (
                                                <View className={styles.action_chatheng} onClick={handleAIHelp}>
                                                    <Image
                                                        src={status === VoiceStatus.Speaking ? IconHelpOff : IconHelp}
                                                        className={styles.action_chatheng_icon}
                                                    />
                                                </View>
                                            )}
                                            <View className={styles.action_chatheng_mini} onClick={handleHangup}>
                                                <Image
                                                    src={IconHangupMini}
                                                    className={styles.action_finish_icon_mini}
                                                    mode='aspectFit'
                                                />
                                            </View>{' '}
                                        </>
                                    )}
                                </View>
                            </View>
                        </View>
                    </View>
                    <ImagePreview
                        show={showImagePreview}
                        current={currentPreviewImage}
                        urls={previewImages}
                        onClose={() => setShowImagePreview(false)}
                    />
                    <Popup
                        show={showActionSheet}
                        className={styles.popup_context}
                        position='right'
                        onClose={() => {
                            if (chatActionSheetType != ChatActionSheetType.OFF) {
                                setShowActionSheet(false);
                            }
                        }}
                    >
                        {chatActionSheetType === ChatActionSheetType.OFF ? (
                            <View className={styles.popup_cross}>
                                <Icon name='cross' size={pxTransform(20)} color='#ffffff' />
                            </View>
                        ) : (
                            <View className={styles.popup_cross} onClick={() => setShowActionSheet(false)}>
                                <Icon name='cross' size={pxTransform(20)} />
                            </View>
                        )}

                        <Imagea
                            className={styles.horizontal_complete}
                            round
                            fit='cover'
                            width={pxTransform(64)}
                            src={`${config.cdnPrefix}slide/reportOk.png`}
                            height={pxTransform(64)}
                        />
                        <View className={styles.horizontal_completetext}>{actionSheetheng?.title}</View>
                        {renderActionheng}
                    </Popup>
                </View>
            ) : (
                <View className={styles.isshu}>
                    <View className={styles.container}>
                        <Image src={bg} mode='aspectFill' className={styles.bg} />
                        {isWework == '1' ? <View className={styles.blurs} /> : <View className={styles.blur} />}

                        <NavBar
                            className={styles.navbar}
                            title={`第${(currentIndex === undefined ? 0 : currentIndex) + 1}/${
                                listDetail?.pptList?.length || 0
                            }页`}
                            renderLeft={<Icon name='cross' size={pxTransform(40)} color='#ffffff' />}
                            safeAreaInsetTop
                            onClickLeft={() => {
                                goBack();
                            }}
                        />

                        <View className={styles.content}>
                            {listDetail && (
                                <Block>
                                    <Swiper
                                        className={styles.swiper}
                                        onChange={onSwiperChange}
                                        autoplay={false}
                                        current={currentIndex}
                                    >
                                        {listDetail.pptList.map((item: PPTItem, index: number) => {
                                            return (
                                                <SwiperItem key={item.id} itemId={item.id}>
                                                    <Image
                                                        onClick={() => previewImage(index)}
                                                        src={item.imageUrl}
                                                        className={styles.ppt_image}
                                                        mode='aspectFit'
                                                    />
                                                </SwiperItem>
                                            );
                                        })}
                                    </Swiper>
                                    {status !== undefined && status !== VoiceStatus.Start && (
                                        <View
                                            className={styles.swiper_cover}
                                            onClick={() => previewImage(currentPPTIndex)}
                                            onTouchStart={handleTouchStart}
                                            onTouchEnd={handleTouchEnd}
                                        >
                                            {/* <Image
                                                    onClick={() => previewImage(currentPPTIndex)}
                                                    src={listDetail.pptList[currentPPTIndex].imageUrl}
                                                    className={styles.ppt_image}
                                                    mode='aspectFit'
                                                    onTouchStart={handleTouchStart}
                                                    onTouchEnd={handleTouchEnd}
                                                /> */}
                                        </View>
                                    )}
                                    {(status === undefined || status === VoiceStatus.Start) && (
                                        <View className={styles.start_tip} onClick={handleTapSay}>
                                            请先点击开始录音
                                        </View>
                                    )}
                                </Block>
                            )}
                        </View>
                        <View
                            className={classNames(
                                styles.require_context,
                                requireVisible && requirementVisible
                                    ? styles.require_context_visible
                                    : styles.require_context_hide
                            )}
                        >
                            <View className={styles.title_require}>{pptTip}</View>
                            <ScrollView
                                enhanced
                                id='verticalScroll'
                                className={styles.title_requiretext}
                                ref={scrollRef}
                                showScrollbar={false}
                                scrollY
                            >
                                <Text>
                                    {(listDetail &&
                                        currentIndex !== undefined &&
                                        getShowContent(
                                            listDetail.pptList[currentIndex],
                                            listDetail.script?.pptScoringType
                                        )) ||
                                        '无'}
                                </Text>
                            </ScrollView>
                        </View>

                        {status === VoiceStatus.Listening ? (
                            <View className={styles.status}>
                                {/* <VoiceLoading type='barAnimate' aaa={llll} /> */}
                                <VoiceWave decibel={isZore == 0 ? 15 + decibelPlay : decibelPlay - 50} />
                            </View>
                        ) : null}

                        <View className={styles.footer} id='actionfooter'>
                            <View className={styles.time}>{leftTimeText}</View>
                            <View className={styles.actions}>
                                <View
                                    className={styles.action_chat}
                                    onClick={() => {
                                        handleAiHelpClose();
                                        showHistoryPractice();
                                    }}
                                >
                                    <Image src={IconChat} className={styles.action_chat_icon} mode='aspectFit' />
                                </View>
                                {requireVisible && (
                                    <View className={styles.action_chat} onClick={toggleDemand}>
                                        {requireVisible}
                                        <Image
                                            src={
                                                requirementVisible
                                                    ? IconRequirementHorizonOn
                                                    : IconRequirementHorizonOff
                                            }
                                            mode='aspectFit'
                                            className={styles.action_chat_icon}
                                        />
                                    </View>
                                )}

                                {aiHelpEnable && (
                                    <View className={styles.action_chat} onClick={handleAIHelp}>
                                        <Image
                                            src={status === VoiceStatus.Speaking ? IconHelpOff : IconHelp}
                                            className={styles.action_chat_icon}
                                        />
                                    </View>
                                )}

                                <View
                                    className={styles.action_chat}
                                    onClick={() => {
                                        handleAiHelpClose();
                                        showMore();
                                    }}
                                >
                                    <Image src={IconMore} mode='aspectFit' className={styles.action_chat_icon} />
                                </View>
                            </View>

                            <View className={styles.action_say_box}>
                                <Image
                                    src={IconHangupMini}
                                    className={styles.action_finish_icon_mini}
                                    mode='aspectFit'
                                    onClick={handleHangup}
                                />
                                <View
                                    className={classnames(styles.action_say, styles.action_say_active)}
                                    onClick={handleTapSay}
                                >
                                    <Image className={styles.action_say_icon_speak} mode='aspectFit' src={btnIcon} />{' '}
                                    {btnText}
                                </View>
                            </View>
                        </View>
                    </View>
                    <ImagePreview
                        show={showImagePreview}
                        current={currentPreviewImage}
                        urls={previewImages}
                        onClose={() => setShowImagePreview(false)}
                    />
                    {listDetail && currentPPTIndex !== undefined && (
                        <Aihelp
                            show={aiHelpShow}
                            currentPPTIndexId={currentPPTIndexId}
                            // keyHeight={keyHeight}
                            chatId={listDetail?.id}
                            styleType={'voice'}
                            onClose={handleAiHelpClose}
                            canGenerate={canGenerate}
                        />
                    )}
                    <ActionSheet
                        show={showActionSheet}
                        zIndex={110}
                        onClose={() => setShowActionSheet(false)}
                        title={actionSheet?.title}
                        className={classnames(styles.actionSheet, {
                            [styles.actionSheet_hide_close]: actionSheet?.closeAction
                        })}
                        closeOnClickOverlay={false}
                        // @ts-ignore
                        style={{ '--action-sheet-header-height': pxTransform(124) }}
                    >
                        <Button.Group className={styles.actionSheet_group} direction='vertical'>
                            {renderAction}
                        </Button.Group>
                    </ActionSheet>
                </View>
            )}
            <HarmonyDialog show={showHarmonyDialog} setShow={setShowHarmonyDialog} type='B' />
            <Dialog_ />
        </Block>
    );
};

export default App;
