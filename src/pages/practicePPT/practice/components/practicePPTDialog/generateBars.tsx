import { View } from '@tarojs/components';
import { useThrottleFn } from 'ahooks';
import { useEffect, useRef, useState } from 'react';
import styles from './index.less';

// 可视化参数配置
const VISUAL_CONFIG = {
    barCount: 16, // 柱子数量（建议12-16个）
    maxHeight: 80, // 最大高度(rpx)
    minHeight: 10, // 最小高度(rpx)
    animationSpeed: 120, // 动画速度(ms)
    smoothingFactor: 0.85, // 平滑系数(0-1)
    silenceThreshold: -55,
    waveIntensity: 0.6
};

const AudioWaveVisualizer = (props: any) => {
    const [heights, setHeights] = useState<number[]>(Array(VISUAL_CONFIG.barCount).fill(VISUAL_CONFIG.minHeight));
    // 分贝处理核心逻辑
    const processDbValue = (db: number) => {
        setHeights((prevHeights) => {
            prevHeights.shift();

            prevHeights.push(db);

            return prevHeights;
        });
    };

    const { run: handleFrameRecorded } = useThrottleFn(
        (db: number) => {
            processDbValue(db);
        },
        {
            wait: 70,
            leading: true,
            trailing: false
        }
    );
    useEffect(() => {
        handleFrameRecorded(props.decibel);
    }, [props.decibel]);
    return (
        <View className={styles.wave_container}>
            {heights.map((height, index) => {
                return (
                    <View
                        key={index}
                        className={styles.wave_bar}
                        style={{
                            transform: `scaleY(${height / 80})`,
                            animationDelay: `${index * 0.1}s`
                        }}
                    />
                );
            })}
        </View>
    );
};

export default AudioWaveVisualizer;
