@import '@/styles/index.less';

:global {
    .van-popup__close-icon  {
        color: #272C47;
        top: 62px;
        right: 42px;
        width: 26px;
        height: 26px;
    }
}

.start_tip {
    display: flex;
    justify-content: center;
    align-items: center;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 1;
    transform: translate(-50%, -50%);
    color: #fff;
    background-color: rgba(0, 0, 0, 0.5);
    font-weight: bold;
}
.isshu{
    .container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100vh;
        transition: all 0.3s;
    
        --nav-bar-title-text-color: #fff;
    }
    .bg {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
    }
    .blur {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.4);
        // backdrop-filter: blur(80px);
    }
    
    .blurs {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        // opacity: 1;
        background-color: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(80px);
    }
    .navbar {
        position: relative;
        z-index: 1;
        background-color: transparent;
        &::after {
            display: none;
        }
    }
    .content {
        position: relative;
        z-index: 1;
        height: 442px;
    }
     
    .status {
        height: 100px;
        margin-top: 18px;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1;
    }
    .footer {
        position: relative;
        z-index: 1;
        box-sizing: border-box;
    }
    
    .time {
        margin-bottom: 24px;
        padding-top: 30px;
        color: #fff;
        font-weight: 400;
      
        font-size: 28px;
        text-align: center;
    }
    
    .actions {
        display: flex;
        gap: 40px;
        align-items: center;
        justify-content: center;
        margin-bottom: 25px;
    }
    .action_close {
        width: 112px;
        height: 112px;
        margin: 0 auto;
        &_icon {
            width: 112px;
            height: 112px;
        }
    }
    .action_chat {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 108px;
        height: 108px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 108px;
        &_icon {
            width: 48px;
            height: 48px;
        }
    }
    .action_finish_icon_mini {
        width: 130px;
        height: 90px;
    }
    .action_say_box {
        position: relative;
        z-index: 1;
        display: flex;
        gap: 20px;
        padding: 22px 48px 60px;
    }
    .action_say {
        position: relative;
        z-index: 1;
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        height: 90px;
        color: rgba(243, 243, 243, 0.5);
        font-size: 28px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 96px;
    
    
        &_disabled {
            color: rgba(243, 243, 243, 0.5);
            background-color: rgba(0, 0, 0, 0.2);
        }
        &_active {
            color: #fff;
            background: linear-gradient(270deg, #6742ff 0%, #3d83ff 100%);
        }
        &_pressed {
            color: #fff;
            background: linear-gradient(270deg, #5237cc 0%, #3269cc 100%);
    
            &::after {
                position: absolute;
                top: -10px;
                left: -3%;
                z-index: 0;
                width: 106%;
                height: 110px;
                background: linear-gradient(270deg, rgba(103, 66, 255, 0.3) 0%, rgba(61, 131, 255, 0.3) 100%);
                border-radius: 130px;
                content: '';
            }
        }
        &_cancel {
            color: #fff;
            background: #ff4545;
            &::after {
                display: none;
            }
      
        }
        &_complete {
            margin-left: 53px;
            margin-right: 53px;
        }
    
        &_icon_speak {
            width: 48px;
            height: 48px;
            margin-right: 8px;
        }
        &_icon_loading {
            animation: rotate 2s linear infinite;
        }
        @keyframes rotate {
            from {
              transform: rotate(0deg);
            }
            to {
              transform: rotate(360deg);
            }
          }
    }
    .swiper {
        height: 442px;
        margin-top: 36px;
    }
    .swiper_cover{
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
    }
    .start_tip{
        width: 420px;
        height: 96px;
        border-radius: 48px;
        font-size: 40px;
        top: 58%;
    }
    .ppt_image {
        width: 100%;
        height: 442px;
    }

    .cancel_icon {
        width: 56px;
        height: 56px;
    }
    
    .error_img {
        width: 120px;
        height: 120px;
    }
    .error_tip {
        font-size: 24px;
        text-align: center;
    }
    
    .require_context{
        position: relative;
        z-index: 1;
        color: #fff;
        flex: 1;
        height: 0;
        display: flex;
        flex-direction: column;
        &_visible {
            visibility: visible;
        }
        &_hide{
            visibility: hidden;
        }
  
      }
    
      .title_require{
        margin-left: 48px;
        color:  rgba(255, 255, 255, 0.5);
        margin-top: 48px;
          font-size: 24px;
      }
      .title_requiretext{
        color:  #ffffff;
        margin-left: 48px;
        flex: 1;
          height:  300px !important;
          white-space: pre-wrap;
        margin-right: 48px;
          padding-bottom: 56px;
          box-sizing: border-box;
          overflow-y: auto;
          font-size: 28px;
          padding-top: 14px;
          width: 640px;
          line-height: 1.5;
          position: relative;
          mask-image: -webkit-gradient(
                  linear,
                  left 70%,
                  left bottom,
                  from(rgba(0,0,0,1)),
                  to(rgba(0,0,0,0))
          ) !important;
      &::-webkit-scrollbar {
          display:none !important;
          width:0  !important;
          height:0  !important;
          color:transparent  !important;
      }
    }
    // .horizontal_requiretext{
    //     color:  #ffffff;
    //     flex: 1;
    //     padding-bottom: 15px;
    //       height: 300px;
    //       white-space: pre-wrap;
    //     overflow-y: auto;
    //       font-size: 14px;
    //     //   padding-top: 14px;
    //       box-sizing: border-box;
    //       line-height: 1.5;
    //       position: relative;
    //       mask-image: -webkit-gradient(
    //               linear,
    //               left 70%,
    //               left bottom,
    //               from(rgba(0,0,0,1)),
    //               to(rgba(0,0,0,0))
    //       ) !important;
    //   &::-webkit-scrollbar {
    //       display:none;
    //       width:0;
    //       height:0;
    //       color:transparent;
    //   }
    // }
    
    
    .actionSheet {
        &_hide_close {
            :global {
                .van-action-sheet__close {
                    display: none !important;
                }
            }
        }
        &_group {
            .wrap-box(12px 62px 54px 62px);
        }
    }
     
.wave_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 200rpx;
    .wave_bar {
      width: 8rpx;
      height: 70rpx;
      margin: 0 6px;
      background: linear-gradient(
        180deg,
        rgb(242, 243, 244)  0%,
        rgb(252, 249, 249) 50%,
         rgb(255, 255, 255)100%
      );
      border-radius: 25% !important;
      animation: shortBar 1s infinite;
      transform-origin: center center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      // 3D效果增强
      box-shadow: 
        0 4rpx 12rpx rgba(0,120,200,0.3),
        inset 0 -2rpx 4rpx rgba(255,255,255,0.2);
      
      // GPU加速
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }
   

  
}

.isheng{
     
.wave_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40rpx;
    .wave_bar {
      width: 4rpx;
      height: 35rpx;
      margin: 0 3px;
      background: linear-gradient(
        180deg,
        rgb(242, 243, 244)  0%,
        rgb(252, 249, 249) 50%,
         rgb(255, 255, 255)100%
      );
      border-radius: 25% !important;
      animation: shortBar 1s infinite;
      transform-origin: center center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      // 3D效果增强
      box-shadow: 
        0 2rpx 66rpx rgba(0,120,200,0.3),
        inset 0 -2rpx 4rpx rgba(255,255,255,0.2);
      
      // GPU加速
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }
  
    .container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100vh;
        transition: all 0.3s;
    
        --nav-bar-title-text-color: #fff;
    }
    .bg {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
    }
    .blur {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        opacity: 1;
        background-color: rgba(0, 0, 0, 0.4);
        // backdrop-filter: blur(80px);
    }
    
    .blurs {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 0;
        width: 100%;
        height: 100%;
        // opacity: 1;
        background-color: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(80px);
    }
    
    // .navbar {
    //     border: #1ee675 2px solid;
    //     position: relative;
    //     z-index: 2;
    //     height: 20px !important;
    //     background-color: transparent;
    //     &::after {
    //         display: none;
    //     }
    // }
    .newNavBar{
        z-index: 1;
        width: 100%;
        height: 20px;
        display: flex;
        vertical-align: middle;
        top: 8px;
        left: 20px;
        position: absolute;
        justify-content: space-between;
    }
    .crossnewNavBar{
        margin-left: 24rpx;
    }
    .times{
        height: 20px;
        width: 10px;
    }
    .content {
        position: relative;
        z-index: 1;
        height: 442px;
    }
     
    .status {
        height: 110px;
        margin-top: 18px;
        display: flex;
        justify-content: center;
        z-index: 1;
        margin-bottom: 18px;
    }
    .footer {
        position: relative;
        z-index: 1;
        box-sizing: border-box;
    }
   
    .time {
        font-weight: 400;
        color: #fff;
     padding-right: 16px;
     font-size: 14px;
    }
    .timetit {
        font-weight: 400;
        color: #fff;
     font-size: 14px;
     margin-right: 48rpx;
    }
    .actions {
        display: flex;
        gap: 80px;
        align-items: center;
        justify-content: center;
        margin-bottom: 25px;
    }
    .action_close {
        width: 112px;
        height: 112px;
        margin: 0 auto;
        &_icon {
            width: 112px;
            height: 112px;
        }
    }
    .action_chat {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 108px;
        height: 108px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 108px;
        &_icon {
            width: 48px;
            height: 48px;
        }
    }




    .action_chathengai {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 66px;
        height: 38px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 108px;
        &_icon {
            width: 16px;
            height: 16px;
        }
    }
    .action_chatheng_miniai{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 64px;
        height: 36px;
        // -----------ai求助打开
    // .action_chatheng {
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     width: 40px;
    //     height: 40px;
    //     background: rgba(0, 0, 0, 0.5);
    //     border-radius: 108px;
    //     &_icon {
    //         width: 16px;
    //         height: 16px;
    //     }
    // }
    // .action_chatheng_mini{
    //     display: flex;
    //     align-items: center;
    //     justify-content: center;
    //     width: 40px;
    //     height: 40px;
        background:  #ff2c30;
        border-radius: 108px;
        &_icon {
            width: 16px;
            height: 16px;
        }
    }
 
        // -----------ai求助打开
    .action_chatheng {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 108px;
        &_icon {
            width: 16px;
            height: 16px;
        }
    }
    .action_chatheng_mini{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background:  #ff2c30;
        border-radius: 108px;
        &_icon {
            width: 16px;
            height: 16px;
        }
    }
    .action_finish_icon_mini {
        width: 66px;
        height: 36px;
    }
    .action_say_boxheng {
        position: relative;
        z-index: 1;
        display: flex;
        gap: 13px;
        justify-content: center;
        padding-top: 10px;
    }
    .action_say_boxhengai {
        position: relative;
        z-index: 1;
        display: flex;
        margin-left: 16px;
        gap: 27rpx;
        justify-content: center;
        padding-top: 10px;
    }
    .action_say_boxhenganzhuo {
        position: relative;
        z-index: 1;
        display: flex;
        gap: 13px;
        
        padding-top: 10px;
    }
    .action_say_boxhenganzhuoai {
        position: relative;
        z-index: 1;
        display: flex;
        margin-left: 16px;
        gap: 27rpx;
        padding-top: 10px;
    }
    // -----------ai求助打开
    .action_buttonanzhuo{
        margin-top: 10px;
    }
   
    .action_say_box {
        position: relative;
        z-index: 1;
        display: flex;
        gap: 20px;
        padding-right: 48px;
        padding-left: 48px;
        padding-top: 22px;
    }
    .action_say {
        position: relative;
        z-index: 1;
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        height: 90px;
        color: rgba(243, 243, 243, 0.5);
        font-size: 28px;
        background-color: rgba(0, 0, 0, 0.2);
        border-radius: 96px;
    
    
        &_disabled {
            color: rgba(243, 243, 243, 0.5);
            background-color: rgba(0, 0, 0, 0.2);
        }
        &_active {
            color: #fff;
            background: linear-gradient(270deg, #6742ff 0%, #3d83ff 100%);
        }
        &_pressed {
            color: #fff;
            background: linear-gradient(270deg, #5237cc 0%, #3269cc 100%);
    
            &::after {
                position: absolute;
                top: -10px;
                left: -3%;
                z-index: 0;
                width: 106%;
                height: 110px;
                background: linear-gradient(270deg, rgba(103, 66, 255, 0.3) 0%, rgba(61, 131, 255, 0.3) 100%);
                border-radius: 130px;
                content: '';
            }
        }
        &_cancel {
            color: #fff;
            background: #ff4545;
            &::after {
                display: none;
            }
      
        }
        &_complete {
            margin-left: 53px;
            margin-right: 53px;
        }
    
        &_icon_speak {
            width: 48px;
            height: 48px;
            margin-right: 8px;
        }
    }
    
    .cancel_icon {
        width: 56px;
        height: 56px;
    }
    
    .error_img {
        width: 120px;
        height: 120px;
    }
    .error_tip {
        font-size: 24px;
        text-align: center;
    }
    .tip {
        font-size: 20px;
        text-align: center;
        color: rgba(255, 255, 255, 0.5);
        margin-top: 20px;
        margin-bottom: 60px;
        }
    .horizontal_context{
        position: relative;
        z-index: 1;
        color: #fff;
        height: 150px;
        flex: 1;
        // height: 0;
        display: flex;
        flex-direction: column;

        &::-webkit-scrollbar {
            width: 0;
            height: 0;
            color: transparent;
        }
        &_visible {
            visibility: visible;
        }
        &_hide{
            visibility: hidden;
        }
        &::-webkit-scrollbar {
            display:none !important;
            width:0 !important;
            height:0 !important;
            color:transparent!important;
        }
      }
     .horizontal_contexaa{
        
        position: relative;
        z-index: 1;
        color: #fff;
        height: 130px;
        flex: 1;
        // height: 0;
        display: flex;
        flex-direction: column;
        &_visible {
            visibility: visible;
        }
        &_hide{
            visibility: hidden;
        }
        &::-webkit-scrollbar {
            display:none !important;
            width:0 !important;
            height:0 !important;
            color:transparent!important;
        }
      }
     
      .horizontal_require{
        margin-top: 5px;
        position: relative;
        color:  rgba(255, 255, 255, 0.5);
        font-size: 12px;
        margin-bottom: 10px;
        z-index: 2;
        &::-webkit-scrollbar {
            display:none !important;
            width:0 !important;
            height:0 !important;
            color:transparent!important;
        }
      }
      .horizontal_requiretext{
        color:  #ffffff;
        flex: 1;
        padding-bottom: 15px;
          height: 300px !important;
          white-space: pre-wrap;
        overflow-y: hidden;
          font-size: 14px;
        //   padding-top: 14px;
          box-sizing: border-box;
          line-height: 1.5;
          position: relative;
          mask-image: -webkit-gradient(
                  linear,
                  left 70%,
                  left bottom,
                  from(rgba(0,0,0,1)),
                  to(rgba(0,0,0,0))
          ) !important;
      &::-webkit-scrollbar {
          display:none !important;
          width:0 !important;
          height:0 !important;
          color:transparent!important;
      }
    }
      .title_requiretext{
        color:  #ffffff;
        margin-left: 48px;
        flex: 1;
          height: 0;
          white-space: pre-wrap;
        margin-right: 48px;
        overflow-y: auto;
          font-size: 28px;
          padding-top: 14px;
          box-sizing: border-box;
          line-height: 1.5;
          position: relative;
          mask-image: -webkit-gradient(
                  linear,
                  left 70%,
                  left bottom,
                  from(rgba(0,0,0,1)),
                  to(rgba(0,0,0,0))
          );
      &::-webkit-scrollbar {
          display:none;
          width:0;
          height:0;
          color:transparent;
      }
    }
    
    
    .actionSheet {
        &_hide_close {
            :global {
                .van-action-sheet__close {
                    display: none !important;
                }
            }
        }
        &_group {
            .wrap-box(12px 62px 54px 62px);
        }
    }
    
    .horizontal_complete {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top:20px;
         
    }
    .actionButton{
        height: 30px;
    }
    .horizontal_completetext{
        position: relative;
        text-align: center;
        font-weight: 600;
        font-size: 16px;
        // background-color: pink;
        top: 40px;
    }
    .horizontal_buttton{
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top: 80px;
        width: 245px;
        height: 48px;
        display: flex;
        margin-top: -10px;
        font-size: 14px;
        margin-bottom: 30px;
        border-radius: 50px;
        color: #fff;
        align-items: center;
        background: linear-gradient(270deg, #6742FF 0%, #3D83FF 99%);
        text-align: center;
        justify-content: center;
    }
    .horizontal_butttons{
        font-size: 14px;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        top: 65px;
        width: 245px;
        height: 48px;
        display: flex;
        margin-bottom: 20px;
        border-radius: 50px;
        color: #777;
        align-items: center;
        background: #F6F6F6;
        text-align: center;
        justify-content: center;
    }
    .horizontal_style{
        margin-right: 45px;
        margin-left: 45px;
        display: flex;
        z-index: 1;
        margin-top: 42px;
        justify-content: start;
            height:100vh;
        .horizontal_image{
            margin-right: 16px;
            position: relative;
            width: 550px !important;
    }
     
    }
    
    .action_button{
        //-------------ai求助
        
        margin-top: 10px;
        // margin-top: 15px;
    }
   
    .action_sayheng {
        position: relative;
        z-index: 1;
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: center;
        height: 48px;
        width: 140px;
        font-size: 14px;
        border-radius: 96px;
        background: linear-gradient(270deg, #5237cc 0%, #3269cc 100%);
        color: #fff;
        background: linear-gradient(270deg, #6742ff 0%, #3d83ff 100%);
        &_icon_speak {
            width: 24px;
            height: 24px;
            margin-right: 8px;
        }
    }
    .swiper {
        height: 86%;
    }
    .swiper_cover {
        width: 100%;
        height: 100%;
        position: absolute;
        left: 0;
        top: 0;
        
        z-index: 1;
    }
    .start_tip{
        width: 200px;
        height: 50px;
        border-radius: 25px;
        font-size: 20px;
        top: 45%;
    }
    .ppt_image {
        width:100%;
        height: 100%;
    }
    .horizontal_contextss{
        height: 100%;
        margin-top: 10px;
        width: 150px;
    }
    .horizontal_contextssanzhuo{
        height: 100%;
        margin-top: 15px;
        width: 150px;
    }
    .popup_context{
        width: 315px;
        height: 100vh;
    }
    .popup_cross{
        margin-top: 16px;
        margin-left: 33px;
    }
}
.wave_container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 150rpx;
    .wave_bar {
      width: 8rpx;
      height: 70rpx;
      margin: 0 6px;
      background: linear-gradient(
        180deg,
        rgb(242, 243, 244)  0%,
        rgb(252, 249, 249) 50%,
         rgb(255, 255, 255)100%
      );
      border-radius: 25% !important;
      animation: shortBar 1s infinite;
      transform-origin: center center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      // 3D效果增强
      box-shadow: 
        0 4rpx 12rpx rgba(0,120,200,0.3),
        inset 0 -2rpx 4rpx rgba(255,255,255,0.2);
      
      // GPU加速
      transform: translateZ(0);
      backface-visibility: hidden;
    }
  }
