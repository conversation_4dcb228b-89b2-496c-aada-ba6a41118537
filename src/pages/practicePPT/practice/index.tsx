import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import FeedbackDialog from '@/components/feedbackDialog';
import FeedbackSucess from '@/components/feedbackSucess';
import { AppIdConsts } from '@/constants/appid';
import { StorageEnvKey } from '@/constants/storage';
import { getChat, getPPTSummaryDetail } from '@/services/chat';
import { uploadLog } from '@/services/common';
import type { ChatVO } from '@/types/chat';
import type { FeedbackData, LogInfo } from '@/types/common';
import { blurImage } from '@/utils/imageUtils';
import { Icon, NavBar } from '@antmjs/vantui';
import { Block, PageContainer, View } from '@tarojs/components';
import Taro, { getCurrentPages, pxTransform, useResize, useRouter } from '@tarojs/taro';
import { useUnmount } from 'ahooks';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';
import ActionMore from './components/actionMore';
import HistoryPractice from './components/historyPractice';
import PracticePPTDialog from './components/practicePPTDialog';
import PracticePPTDialogHorizontal from './components/practicePPTDialogHorizontal';
import styles from './index.less';

const App = () => {
    const [isWework] = useStorage(StorageEnvKey.IS_WEWORK);
    const { params } = useRouter<{
        chatId: string;
        pptId: string;
        text: string;
    }>();
    const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
    const { chatId, pptId, text } = params;
    // const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');
    // useResize((res) => {
    //     const newOrientation = res.size.windowWidth > res.size.windowHeight ? 'landscape' : 'portrait';
    //     setOrientation(newOrientation);
    // });
    // console.log('pamsssra', orientation);
    const [listDetail, setListDetail] = useState<ChatVO>();
    const [showMoreActionSheet, setShowMoreActionSheet] = useState(false);
    const [feedbackShow, setFeedbackShow] = useState<boolean>(false);
    const [ShowHistory, setShowHistory] = useState(false); // 脚本列表过来的

    const [feedbackSuccessShow, setFeedbackSuccessShow] = useState<boolean>(false);

    const oldSummary = useRef<string>('');
    const [summary, setSummary] = useState<string>(''); // 演练记录
    const [summaryTime, setSummaryTime] = useState<string>('');
    const feedbackData = useRef<FeedbackData>({
        client: 'wechat',
        version: '',
        brand: '',
        wxVersion: '',
        SDKVersion: '',
        model: '',
        system: '',
        platform: '',
        environment: '',
        microphoneAuthorized: undefined,
        name: '',
        phone: '',
        company: '',
        appId: '',
        path: '',
        chatId: '',
        description: '',
        logs: []
    });

    const addLog = (log: LogInfo) => {
        const params = {
            ...log,
            time: dayjs().format('YYYY-MM-DD HH:mm:ss.SSS')
        };
        console.log(params);
        feedbackData.current.logs.push(params);
    };
    useResize((res) => {
        setFeedbackShow(false);

        setShowMoreActionSheet(false);
        setShowHistory(false);
        const newOrientation = res.size.windowWidth > res.size.windowHeight ? 'landscape' : 'portrait';
        setOrientation(newOrientation);
    });
    function goBack() {
        Taro.navigateBack();
    }

    const saveSummary = (type: 'temp' | 'sentence', txt: string) => {
        setSummaryTime(dayjs().format('YYYY-MM-DD HH:mm:ss'));
        let sm = '';
        if (type === 'temp') {
            sm = oldSummary.current + txt;
        } else if (type === 'sentence') {
            oldSummary.current = oldSummary.current + txt;
            sm = oldSummary.current;
        }
        setSummary(sm);
    };

    const handleFeedback = () => {
        setShowMoreActionSheet(false);
        setFeedbackShow(true);
    };

    const submitFeedback = async (values: { description: string }) => {
        feedbackData.current.description = values.description;
        try {
            const res = await uploadLog(JSON.stringify(feedbackData.current));
            if (res.data.data) {
                feedbackData.current.logs = [];
                feedbackData.current.description = '';
                setFeedbackSuccessShow(true);
            }
        } catch (error) {
            Taro.showToast({
                title: '提交失败',
                icon: 'none'
            });
        }
    };

    Taro.useLoad(() => {
        try {
            const pages = getCurrentPages();
            feedbackData.current.path = pages[pages.length - 1].$taroPath || pages[pages.length - 1].route;

            const { appId, version } = Taro.getAccountInfoSync().miniProgram;
            feedbackData.current.appId = appId;
            feedbackData.current.version = version;
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            Taro.getSystemInfo({
                success(res: any) {
                    console.log('getSystemInfo', res);
                    feedbackData.current.brand = res.brand;
                    feedbackData.current.model = res.model;
                    feedbackData.current.platform = res.platform;
                    feedbackData.current.system = res.system;
                    feedbackData.current.wxVersion = res.version;
                    feedbackData.current.environment = res.environment;
                    feedbackData.current.SDKVersion = res.SDKVersion;
                    feedbackData.current.microphoneAuthorized = res.microphoneAuthorized;
                    feedbackData.current.name = userInfo.name;
                    feedbackData.current.company = userInfo.companyName;
                    feedbackData.current.phone = userInfo.phoneNumber;
                }
            });
        } catch (error) {
            addLog({
                level: 'error',
                message: 'getAccountInfoSync',
                data: {
                    error
                }
            });
        }
        Taro.setKeepScreenOn({
            keepScreenOn: true,
            fail(res: any) {
                console.log('keepScreenOn error', res);
            }
        });
        getChat(chatId || '').then((listRes: any) => {
            // taro预加载方法
            async function preloadImages(urls: any) {
                console.log(urls);
                const option = urls.map((item: any) => {
                    return { src: item, type: 'image' };
                });
                Taro.preloadAssets({
                    data: option,

                    success: () => {
                        console.log('核心资源预加载完成');
                    },
                    fail: (error) => {
                        console.error('关键资源加载失败:', error);
                    }
                });
            }
            if (isWework !== '1') {
                //  listRes.data.data.pptList请求接口的全部url数据

                const a = listRes.data.data.pptList.map((item: any, index: any) => {
                    const url = blurImage(
                        item.imageUrl,
                        feedbackData.current.appId === AppIdConsts.qnq ? 'huawei' : 'tencent'
                    );
                    listRes.data.data.pptList[index].imageUrlback = url;
                    return url;
                }); // 返回一个数组
                preloadImages(a); // 调用预加载方法
            }
            setListDetail(listRes.data.data);
            setSummaryTime(listRes.data.data.updateTime);
            const summary = listRes.data.data.pptList.map((item: any) => item.answer || '').join('');
            oldSummary.current = summary;
            setSummary(summary);
        });

        /* getPPTSummaryDetail(chatId || '').then((res) => {
            if (res.data.data) {
                const { answer } = res.data.data;
                // 如果存在answer并且answer以\n\n结尾，则不追加\n\n，如果不是，则追加\n\n
                if (!answer || answer.endsWith('\n\n')) {
                    oldSummary.current = answer || '';
                } else {
                    if (answer.endsWith('\n')) {
                        oldSummary.current = `${answer}\n`;
                    } else {
                        oldSummary.current = `${answer}\n\n`;
                    }
                }
                setSummary(oldSummary.current);
            }
        }); */
    });

    Taro.useUnload(() => {
        console.log('useUnload');
        feedbackData.current.description = '自动上传日志';
        Taro.eventCenter.trigger('uploadLogs', JSON.stringify(feedbackData.current));
        Storage.set(StorageEnvKey.REFRESH_HOME, 1);
        Taro.setKeepScreenOn({
            keepScreenOn: false,
            fail(res: any) {
                console.log('keepScreenOff error', res);
            }
        });
    });

    return (
        <Page className={styles.page}>
            {/* <View
                className={classNames(styles.loading_box, {
                    [styles.loading_hide]: !loading
                })}
            /> */}
            <Block>
                <View className={styles.container_page}>
                    {/* {orientation == 'landscape' ? (
                        <View className={styles.container_page_landscape}>
                            <PracticePPTDialogHorizontal
                                pptId={pptId}
                                listDetail={listDetail}
                                addLog={addLog}
                                summary={summary}
                                saveSummary={saveSummary}
                            />
                        </View>
                    ) : ( */}
                    <View
                        className={
                            orientation === 'landscape'
                                ? styles.container_page_landscape
                                : styles.container_page_portrait
                        }
                    >
                        <PracticePPTDialog
                            pptId={pptId}
                            orientation={orientation}
                            listDetail={listDetail}
                            showHistoryPractice={() => setShowHistory(true)}
                            showMore={() => setShowMoreActionSheet(true)}
                            addLog={addLog}
                            summary={summary}
                            saveSummary={saveSummary}
                            texturl={text}
                        />
                    </View>
                    {/* )} */}

                    <ActionMore
                        show={showMoreActionSheet}
                        onClose={() => setShowMoreActionSheet(false)}
                        onFeedback={handleFeedback}
                    />

                    {/* <RequirePage
                        require={require}
                        show={showRequirePage}
                        onClose={() => setRequirePage(false)}
                        onScript={() => {
                            setRequirePage(false);
                            // onShowScript();
                        }}
                        onSetting={showRequirePageSetting}
                    /> */}
                </View>
                <PageContainer
                    zIndex={100}
                    show={ShowHistory}
                    position='right'
                    onAfterLeave={() => setShowHistory(false)}
                >
                    <View className={styles.history_box}>
                        <NavBar
                            title='演练记录'
                            safeAreaInsetTop
                            renderLeft={
                                <View className={styles.countDownBox}>
                                    <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                                </View>
                            }
                        />
                        <View className={styles.history_context}>
                            <HistoryPractice scriptTime={summaryTime} summary={summary} />
                        </View>
                    </View>
                </PageContainer>
                <FeedbackDialog
                    show={feedbackShow}
                    keyHeight={0}
                    onClose={() => setFeedbackShow(false)}
                    onConfirm={submitFeedback}
                />
                <FeedbackSucess show={feedbackSuccessShow} onClose={() => setFeedbackSuccessShow(false)} />
            </Block>
        </Page>
    );
};

export default App;
