import { Page } from '@/components';
import { getChat, getPPTSummaryDetail } from '@/services/chat';
import { Icon, NavBar } from '@antmjs/vantui';
import { View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import { useEffect, useRef, useState } from 'react';
import HistoryPractice from '../practice/components/historyPractice';

import styles from './index.less';
const App = () => {
    const { params } = useRouter<{
        chatId: string;
    }>();
    const { chatId } = params;
    console.log('pamra', params);
    const [summary, setSummary] = useState<string>(''); // 演练记录
    const [audioUrl, setAudioUrl] = useState<string>('');
    const [listDetail, setListDetail] = useState<any>();
    useEffect(() => {
        try {
            getChat(chatId || '').then((listRes: any) => {
                console.log(listRes, '阿斯利康马赛克了吗@@@@11');
                setListDetail(listRes.data.data);
            });
            getPPTSummaryDetail(chatId || '').then((listRes: any) => {
                console.log(listRes, '阿斯利康马赛克了吗@@@@');
                setSummary(listRes.data.data.answer);
                setAudioUrl(listRes.data.data.audioUrl);
            });
        } catch (error) {
            console.log(error);
        }
    }, [chatId]);

    function goBack() {
        console.log('sanjanlkn');
        Taro.navigateBack();
    }

    return (
        <Page>
            <View id='topBox' className={styles.topBox}>
                {/* style='padding-top: 88px;' */}
                <NavBar
                    title='演练记录'
                    safeAreaInsetTop
                    renderLeft={
                        <View className={styles.countDownBox}>
                            <Icon name='arrow-left' size={pxTransform(48)} onClick={goBack} />
                        </View>
                    }
                />
            </View>
            <View className={styles.history_context}>
                <HistoryPractice scriptTime={listDetail?.createTime} summary={summary} chatId={chatId} url={audioUrl} />
            </View>
        </Page>
    );
};

export default App;
