:global {
    .van-loading__spinner--circular {
        border-width: 6px;
    }
  }
  page {
    background-color: #fff;
  }
  .loading {
    &_box {
      position: fixed;
      top: 0;
      left: 0;
      z-index: 500;
      width: 100%;
      height: 100vh;
      background-color: #fff;
    }
    &_hide {
      display: none;
    }
  }
 
.topBox{
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #fff;
    z-index: 99;
}
.navbar {
    z-index: 0;
}


.history_context{
  margin-top:230px;
}