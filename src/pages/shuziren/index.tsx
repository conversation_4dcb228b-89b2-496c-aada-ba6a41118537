import { Storage, useStorage } from '@/common';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { ChatMode, DeepThinkStatus } from '@/constants/voicetype';
import type { FeedbackData, UserInfo } from '@/types/common';
import { WebView } from '@tarojs/components';
import {
    eventCenter,
    getAccountInfoSync,
    getSystemInfo,
    setInnerAudioOption,
    setKeepScreenOn,
    useDidShow,
    useLoad,
    useRouter,
    useUnload
} from '@tarojs/taro';
import { useRef, useState } from 'react';
const App = () => {
    const { params } = useRouter<{
        chatId: string;
        interrupt: string;
        from: string;
        introductionType: string;
        introductionDelay: string;
    }>();
    const [userInfo] = useStorage<UserInfo>(StorageEnvKey.USERINFO);
    const [token] = useStorage<string>(StorageEnvKey.TOKEN);
    const [tenantId] = useStorage<string>(StorageEnvKey.TENANT_ID);
    const [isEnterprise] = useStorage<boolean>(StorageEnvKey.IS_ENTERPRISE);
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);

    const { chatId, from, introductionType, introductionDelay } = params;

    const [url, setUrl] = useState('');
    const feedbackData = useRef<FeedbackData>({
        client: 'h5',
        version: '',
        brand: '',
        wxVersion: '',
        SDKVersion: '',
        model: '',
        system: '',
        platform: '',
        environment: '',
        appId: '',
        path: '',
        chatId: '',
        name: '',
        phone: '',
        company: '',
        description: '',
        logs: []
    });

    const onMessage = (res: any) => {
        console.log('onMessage', res);
        const logs: any[] = [];
        res.detail.data.map((item: { type: string; data: any }) => {
            console.log(item);
            if (item.type === 'log') {
                logs.push(item.data);
            } else if (item.type === 'deepthink') {
                Storage.set(StorageEnvKey.DEEP_THINK, item.data);
            } else if (item.type === 'ChatMode') {
                Storage.set(StorageEnvKey.CHAT_MODE, item.data);
            }
        });
        feedbackData.current.logs = logs;
        eventCenter.trigger('uploadLogs', JSON.stringify(feedbackData.current));
    };
    const initUrl = async (appId: string) => {
        console.log(introductionType, typeof introductionType);
        const deepthinkStorage = Storage.get(StorageEnvKey.DEEP_THINK);
        const deepthink = deepthinkStorage === null ? DeepThinkStatus.Off : deepthinkStorage;
        const chatModeStorage = Storage.get(StorageEnvKey.CHAT_MODE);
        const chatMode = chatModeStorage === null ? ChatMode.SHUZIREN : chatModeStorage;
        let url = `${
            config.shuzirenServer
        }/#/pages/shuziren/index?chatId=${chatId}&from=${from}&appId=${appId}&tenantId=${tenantId}&isEnterprise=${isEnterprise}&isWework=${isWework}&token=${token}&timestamp=${Date.now()}&debug=${
            config.webviewDebug
        }&QCloudAIVoiceAppId=${config.QCloudAIVoice.appId}&QCloudAIVoiceSecretId=${
            config.QCloudAIVoice.secretId
        }&QCloudAIVoiceSecretKey=${config.QCloudAIVoice.secretKey}&deepthink=${deepthink}&currentChatMode=${chatMode}`;

        if (introductionType) {
            url += `&introductionType=${introductionType}&introductionDelay=${introductionDelay}`;
        }

        console.log('url', url);
        feedbackData.current.path = url;
        setUrl(url);
    };
    useLoad(() => {
        feedbackData.current.description = '自动上传日志';
        const { appId, version } = getAccountInfoSync().miniProgram;
        feedbackData.current.appId = appId;
        feedbackData.current.version = version;
        feedbackData.current.chatId = chatId;
        getSystemInfo({
            success(res: any) {
                console.log('getSystemInfo', res);
                feedbackData.current.brand = res.brand;
                feedbackData.current.model = res.model;
                feedbackData.current.platform = res.platform;
                feedbackData.current.system = res.system;
                feedbackData.current.wxVersion = res.version;
                feedbackData.current.SDKVersion = res.SDKVersion;
                feedbackData.current.environment = res.environment;
                feedbackData.current.name = userInfo.name;
                feedbackData.current.company = userInfo.companyName;
                feedbackData.current.phone = userInfo.phoneNumber;
            }
        });
        initUrl(appId);

        setKeepScreenOn({
            keepScreenOn: true,
            fail(res: any) {
                console.error('keepScreenOn error', res);
            }
        });
        setInnerAudioOption({ obeyMuteSwitch: false });
    });
    useDidShow(() => {
        setKeepScreenOn({
            keepScreenOn: true,
            fail(res: any) {
                console.error('keepScreenOn error', res);
            }
        });
    });

    useUnload(() => {
        Storage.set(StorageEnvKey.REFRESH_HOME, 1); // 返回首页要刷新列表
        setKeepScreenOn({
            keepScreenOn: false,
            fail(res: any) {
                console.error('keepScreenOff error', res);
            }
        });
        setInnerAudioOption({ obeyMuteSwitch: true });
    });

    return url && <WebView src={url} onMessage={onMessage} />;
};

export default App;
