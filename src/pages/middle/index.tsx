import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import { HomePath } from '@/constants/homePath';
import { OpenMode } from '@/constants/OpenMode';
import { StorageEnvKey } from '@/constants/storage';
import { DeepThinkStatus } from '@/constants/voicetype';
import { useLogin } from '@/hooks';
import { logout } from '@/services/user';
import type { ActionsType } from '@/types/actions';
import frontLogout from '@/utils/fontLogout';
import tenantSettingUtils from '@/utils/tenantSettingUtils';
import Taro from '@tarojs/taro';
// import dayjs from 'dayjs';
import qs from 'qs';
import styles from './index.less';

const App = () => {
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [isEnterprise] = useStorage<number>(StorageEnvKey.IS_ENTERPRISE);
    const jump = () => {
        // 企业微信
        const token = Storage.get(StorageEnvKey.TOKEN);
        const user = Storage.get(StorageEnvKey.USERINFO) || {};
        if ((isWework === '1' && token) || (isWework === '0' && user.phoneNumber)) {
            tenantSettingUtils((homePath: string) => {
                Taro.switchTab({
                    url: homePath
                });
            });
        } else {
            Storage.set(StorageEnvKey.HOME_PATH, HomePath.DIALOG);
            Taro.switchTab({
                url: HomePath.DIALOG
            });
        }
    };

    const { login } = useLogin({
        onSuccess: (userInfo, data) => {
            console.log('login success', userInfo);
            Taro.reLaunch({
                url: (data && data.url) || ''
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });

    const handleEman = async (paramsObj: ActionsType) => {
        // const { endtime } = paramsObj;
        // if (endtime && dayjs.unix(Number(endtime)).isBefore(dayjs())) {
        //     console.log('活动时间已结束');
        //     Taro.reLaunch({
        //         url: '/pages/qrcode/end/index'
        //     });
        // } else {
        const userInfo = Storage.get(StorageEnvKey.USERINFO);
        const url = `/pages/home/<USER>
        if (isEnterprise === 0) {
            // 语贝ai
            // 语贝ai开启深度思考

            if (!userInfo || !userInfo.phoneNumber) {
                // 未登录
                login({ url, openMode: paramsObj.action });
            } else {
                // 已登录
                Taro.reLaunch({
                    url
                });
            }
        } else {
            // 药企
            Storage.set(StorageEnvKey.DEEP_THINK, DeepThinkStatus.On);
            if (!userInfo || !userInfo.phoneNumber) {
                // 未登录，跳转注册
                login({ url, openMode: paramsObj.action });
            } else {
                // 已登录
                // 判断租户是否是活动的租户，如果是活动的租户，就跳转到练习页面；如果不是，判断企业列表是否有活动租户，有则跳转登录后，自动登录到活动租户，否则登出跳转注册
                const localTenantId = Storage.get(StorageEnvKey.TENANT_ID);
                if (localTenantId === paramsObj.tenantId) {
                    // 是活动的租户，就跳转到练习页面
                    Taro.reLaunch({
                        url
                    });
                } else {
                    try {
                        await logout();
                    } catch (error) {}
                    frontLogout();
                    login({ url, openMode: paramsObj.action });
                }
            }
        }
        // }
    };

    const handleScript = async (paramsObj: ActionsType) => {
        // yb.ecaiabc.com/miniprogram?action=script&emanId=1822869879490973697&scriptId=1859912455122362369&endtime=1742486399
        // const { endtime } = paramsObj;
        // if (endtime && dayjs.unix(Number(endtime)).isBefore(dayjs())) {
        //     console.log('活动时间已结束');
        //     Taro.reLaunch({
        //         url: '/pages/qrcode/end/index'
        //     });
        // } else {
        // console.log('活动时间未结束');
        const userInfo = Storage.get(StorageEnvKey.USERINFO);
        const url = `/pages/chat/sceneExercise/index?${qs.stringify(paramsObj)}`;
        if (!userInfo || !userInfo.phoneNumber) {
            // 未登录，跳转注册
            login({ url, openMode: paramsObj.action });
        } else {
            // 已登录
            // 判断租户是否是活动的租户，如果是活动的租户，就跳转到练习页面；如果不是，判断企业列表是否有活动租户，有则跳转登录后，自动登录到活动租户，否则登出跳转注册
            const localTenantId = Storage.get(StorageEnvKey.TENANT_ID);
            if (localTenantId === paramsObj.tenantId) {
                // 是活动的租户，就跳转到练习页面
                Taro.reLaunch({
                    url
                });
            } else {
                try {
                    await logout();
                } catch (error) {}
                frontLogout();
                login({ url, openMode: paramsObj.action });
            }
        }
        // }
    };

    Taro.useLoad((options: { q: string; scancode_time: string }) => {
        console.log(options);
        if (options && options.q) {
            const url = decodeURIComponent(options.q);
            // url的格式是https//yb.ecaiabc.com/miniprogram?action=eman&eman_id=1857328097277583362
            try {
                const queryString = url.split('?')[1] || '';
                const paramsObj = qs.parse(queryString) as unknown as ActionsType;
                console.log('Parsed query params:', paramsObj);
                Storage.get(StorageEnvKey.IS_ENTERPRISE);
                switch (paramsObj.action) {
                    case OpenMode.Eman:
                        // 智脑类
                        handleEman(paramsObj);
                        break;
                    case OpenMode.Script:
                        // 脚本类
                        handleScript(paramsObj);
                        break;
                }
            } catch (e) {
                console.error('Error parsing query params:', e);
                jump();
            }
        } else {
            jump();
        }
    });
    return <Page className={styles.page} />;
};

export default App;
