import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import { StorageEnvKey } from '@/constants/storage';
import { Icon, NavBar, Toast } from '@antmjs/vantui';

import { getWxCode, loginStr } from '@/services/user';
import type { VoList } from '@/types/common';
import { loginSuccess } from '@/utils/login';
import tenantSettingUtils from '@/utils/tenantSettingUtils';
import { View } from '@tarojs/components';
import Taro, { pxTransform, useRouter, useUnload } from '@tarojs/taro';
import { useMount, useThrottleFn } from 'ahooks';
import classNames from 'classnames';
import { useState } from 'react';
import styles from './index.less';
const Toast_ = Toast.createOnlyToast();
const App = () => {
    const { params } = useRouter<{ loginStr: string; code: string; url: string }>();
    const [selected, setSelected] = useState<string>();
    const [list, setList] = useState<VoList[]>([]);
    const { run: handleChoose } = useThrottleFn(
        async (data: VoList) => {
            Taro.showLoading();
            setSelected(data.tenantId);
            const { code } = await getWxCode();

            Storage.set(StorageEnvKey.TENANT_ID, data.tenantId);

            const loginStrRes = await loginStr(params.loginStr, code);
            const { data: loginStrData } = loginStrRes.data;
            console.log(loginStrData);
            if (loginStrRes.data.code === 200) {
                Storage.set(StorageEnvKey.IS_ENTERPRISE, data.enterpriseFlag ? 1 : 0);
                const { userInfo } = loginStrData;
                loginSuccess(loginStrData);
                Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息
                tenantSettingUtils((homePath) => {
                    Taro.hideLoading();
                    Toast_.show({
                        duration: 1000,
                        message: '登录成功',
                        forbidClick: true,
                        onClose: () => {
                            Taro.reLaunch({
                                url: params.url ? decodeURIComponent(params.url) : homePath
                            });
                        }
                    });
                });

                // Taro.navigateBack();
                // Taro.eventCenter.trigger(EventKey.LOGIN_SUCCESS, loginStrData.userInfo);
            } else {
                // Toast_.show({
                //     duration: 1000,
                //     message: '登录失败',
                //     forbidClick: true,
                //     onClose: () => {
                //         Taro.reLaunch({
                //             url: '/pages/home/<USER>'
                //         });
                //     }
                // });
                // Taro.eventCenter.trigger(EventKey.LOGIN_FAIL);
            }
        },
        {
            wait: 1500,
            leading: true,
            trailing: false
        }
    );
    useMount(() => {
        const res = Storage.get(StorageEnvKey.TENANT_LIST);
        console.log(res);
        if (res) {
            setList(res);
        }
    });
    useUnload(() => {
        Storage.del(StorageEnvKey.TENANT_LIST);
    });
    return (
        <Page className={styles.page}>
            <NavBar
                border={false}
                safeAreaInsetTop
                renderLeft={<Icon size={pxTransform(48)} name='arrow-left' />}
                onClickLeft={() => Taro.navigateBack()}
            />
            <View className={styles.body}>
                <View className={styles.title}>选择企业</View>
                <View className={styles.list}>
                    {list &&
                        list.map((item) => {
                            return (
                                <View
                                    className={classNames(
                                        styles['list-item'],
                                        selected === item.tenantId && styles['list-item-active']
                                    )}
                                    key={item.tenantId}
                                    onClick={() => {
                                        handleChoose(item);
                                    }}
                                >
                                    <View className={classNames('van-ellipsis', styles['list-item-name'])}>
                                        {item.companyName}
                                    </View>
                                    <Icon
                                        size={pxTransform(28)}
                                        name='arrow'
                                        color={selected === item.tenantId ? '#fff' : '#999'}
                                    />
                                </View>
                            );
                        })}
                </View>
            </View>
            <Toast_ />
        </Page>
    );
};

export default App;
