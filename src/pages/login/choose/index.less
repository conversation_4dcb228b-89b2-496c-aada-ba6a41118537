page {
  background: linear-gradient(188deg, #dddeff99 0.35%, #f8faff99 24.5%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

  --nav-bar-background-color: transparent;

  box-sizing: border-box;
  position: relative;
}

.page {
  width: 100%;
  height: 100vh;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
.body {
  padding-left: 45px;
  padding-right: 45px;
  height: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 80px;
  margin-top: 75px;
}
.list {
  width: 100%;
  flex: 1;
  height: 0;
  overflow-y: auto;

  &-item {
    height: 146px;
    font-size: 36px;
    font-weight: bold;
    display: flex;
    align-items: center;
    padding: 0 40px;
    background: #F5F6F8;
    border-radius: 32px;
    margin-bottom: 24px;

    &-name {
      flex: 1;
    }

    &-active {
      color: #fff;
      background: linear-gradient(270deg, #6746FF 0%, #4181FF 100%);
    }
  }
}