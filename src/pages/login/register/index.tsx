import { Page } from '@/components';

import { Storage, useEvent } from '@/common';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import { getWxCode, register, sendVerifyCode } from '@/services/user';
import type { ActionsType } from '@/types/actions';
import type { UserInfo } from '@/types/common';
import { loginSuccess } from '@/utils/login';
import { checkNetworkStatus } from '@/utils/networkUtils';
import { rulePhone } from '@/utils/rules';
import tenantSettingUtils from '@/utils/tenantSettingUtils';
import { Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Form, Input, View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import classNames from 'classnames';
// import dayjs from 'dayjs';
import config from '@/config';
import qs from 'qs';
import { useMemo, useRef, useState } from 'react';
import styles from './index.less';
const Toast_ = Toast.createOnlyToast();
const App = () => {
    const { params } = useRouter<{ url: string }>();
    const [canSubmit, setCanSubmit] = useState(false);
    const [codeText, setCodeText] = useState('获取验证码');
    const countRef = useRef(60);
    const formData = useRef<{
        phoneNumber: string;
        phoneCode: string;
        company: string;
        post: string;
        name: string;
    }>({
        phoneNumber: '',
        phoneCode: '',
        company: '',
        post: '',
        name: ''
    });
    const timerCode = useRef<any>();
    const [canCode, setCanCode] = useState(true);
    const [keyboardMove, setKeyboardMove] = useState<string | number>(0);

    // const back = () => {
    //     const homePath = Storage.get(StorageEnvKey.HOME_PATH);
    //     if (homePath) {
    //         Taro.switchTab({ url: homePath });
    //     } else {
    //         Taro.redirectTo({ url: HomePath.MIDDLE });
    //     }
    // };

    const qrcodeParams = useMemo(() => {
        if (params.url) {
            try {
                const url = decodeURIComponent(params.url);
                const queryString = url.split('?')[1] || '';
                const paramsObj = qs.parse(queryString) as unknown as ActionsType;
                console.log('Parsed query params:', paramsObj);

                return {
                    tenantId: paramsObj.tenantId
                    // endTime: dayjs.unix(Number(paramsObj.endtime)).format('YYYY年MM月DD日')
                };
            } catch (error) {
                return null;
            }
        } else {
            return null;
        }
    }, [params]);

    const getTenantId = () => {
        if (params.url) {
            try {
                const url = decodeURIComponent(params.url);
                const queryString = url.split('?')[1] || '';
                const paramsObj = qs.parse(queryString) as unknown as ActionsType;
                return paramsObj.tenantId;
            } catch (error) {
                return null;
            }
        } else {
            return null;
        }
    };

    const onSuccess = (loginData: { token: string; userInfo: UserInfo }) => {
        loginSuccess(loginData);
        tenantSettingUtils((homePath) => {
            Toast_.show({
                message: '登录成功',
                forbidClick: true,
                duration: 1000,
                onClose: () => {
                    Taro.reLaunch({ url: params.url ? decodeURIComponent(params.url) : homePath });
                }
            });
        });
    };
    const loginFail = () => {
        setCanSubmit(true);
        // Toast_.show({
        //     message: '登录失败'
        // });
    };

    /* const loginNoRight = () => {
        setCanSubmit(true);
        Taro.showModal({
            title: '无登录权限',
            content: '如有疑问，请联系管理员。',
            showCancel: false,
            confirmText: '好的',
            confirmColor: '#4F66FF'
        });
    }; */

    const { run: formSubmit } = useThrottleFn(
        async (e: any) => {
            console.log(e.detail);

            try {
                await checkNetworkStatus();
                const { phoneNumber, phoneCode, company, post, name } = e.detail.value;
                if (!name) {
                    Toast_.show({
                        message: '请输入姓名'
                    });
                    return;
                }

                if (!company) {
                    Toast_.show({
                        message: '请输入公司名称'
                    });
                    return;
                }

                if (!post) {
                    Toast_.show({
                        message: '请输入职位'
                    });
                    return;
                }

                if (!phoneNumber) {
                    Toast_.show({
                        message: '请输入手机号码'
                    });
                    return;
                }
                if (!phoneCode) {
                    Toast_.show({
                        message: '请输入验证码'
                    });
                    return;
                }
                // 判断手机号码格式
                if (!rulePhone.test(phoneNumber)) {
                    Toast_.show({
                        message: '请输入正确的手机号码'
                    });
                    return;
                }
                setCanSubmit(false);

                // 注册登录
                try {
                    const { code } = await getWxCode();
                    let tenantId = getTenantId();
                    console.log(tenantId, 'tenantId');
                    if (tenantId) {
                        Storage.set(StorageEnvKey.TENANT_ID, tenantId);
                    } else {
                        if (config.env === 'develop' || config.env === 'trail') {
                            tenantId = '530367';
                        } else if (config.env === 'release') {
                            tenantId = '438045';
                        }
                    }
                    Storage.set(StorageEnvKey.TENANT_ID, tenantId);
                    const loginStrRes = await register({
                        phoneNumber,
                        phoneCode,
                        company,
                        post,
                        name,
                        jsCode: code
                    });
                    const { data: loginStrData } = loginStrRes.data;
                    if (loginStrRes.data.code === 200) {
                        onSuccess(loginStrData);
                    } else {
                        loginFail();
                    }
                } catch (error) {
                    loginFail();
                }
            } catch (error) {
                console.log('error', error);
            }
        },
        {
            wait: 1500,
            leading: true,
            trailing: false
        }
    );

    const judgeCanSubmit = () => {
        Taro.nextTick(() => {
            const { phoneNumber, phoneCode, company, post, name } = formData.current;
            console.log(
                { phoneNumber, phoneCode, company, post, name },
                phoneNumber.length > 0 &&
                    phoneCode.length > 0 &&
                    company.length > 0 &&
                    post.length > 0 &&
                    name.length > 0,
                'cansubmit'
            );
            if (
                phoneNumber.length > 0 &&
                phoneCode.length > 0 &&
                company.length > 0 &&
                post.length > 0 &&
                name.length > 0
            ) {
                setCanSubmit(true);
            } else {
                setCanSubmit(false);
            }
        });
    };

    const phoneInput = (e: any) => {
        formData.current.phoneNumber = e.detail.value;
        judgeCanSubmit();
    };
    const codeInput = (e: any) => {
        formData.current.phoneCode = e.detail.value;
        judgeCanSubmit();
    };
    const companyInput = (e: any) => {
        formData.current.company = e.detail.value;
        judgeCanSubmit();
    };
    const nameInput = (e: any) => {
        formData.current.name = e.detail.value;
        judgeCanSubmit();
    };
    const positionInput = (e: any) => {
        formData.current.post = e.detail.value;
        judgeCanSubmit();
    };

    const codeCountDown = useEvent(() => {
        // 设置定时器，倒计时60s，显示codeText显示“重新获取(s)”，当0s时停止倒计时
        timerCode.current = setInterval(() => {
            if (countRef.current > 0) {
                countRef.current--;
                setCodeText(`重新获取(${countRef.current}s)`);
            } else {
                clearInterval(timerCode.current);
                setCanCode(true);
                setCodeText('获取验证码');
                countRef.current = 60;
            }
        }, 1000);
    });

    const { run: sendCode } = useThrottleFn(
        useEvent(async () => {
            console.log('sendCode');

            if (canCode) {
                if (!formData.current.phoneNumber) {
                    Toast_.show({
                        message: '请输入手机号'
                    });
                    return;
                }
                if (!rulePhone.test(formData.current.phoneNumber)) {
                    Toast_.show({
                        message: '请输入正确的手机号码'
                    });
                    return;
                }

                setCanCode(false);
                setCodeText('重新获取(60s)');
                try {
                    const res = await sendVerifyCode(formData.current.phoneNumber);
                    if (res.data.code === 200 && res.data.data) {
                        codeCountDown();
                        Toast_.show({
                            message: '已发送验证码'
                        });
                    } else {
                        setCanCode(true);
                        setCodeText('获取验证码');
                        Toast_.show({
                            message: '发送失败'
                        });
                    }
                } catch (error) {
                    console.log('code', error);
                    setCanCode(true);
                    setCodeText('获取验证码');
                }
            }
        }),
        { wait: 3000, leading: true, trailing: false }
    );

    const onKeyboardHeightChange = (e: any) => {
        console.log('onKeyboardHeightChange', e.detail.height);
        const keyHeight = e.detail.height;
        Taro.createSelectorQuery()
            .select('#codeInputBox')
            .boundingClientRect()
            .exec((res) => {
                console.log('res', res);
                const { bottom, height } = res[0];
                if (bottom > keyHeight * 2) {
                    setKeyboardMove(0);
                } else {
                    const move = keyHeight - bottom / 2 + height;
                    setKeyboardMove(move);
                }
            });
        // setKeyboardMove(keyHeight > 0 ? Taro.pxTransform(50) : 0);
    };

    return (
        <Page className={styles.page}>
            <View className={styles.body} style={{ transform: `translateY(-${keyboardMove}px)` }}>
                <View className={styles.title_box}>
                    <View className={styles.title_main}>欢迎体验AI智能陪练</View>
                    <View className={styles.title_sub}>注册即可跳转体验</View>
                </View>
                <Form onSubmit={formSubmit}>
                    <Input
                        className={styles.input}
                        type='text'
                        name='name'
                        maxlength={50}
                        onInput={nameInput}
                        placeholderClass={styles.placeholder}
                        placeholder='姓名'
                    />
                    <Input
                        className={styles.input}
                        type='text'
                        name='company'
                        maxlength={100}
                        onInput={companyInput}
                        placeholderClass={styles.placeholder}
                        placeholder='公司'
                    />

                    <Input
                        className={styles.input}
                        type='text'
                        name='post'
                        maxlength={100}
                        onInput={positionInput}
                        placeholderClass={styles.placeholder}
                        placeholder='职务'
                    />
                    <Input
                        className={styles.input}
                        type='number'
                        name='phoneNumber'
                        maxlength={11}
                        onInput={phoneInput}
                        placeholderClass={styles.placeholder}
                        placeholder='手机号'
                    />
                    <View className={classNames(styles.code_box, styles.input)} id='codeInputBox'>
                        <Input
                            name='phoneCode'
                            className={styles.code_input}
                            maxlength={6}
                            type='number'
                            onInput={codeInput}
                            placeholder='验证码'
                            adjustPosition={false}
                            placeholderClass={styles.placeholder}
                            onKeyboardHeightChange={onKeyboardHeightChange}
                        />
                        <View
                            onClick={sendCode}
                            className={classNames(styles.code_send, !canCode ? styles.code_send_disabled : '')}
                        >
                            {codeText}
                        </View>
                    </View>
                    <Button
                        className={styles.submit}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        formType='submit'
                        round
                        block
                        disabled={!canSubmit}
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        立即体验
                    </Button>
                </Form>
                {/* {qrcodeParams && qrcodeParams.endTime && (
                    <View className={styles.endtime}>{`该产品体验${qrcodeParams.endTime}前有效`}</View>
                )} */}
            </View>

            <Toast_ />
        </Page>
    );
};

export default App;
