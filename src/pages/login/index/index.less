page {
    position: relative;
    box-sizing: border-box;
    background: linear-gradient(191deg, #dddeff99 1.1%, #fdfdff99 22%),
        linear-gradient(169deg, #cff7f4 0.76%, #d1ebff 12.52%, #fff 36%);

    --nav-bar-background-color: transparent;
}

.page {
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    width: 100%;
    height: 100vh;
}
.body {
    padding-right: 45px;
    padding-left: 45px;
}
.title {
    margin-top: 75px;
    margin-bottom: 80px;
    font-weight: bold;
    font-size: 48px;
}

.input {
    box-sizing: border-box;
    height: 106px;
    padding: 0 40px;
    font-weight: 400;
    font-size: 32px;
    background-color: #f5f6f8;
    border-radius: 67px;
}
.placeholder {
    color: rgba(0, 0, 0, 0.3);
}

.code_box {
    display: flex;
    align-items: center;
    margin-top: 28px;
}
.code_input {
    flex: 1;
}
.code_send {
    color: #4f66ff;
    font-weight: 400;
    font-size: 32px;
    &_disabled {
        color: #9597a0;
    }
}
.submit {
    margin-top: 136px;
    font-weight: bold;
    font-size: 32px;
}
