import { Page } from '@/components';

import { Storage, useEvent, useStorage } from '@/common';
import { StorageEnvKey } from '@/constants/storage';
import { bindPhone, getTenantList, getWxCode, loginStr, sendVerifyCode } from '@/services/user';
import type { UserInfo } from '@/types/common';
import { loginSuccess } from '@/utils/login';
import { checkNetworkStatus } from '@/utils/networkUtils';
import { rulePhone } from '@/utils/rules';
import { Icon, NavBar, Toast } from '@antmjs/vantui';
import { Button } from '@hygeia/ui';
import { Form, Input, View } from '@tarojs/components';
import Taro, { pxTransform, useRouter } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import classNames from 'classnames';
import { useRef, useState } from 'react';
import styles from './index.less';
import tenantSettingUtils from "@/utils/tenantSettingUtils";
import { HomePath } from "@/constants/homePath";
const Toast_ = Toast.createOnlyToast();
const App = () => {
    const {params} = useRouter<{url: string}>()
    const [canSubmit, setCanSubmit] = useState(false);
    const [codeText, setCodeText] = useState('获取验证码');
    const countRef = useRef(60);
    const phone = useRef();
    const code = useRef();
    const timerCode = useRef<any>();
    const [canCode, setCanCode] = useState(true);

    const back = () => {
        const homePath = Storage.get(StorageEnvKey.HOME_PATH);
        if(homePath) {
            Taro.switchTab({ url: homePath });
        } else {
            Taro.redirectTo({url: HomePath.MIDDLE})
        }

    };

    const onSuccess = (loginData: { token: string; userInfo: UserInfo }) => {
        loginSuccess(loginData);
        tenantSettingUtils((homePath) => {
            Toast_.show({
                message: '登录成功',
                forbidClick: true,
                duration: 1000,
                onClose: () => {
                    Taro.reLaunch({ url: params.url? decodeURIComponent(params.url): homePath });
                }
            });
        })

    };
    const loginFail = () => {
        setCanSubmit(true);
        // Toast_.show({
        //     message: '登录失败'
        // });
    };

    const loginNoRight = () => {
        setCanSubmit(true);
        Taro.showModal({
            title: '无登录权限',
            content: '如有疑问，请联系管理员。',
            showCancel: false,
            confirmText: '好的',
            confirmColor: '#4F66FF'
        });
    };

    const { run: formSubmit } = useThrottleFn(
        async (e: any) => {
            console.log(e.detail);

            try {
                await checkNetworkStatus();
                const { phonenumber, phoneCode } = e.detail.value;
                if (!phonenumber) {
                    Toast_.show({
                        message: '请输入手机号码'
                    });
                    return;
                }
                if (!phoneCode) {
                    Toast_.show({
                        message: '请输入验证码'
                    });
                    return;
                }
                // 判断手机号码格式
                if (!rulePhone.test(phonenumber)) {
                    Toast_.show({
                        message: '请输入正确的手机号码'
                    });
                    return;
                }
                setCanSubmit(false);
                const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);
                if (isEnterprise === 0) {
                    // 语贝登录
                    try {
                        const bindRes = await bindPhone(phoneCode, phonenumber); // 绑定手机号
                        if (bindRes.data.code === 200) {
                            const userInfo = Storage.get(StorageEnvKey.USERINFO);
                            if (userInfo) {
                                userInfo.phoneNumber = bindRes.data.data;
                                Storage.set(StorageEnvKey.USERINFO, userInfo);
                            }
                            tenantSettingUtils((homePath) => {
                                Toast_.show({
                                    message: '登录成功',
                                    forbidClick: true,
                                    duration: 1000,
                                    onClose: () => {
                                        Taro.reLaunch({ url: params.url ? decodeURIComponent(params.url) : homePath });
                                    }
                                });
                            });

                        } else {
                            // 绑定失败
                            loginFail();
                        }
                    } catch (error) {
                        loginFail();
                    }
                } else {
                    // 药企登录
                    try {
                        const tenantListRes = await getTenantList(phoneCode, phonenumber);
                        const { data: tenantListData } = tenantListRes.data;
                        if (tenantListRes.data.code === 200) {
                            if (tenantListData.voList.length === 0) {
                                loginNoRight();
                            } else if (tenantListData.voList.length === 1) {
                                // 只有一个直接登录
                                const { code } = await getWxCode();
                                Storage.set(
                                    StorageEnvKey.IS_ENTERPRISE,
                                    tenantListData.voList[0].enterpriseFlag ? 1 : 0
                                );
                                Storage.set(StorageEnvKey.TENANT_ID, tenantListData.voList[0].tenantId);
                                const loginStrRes = await loginStr(tenantListData.loginStr, code);
                                const { data: loginStrData } = loginStrRes.data;
                                if (loginStrRes.data.code === 200) {
                                    onSuccess(loginStrData);
                                } else {
                                    loginFail();
                                }
                            } else {
                                // 有多个，跳转选择
                                Storage.set(StorageEnvKey.TENANT_LIST, tenantListData.voList);
                                Taro.redirectTo({
                                    url: `/pages/login/choose/index?loginStr=${tenantListData.loginStr}${params.url ? `&url=${params.url}`:''}`
                                });
                            }
                        } else {
                            loginFail();
                        }
                    } catch (error: any) {
                        if ('data' in error && error.data.code === 20003) {
                            loginNoRight();
                        } else {
                            loginFail();
                        }
                    }
                }
            } catch (error: any) {
                loginFail();
            }
        },
        {
            wait: 1500,
            leading: true,
            trailing: false
        }
    );

    const phoneInput = (e: any) => {
        phone.current = e.detail.value;
        if (phone.current && code.current) {
            setCanSubmit(true);
        } else {
            setCanSubmit(false);
        }
    };
    const codeInput = (e: any) => {
        code.current = e.detail.value;
        if (phone.current && code.current) {
            setCanSubmit(true);
        } else {
            setCanSubmit(false);
        }
    };

    const codeCountDown = useEvent(() => {
        // 设置定时器，倒计时60s，显示codeText显示“重新获取(s)”，当0s时停止倒计时
        timerCode.current = setInterval(() => {
            if (countRef.current > 0) {
                countRef.current--;
                setCodeText(`重新获取(${countRef.current}s)`);
            } else {
                clearInterval(timerCode.current);
                setCanCode(true);
                setCodeText('获取验证码');
                countRef.current = 60;
            }
        }, 1000);
    });

    const { run: sendCode } = useThrottleFn(
        useEvent(async () => {
            console.log('sendCode');

            if (canCode) {
                if (!phone.current) {
                    Toast_.show({
                        message: '请输入手机号'
                    });
                    return;
                }
                if (!rulePhone.test(phone.current)) {
                    Toast_.show({
                        message: '请输入正确的手机号码'
                    });
                    return;
                }

                setCanCode(false);
                setCodeText('重新获取(60s)');
                try {
                    const res = await sendVerifyCode(phone.current);
                    if (res.data.code === 200 && res.data.data) {
                        codeCountDown();
                        Toast_.show({
                            message: '已发送验证码'
                        });
                    } else {
                        setCanCode(true);
                        setCodeText('获取验证码');
                        Toast_.show({
                            message: '发送失败'
                        });
                    }
                } catch (error) {
                    console.log('code', error);
                    setCanCode(true);
                    setCodeText('获取验证码');
                }
            }
        }),
        { wait: 3000, leading: true, trailing: false }
    );

    return (
        <Page className={styles.page}>
            <NavBar
                border={false}
                safeAreaInsetTop
                renderLeft={<Icon size={pxTransform(48)} name='arrow-left' />}
                onClickLeft={() => back()}
            />

            <View className={styles.body}>
                <View className={styles.title}>手机号登录</View>
                <Form onSubmit={formSubmit}>
                    <Input
                        className={styles.input}
                        type='number'
                        name='phonenumber'
                        maxlength={11}
                        onInput={phoneInput}
                        placeholderClass={styles.placeholder}
                        placeholder='请输入手机号'
                    />
                    <View className={classNames(styles.code_box, styles.input)}>
                        <Input
                            name='phoneCode'
                            className={styles.code_input}
                            maxlength={6}
                            type='number'
                            onInput={codeInput}
                            placeholder='验证码'
                            placeholderClass={styles.placeholder}
                        />
                        <View
                            onClick={sendCode}
                            className={classNames(styles.code_send, !canCode ? styles.code_send_disabled : '')}
                        >
                            {codeText}
                        </View>
                    </View>
                    <Button
                        className={styles.submit}
                        style={{
                            '--padding-md': pxTransform(28),
                            '--button-normal-height': pxTransform(96)
                        }}
                        formType='submit'
                        round
                        block
                        disabled={!canSubmit}
                        color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    >
                        登录
                    </Button>
                </Form>
            </View>

            <Toast_ />
        </Page>
    );
};

export default App;
