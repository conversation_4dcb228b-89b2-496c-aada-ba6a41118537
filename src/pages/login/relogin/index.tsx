import Logo from '@/assets/logo.png';
import { Page } from '@/components';
import { useLogin } from '@/hooks';
import { Button } from '@hygeia/ui';
import { Image } from '@tarojs/components';
import Taro, { pxTransform } from '@tarojs/taro';
import styles from './index.less';
const App = () => {
    const { login } = useLogin({
        onSuccess: () => {
            Taro.reLaunch({
                url: '/pages/home/<USER>'
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });
    const _getPhoneNumber = async (e: any) => {
        console.log('geHP', e);
        if (e.errMsg !== 'getPhoneNumber:ok') return;
        login(e.code);
    };

    return (
        <Page className={styles.body}>
            <Image className={styles.logo} mode='aspectFit' src={Logo} />
            <Button
                style={{
                    '--padding-md': pxTransform(28),
                    '--button-normal-height': pxTransform(96)
                }}
                openType='getPhoneNumber'
                onGetPhoneNumber={(e: any) => {
                    if (e.detail.errMsg === 'getPhoneNumber:ok') {
                        Taro.nextTick(async () => {
                            await _getPhoneNumber(e.detail);
                        });
                    }
                }}
                round
                block
                color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
            >
                重新登录
            </Button>
        </Page>
    );
};

export default App;
