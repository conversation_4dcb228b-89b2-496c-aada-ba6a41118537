import { Storage, useEvent, useShow, useStorage } from '@/common';
import { Page } from '@/components';
import { StorageEnvKey } from '@/constants/storage';
import { getAllScript } from '@/services/chat';
import { NavBar } from '@antmjs/vantui';
import { Block, Image, ScrollView, Text, View } from '@tarojs/components';
import { getDeviceInfo, navigateBack, useDidShow, useRouter } from '@tarojs/taro';
import { useMount } from 'ahooks';
import classNames from 'classNames';
import { useState } from 'react';
import ScriptItem from './components/scriptList';
import ScrollableTab from './components/Tab';
import styles from './index.less';

const App = () => {
    const { params } = useRouter<any>();
    const { productName } = params;
    const [list, setList] = useState<any>([]);
    const [activeTab, setActiveTab] = useState(0);
    const [isIOS, setIsIOS] = useState(false);

    // 分页处理
    const scriptMore = async (type?: any) => {
        const result = await getAllScript(undefined, type == 0 ? undefined : type, productName);
        // const { records, total } = result.data.data;
        const records = result.data.data;
        setList(records);
    };

    useDidShow(async () => {
        const realTimeStatus = Storage.get(StorageEnvKey.Script_Type);
        setActiveTab(realTimeStatus || 0);
        scriptMore(realTimeStatus);
        Storage.del(StorageEnvKey.Script_Type);
    });
    const goBack = () => {
        navigateBack();
    };
    const tabsList = [{ tabName: '全部' }, { tabName: '技巧类' }, { tabName: '答题类' }, { tabName: '幻灯片演练' }];

    const handleTabChange = (index: any) => {
        console.log(index, 'index');
        setActiveTab(index);
        scriptMore(index);
    };
    useMount(() => {
        const isIOS = getDeviceInfo().platform === 'iOS' || getDeviceInfo().platform === 'ios';
        setIsIOS(isIOS);
    });
    return (
        <Page className={styles.page}>
            <NavBar className={styles.navbar} title='脚本列表' leftArrow safeAreaInsetTop onClickLeft={goBack} />

            <ScrollableTab tabs={tabsList} activeTab={activeTab} onTabChange={handleTabChange} />
            <ScrollView className={styles.content} scrollY lowerThreshold={50}>
                {!list || list.length === 0 ? (
                    <View className={styles.points_empty}>
                        <View>
                            <Image
                                className={styles.empty_icon}
                                src='https://assets.ecaisys.com/yubei/miniapp/x/noscript.png'
                            />
                        </View>
                        <Text className={styles.empty_text}> 暂无脚本</Text>
                    </View>
                ) : (
                    <Block>
                        {list.map((item: any) => (
                            <ScriptItem key={item.id} {...item} departmentLine={1} activeTab={activeTab} />
                        ))}
                        <View className={isIOS ? styles.safe_bottom_ios : styles.safe_bottom_android} />
                    </Block>
                )}
            </ScrollView>
        </Page>
    );
};

export default App;
