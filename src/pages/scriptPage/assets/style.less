.section {
    &_title {
        position: relative;
        margin-top: 32px;
        margin-bottom: 24px;
        margin-left: 32px;
        font-weight: bold;
        font-size: 32px;

        &::before {
            position: absolute;
            bottom: 4px;
            left: 0;
            width: 124px;
            height: 8px;
            background: linear-gradient(90deg, #4f66ff 1%, rgba(79, 102, 255, 0) 100%);
            content: '';
        }
    }
}

.avatar_image {
    display: block;
    width: 104px;
    height: 104px;
    margin-right: 24px;
    border-radius: 100%;
}
.member_info {
    display: flex;
    align-items: center;
    margin: 32px;

    &_name {
        flex: 1;
        font-weight: bold;
        font-size: 32px;
    }
}

.data {
    &_list {
        height: 176px;
        white-space: nowrap;
    }
    &_item {
        display: inline-block;
        width: 236px;
        height: 176px;
        margin-right: 20px;
        text-align: center;
        vertical-align: top;
        background-color: rgba(79, 102, 255, 0.1);
        border-radius: 12px;

        &:first-child {
            margin-left: 32px;
        }

        &:last-child {
            margin-right: 32px;
        }

        &_value {
            margin-top: 40px;
            font-weight: bold;
            font-size: 40px;
        }
        &_unit {
            font-size: 20px;
        }
        &_label {
            margin-top: 20px;
            color: rgba(39, 44, 71, 0.5);
            font-size: 24px;
        }
    }
}
.query_line {
    height: 0;
    margin-top: 24px;
    border-top: 1px solid #d8d8d8;
    opacity: 0.3;
}
.time_range {
    &_picker {
        margin-left: 32px;
        padding-right: 32px;
    }

    /* &_box {
    display: flex;
    align-items: center;
    margin: 44px 32px;
  }
  &_item {
   flex: 1;
   height: 64px;
   border-radius: 12px;
   background-color: rgba(235, 235, 235, 0.5);
   display: flex;
   align-items: center;
   justify-content: center;
   font-size: 24px;
   box-sizing: border-box;
   border: 1px solid rgba(235, 235, 235, 0.5);
   &_active {
    background: rgba(79, 102, 255, 0.1);
    color: #4F66FF;
    border: 1px solid #4F66FF;
   }
  } */
    &_calendar {
        margin-top: 32px;
    }
    &_split {
        width: 20px;
        height: 0;
        margin-right: 14px;
        margin-left: 14px;
        border-top: 1px solid #d8d8d8;
    }
}

.table_th,
.table_tr {
    display: flex;
    align-items: center;
    box-sizing: border-box;
    margin-right: 32px;
    margin-left: 32px;
    padding-right: 24px;
}

.table_th {
    padding-top: 24px;
    padding-bottom: 24px;
    color: #9e9e9e;
    font-size: 24px;
}
.table_tr {
    .table_td_order {
        color: rgba(39, 44, 71, 0.6);
        font-weight: bold;
        font-size: 24px;
    }

    .table_td_time {
        font-weight: bold;
        font-size: 32px;
    }
}
.table_td_order {
    width: 90px;
    padding-right: 28px;
    text-align: center;
}
.table_td_main {
    flex: 1;
    padding-left: 24px;
}
.table_td_time {
    width: 180px ;
    text-align: right;
}
.table_td_score {
    width: 120px;
    text-align: right;
}
.table_tr .table_td_score {
    font-weight: bold;
    font-size: 32px;
}

.table_td_name {
    font-weight: bold;
    font-size: 28px;
}
.table_td_dept {
    color: #666;
    font-size: 24px;
}

.table_td_unit {
    font-size: 20px;
}
.table_td_text {
    font-size: 24px;
}

.points_empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 30%;
    color: #9597a0;
    font-size: 26px;
}
.load_status {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-top: 16px;
    color: #9597a0;
    font-size: 26px;
}
