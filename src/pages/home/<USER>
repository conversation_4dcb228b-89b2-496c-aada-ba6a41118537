page {
    background: linear-gradient(191deg, #dddeff99 1.1%, #eef3ff99 22%), linear-gradient(169deg, #CFF7F4 0.76%, #D1EBFF 12.52%, #FFF 36%);

    --nav-bar-background-color: rgba(255, 255, 255, 0);
}
.page {
    display: flex;
    flex-direction: column;
    height: 100vh;
}
.page_body {
    height: 100%;
    overflow-y: auto;

}
.swiper {
    padding-top: 45px;
    height:calc(100vh - 45px - 97px - 98px - 113px);
}
.eman_touch {
    flex:1;
}
.history_icon{
    width: 48px;
    height: 48px;
}
.button{
    background: transparent;
    padding: 0 !important;
    display: flex;
    align-items: center;
}
.button::after{
    border: 0; 
}
.title_tab {
    display: flex;
    justify-content: center;
    align-items: center;

    &_item {
        width: 148px;
        color: #61626a;
        text-align: center;
        font-size: 32px;
        font-weight: normal;
        background: transparent;
        padding: 0 !important;
        display: flex;
        align-items: center;
        &::after {
            border: none;
        }
        &_active {
            color: #272c47;
            font-size: 40px;
            font-weight: bold;
        }
    }
}
.list_recent {
    margin: 45px 32px 0;
}
.empty {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap:24px 0;
    margin-top: 25vh;
    font-size: 28px;
    color: #9597a0;
    font-weight: 400;
    .icon{
        width: 400px;
        height: 205px;
    }
}

.split_line {
    text-align: center;
    color: #9597A0;
    font-size: 24px;
    margin-top: 36px;
    margin-bottom: 36px;
    display: flex;
    align-items: center;

    &_text {
        margin-left: 10px;
        margin-right: 10px;
    }

    &::before {
        content: '';
        display: block;
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg, #FFF, #9597A0);
    }

    &::after {
        content: '';
        display: block;
        flex: 1;
        height: 1px;
        background: linear-gradient(90deg,#9597A0, #FFF);
    }
}