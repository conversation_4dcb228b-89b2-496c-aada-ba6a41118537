import { isUndefined, Storage, useEvent, useShow, useStorage } from '@/common';
import { Page } from '@/components';
// import { EventKey } from '@/constants/eventKey';

import ChatList from '@/components/chatList';
import config from '@/config';
import { AppIdConsts } from '@/constants/appid';
import { EventKey } from '@/constants/eventKey';
import { OpenMode } from '@/constants/OpenMode';
import { StorageEnvKey } from '@/constants/storage';
import { ChatMode } from '@/constants/voicetype';
import { useLogin } from '@/hooks';
import { useSubscribe } from '@/hooks/useSubscribe';
import { createChat, getEnam, getPageEman, getRecentEman } from '@/services/chat';
import { getRandomScene } from '@/services/common';
import type { ChatHistoryVO, EmanVO } from '@/types/chat';
import type { Pagination } from '@/types/common';
import { checkHarmony, checkRecordPermission } from '@/utils/permission';
import { Dialog, Icon, NavBar } from '@antmjs/vantui';
import { Block, Image, Swiper, SwiperItem, View, Text } from '@tarojs/components';
import Taro, { pxTransform, useLoad, useRouter } from '@tarojs/taro';
import classNames from 'classnames';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import EmanCard from '../chat/components/emanCard';
import EmanRecent from '../chat/components/emanRecent';
import fakeEmanData from './eman';
import styles from './index.less';
const enum HomeTab {
    EMAN,
    RECENT
}
const Dialog_ = Dialog.createOnlyDialog();
const App = () => {
    const { params } = useRouter<{ action: string; id: string }>();
    const IconHistory = `${config.cdnPrefix}icon_history.png`;
    const RecentEmpty = `${config.cdnPrefix}recent_empty.png`;
    const EmanEmpty = `${config.cdnPrefix}empty_eman.png`;
    const [current, _setCurrent] = useState(0);
    const [chatListShow, setChatListShow] = useState(false);
    const [loading, setLoading] = useState(false);
    const [userphone, setUserphone] = useState<string>('');
    const [tab, setTab] = useState<HomeTab>();
    const refBase = useRef<any>();
    const [data, setData] = useState({} as Pagination<EmanVO>);

    const [dataRecent, setDataRecent] = useState<EmanVO[]>([]);
    const [dataRecentList, setDataRecentList] = useState<EmanVO[]>([]);
    const [dataRecentDelist, setDataRecentDelist] = useState<EmanVO[]>([]);
    const [isEnterprise] = useStorage<number>(StorageEnvKey.IS_ENTERPRISE);
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);
    const [isQnq, setIsQnq] = useState<boolean>(false);

    const swiperCurrentTimeout = useRef<any>();

    const setCurrent = (current: number) => {
        // 修复swiper组件自动快速滑动 more https://developers.weixin.qq.com/community/develop/doc/000042f32686b0fd2369839ff51400
        if (swiperCurrentTimeout.current) {
            clearTimeout(swiperCurrentTimeout.current);
        }
        swiperCurrentTimeout.current = setTimeout(() => _setCurrent(current));
    };
    const isHarmonyRef = useRef<boolean>(false);
    const loginFn = useEvent(async () => {
        console.log('loginFn', tab);
        if (tab === HomeTab.EMAN) {
            requestData();
        } else if (tab === HomeTab.RECENT) {
            requestRecent();
        }

        setLoading(loading);
    });

    const { login } = useLogin({
        onSuccess: (userInfo) => {
            setUserphone(userInfo.phoneNumber);
            console.log('login success');
            loginFn();
            Taro.showToast({
                icon: 'none',
                title: '登录成功'
            });
        },
        onError: (error: any) => {
            console.log(error);
        }
    });
    const { checkSubscribe } = useSubscribe();
    // Taro.eventCenter.on(EventKey.LOGIN_SUCCESS, (userInfo: any) => {
    //     console.log('login success', userInfo);
    //     setUserphone(userInfo.phoneNumber);
    //     loginFn();
    //     Taro.showToast({
    //         icon: 'none',
    //         title: '登录成功'
    //     });
    // });
    // Taro.eventCenter.on(EventKey.LOGIN_FAIL, () => {
    //     Taro.showToast({
    //         icon: 'none',
    //         title: '登录失败，请重试'
    //     });
    // });
    async function requestData() {
        console.info('requestData');
        const accountInfo = Taro.getAccountInfoSync();
        console.log('accountInfo', accountInfo);
        let query = {};
        const { appId } = accountInfo.miniProgram;

        if (appId === AppIdConsts.qnq) {
            query = {
                pageNo: 1,
                pageSize: 20
            };
        }
        const emanRes = await getPageEman(query);

        const { data } = emanRes.data;
        console.log(params, OpenMode.Eman, params.action === OpenMode.Eman);
        if (params.action === OpenMode.Eman) {
            // 扫码打开e人，放到第一个
            const { id } = params;
            const fd = data.records.find((item) => item.id === id);
            console.log(fd, 'fd eman');
            if (fd) {
                data.records = [fd];
                data.total = 1;
                setData(data);
            } else {
                try {
                    const emanDetailRes = await getEnam(id);
                    const eManDetail = emanDetailRes.data.data;
                    if (eManDetail) {
                        const sceneRes = await getRandomScene(eManDetail.occupation, eManDetail.title);
                        const scene = sceneRes.data.data;

                        if (scene) {
                            eManDetail.scene = scene;
                            data.records = [eManDetail];
                            data.total = 1;
                            setData(data);
                            console.log(eManDetail, scene, data, 'noeman');
                        } else {
                            setData(data);
                        }
                    } else {
                        setData(data);
                    }
                } catch (error) {
                    setData(data);
                }
            }
            /* const fdIndex = data.records.findIndex((item) => item.id === id);
            console.log(fdIndex, 'findEman');
            if (fdIndex !== -1) {
                const eman = data.records.splice(fdIndex, 1);
                console.log('eman splice', eman)
                data.records.unshift(...eman);
                setData(data);
            } else {
                try {
                    const emanDetailRes = await getEnam(id);
                    const eManDetail = emanDetailRes.data.data;
                    if (eManDetail) {
                        const sceneRes = await getRandomScene(eManDetail.occupation, eManDetail.title);
                        const scene = sceneRes.data.data;
                        if (scene) {
                            eManDetail.scene = scene;
                            data.records.unshift(eManDetail);
                            data.total += 1;
                            setData(data);
                        } else {
                            setData(data);
                        }
                    } else {
                        setData(data);
                    }
                } catch (error) {
                    setData(data);
                }
            }*/
        } else {
            const cacheData = Storage.get(StorageEnvKey.HOME_DATA);
            // console.log('cacheData', cacheData);
            console.log('data', data);
            if (!cacheData) {
                Storage.set(StorageEnvKey.HOME_DATA, JSON.stringify(data));
                setData(data);
            } else {
                let newData: any;
                try {
                    // 把缓存取出来
                    newData = JSON.parse(cacheData);
                    if (!newData) {
                        newData = data;
                    } else {
                        const fds = [];
                        for (let i = 0; i < newData.records.length; i++) {
                            // 接口数据中查找，缓存的有没有，有就更新
                            const fd = data.records.find((item) => item.id === newData.records[i].id);
                            if (fd) {
                                fds.push(fd.id);
                                const { scene } = newData.records[i];
                                const sceneNew = { ...scene };
                                sceneNew.id = fd.scene.id;
                                newData.records[i] = { ...fd };
                                newData.records[i].scene = sceneNew;
                            } else {
                                newData.records[i] = null;
                            }
                        }
                        // 删除缓存中，接口数据中没有的数据
                        newData.records = newData.records.filter((item: any) => item !== null);

                        // 把剩余的接口数据中新的数据添加到缓存中
                        for (let i = 0; i < data.records.length; i++) {
                            if (!fds.includes(data.records[i].id)) {
                                newData.records.push(data.records[i]);
                            }
                        }
                    }
                } catch (error) {
                    newData = data;
                }
                console.log('newData', newData);
                setData(newData);
                Storage.set(StorageEnvKey.HOME_DATA, JSON.stringify(newData));
            }
        }
    }

    function requestRecent() {
        getRecentEman().then((res) => {
            // console.log(res);
            if (res.data.code === 200) {
                setDataRecent(res.data.data);
                setDataRecentList(res.data.data.filter((item) => item.status === 3));
                setDataRecentDelist(res.data.data.filter((item) => item.status !== 3));
            } else if (res.data.code === 401) {
                setUserphone('');
            }
        });
    }

    useShow(() => {
        // 聊天结束返回需要调用
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);

            if (token) {
                requestData();
                if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                    if (chatListShow) {
                        refBase!.current.onInitQuery();
                    }

                    // 刷新过后要把刷新标记设置为0
                    Storage.set(StorageEnvKey.REFRESH_HOME, 0);
                }
            } else {
                login();
            }
        } else {
            if (Storage.get(StorageEnvKey.REFRESH_HOME) === 1) {
                if (chatListShow) {
                    refBase!.current.onInitQuery();
                }

                // 刷新过后要把刷新标记设置为0
                Storage.set(StorageEnvKey.REFRESH_HOME, 0);
            }
            const userInfo = Storage.get(StorageEnvKey.USERINFO);
            console.log('onShow', userInfo);
            if (!userInfo) {
                setChatListShow(false);
                setUserphone('');
                setTab(HomeTab.EMAN);
                /* const isEnterprise = Storage.get(StorageEnvKey.IS_ENTERPRISE);
                if (isEnterprise === 0) {
                    requestData();
                } else {
                    setData(fakeEmanData.data);
                } */
                setData(fakeEmanData.data);
                setDataRecent([]);
            } else {
                console.log(userInfo.phoneNumber);
                setUserphone(userInfo.phoneNumber);

                loginFn();
            }
        }
    });

    const initPage = async () => {
        setTab(HomeTab.EMAN);
        const accountInfo = Taro.getAccountInfoSync();
        setIsQnq(accountInfo.miniProgram.appId === AppIdConsts.qnq);
        Storage.del(StorageEnvKey.HOME_DATA);
        const isHarmony = await checkHarmony();
        isHarmonyRef.current = isHarmony;
        Taro.eventCenter.on(EventKey.LOGOUT, () => {
            setUserphone('');
        });
    };

    useLoad(() => {
        initPage();
    });

    useEffect(() => {
        if (tab === HomeTab.RECENT) {
            requestRecent();
        }
    }, [tab]);

    // useEffect(() => {
    //     if (chatListShow) {
    //         Taro.hideTabBar();
    //     } else {
    //         Taro.showTabBar();
    //     }
    // }, [chatListShow]);

    // useMemo(async () => {

    // }, [OPENID, loading, USERINFO]);

    const openSceneExercise = (emanId: string, sceneId: string) => {
        Taro.navigateTo({
            url: `/pages/chat/sceneExercise/index?emanId=${emanId}&sceneId=${sceneId}`
            // url: `/pages/chat/enterpriseIntelligence/index?emanId=${emanId}&sceneId=${sceneId}`
        });
    };
    const openEnterpriseIntelligence = async (item: EmanVO) => {
        const { data } = await createChat({ emanId: item.id, sceneId: item.scene.id });
        if (item.zipFileUrl && item.show3dFlag) {
            if (
                isHarmonyRef.current ||
                Storage.get(StorageEnvKey.CHAT_MODE) === ChatMode.SHUZIREN ||
                Storage.get(StorageEnvKey.CHAT_MODE) === null
            ) {
                // 鸿蒙next跳转数字人
                Taro.navigateTo({
                    url: `/pages/shuziren/index?chatId=${data.data?.id}&from=home&introductionType=${
                        data.data.introductionType
                    }&introductionDelay=${data.data.introductionDelay}&introduction=${data.data.introduction || ''}`
                });
            } else {
                Taro.navigateTo({
                    url: `/pages/chat/enterpriseIntelligence/index?chatId=${data.data?.id}&introductionType=${
                        data.data.introductionType
                    }&introductionDelay=${data.data.introductionDelay}&introduction=${data.data.introduction || ''}`
                });
            }
        } else {
            Taro.navigateTo({
                url: `/pages/chat/enterpriseIntelligence/index?chatId=${data.data?.id}&introductionType=${
                    data.data.introductionType
                }&introductionDelay=${data.data.introductionDelay}&introduction=${data.data.introduction || ''}`
            });
        }
    };

    const handleLogin = useEvent((cb: () => void) => {
        if (isWework === '1') {
            const token = Storage.get(StorageEnvKey.TOKEN);

            if (token) {
                cb();
            } else {
                login();
            }
        } else {
            console.log('handleLogin', userphone);
            if (userphone) {
                cb();
            } else {
                login();
            }
        }
    });
    const setShowDialog = () => {
        handleLogin(() => setChatListShow(true));
    };

    const tabCurrent = useMemo(() => {
        if (tab === HomeTab.EMAN) {
            return 0;
        } else {
            return 1;
        }
    }, [tab]);

    const changeTab = (e: any) => {
        if (e.detail.current === 0) {
            setTab(HomeTab.EMAN);
        }
        if (e.detail.current === 1) {
            setTab(HomeTab.RECENT);
        }
    };

    const openEmanDetail = (id: string) => {
        Taro.navigateTo({
            url: `/pages/emanDetail/index?id=${id}`
        });
    };

    const onEmanAction = useCallback(
        (item: EmanVO) => {
            handleLogin(async () => {
                /* checkSubscribe().then(
                async () => {
                    if (item.type === 2) {
                        try {
                            await checkRecordPermission(Dialog_);
                            openEnterpriseIntelligence(item);
                        } catch (error) {
                            // 拒绝授权
                        }
                    } else {
                        openSceneExercise(item.id, item.scene.id);
                    }
                },
                () => {}
            ); */

                if (isWework === '0' && !isQnq) {
                    checkSubscribe().then(
                        async () => {
                            if (item.type === 2) {
                                try {
                                    await checkRecordPermission(Dialog_);
                                    openEnterpriseIntelligence(item);
                                } catch (error) {
                                    // 拒绝授权
                                }
                            } else {
                                openSceneExercise(item.id, item.scene.id);
                            }
                        },
                        () => {}
                    );
                } else {
                    if (item.type === 2) {
                        try {
                            await checkRecordPermission(Dialog_);
                            openEnterpriseIntelligence(item);
                        } catch (error) {
                            // 拒绝授权
                        }
                    } else {
                        openSceneExercise(item.id, item.scene.id);
                    }
                }
            });
        },
        [isQnq, isWework]
    );

    const handleHistoryClick = async (item: ChatHistoryVO) => {
        if (item.type == 3) {
            if (!item.done) {
                // 跳转到进行中的ppt页面
                Taro.navigateTo({ url: `/pages/practicePPT/practice/index?chatId=${item.id}&from=history` });
            } else {
                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/practicePPT/historyPPT/index?chatId=${item.id}&from=history` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}`
                    });
                }
            }
        } else {
            if (item.emanType === 1) {
                // done：是否结束对话。
                // status:1-无报告：看历史聊天记录，2-生成成功：看报告详情，3-生成失败：报告详情，4-生成中：报告详情点击重试后。

                if (!item.done) {
                    const chatMode = Storage.get(StorageEnvKey.CHAT_MODE);
                    console.log('chatMode', chatMode);
                    if (item.zipFileUrl && item.show3dFlag) {
                        if (isHarmonyRef.current || chatMode === null || chatMode === ChatMode.SHUZIREN) {
                            Taro.navigateTo({ url: `/pages/shuziren/index?chatId=${item.id}&from=history` });
                        } else {
                            await checkRecordPermission(Dialog_);
                            Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.id}&from=history` });
                        }
                    } else {
                        try {
                            await checkRecordPermission(Dialog_);
                            Taro.navigateTo({ url: `/pages/chat/dialogue/index?chatId=${item.id}&from=history` });
                        } catch (error) {
                            // 拒绝授权
                        }
                    }

                    return;
                }

                if (item.status === 1) {
                    Taro.navigateTo({ url: `/pages/chat/history/index?chatId=${item.id}&from=history` });
                } else if (item.status === 2 || item.status === 3 || item.status === 4) {
                    Taro.navigateTo({
                        url: `/pages/chat/report/index?id=${item.chatReportId}&list_status=${item.status}`
                    });
                }
            }
            if (item.emanType === 2) {
                // 企业智脑
                Taro.navigateTo({
                    url: `/pages/chat/enterpriseIntelligence/index?chatId=${item.id}&from=history`
                });
            }
        }
    };

    return (
        <Page loading={isUndefined(data) && loading} className={styles.page}>
            <NavBar
                title={
                    <View className={styles.title_tab}>
                        <View
                            className={classNames(
                                styles.title_tab_item,
                                tab === HomeTab.EMAN ? styles.title_tab_item_active : ''
                            )}
                            onClick={() => setTab(HomeTab.EMAN)}
                        >
                            E人
                        </View>
                        <View
                            className={classNames(
                                styles.title_tab_item,
                                tab === HomeTab.RECENT ? styles.title_tab_item_active : ''
                            )}
                            onClick={() => {
                                handleLogin(() => setTab(HomeTab.RECENT));
                            }}
                        >
                            最近
                        </View>
                    </View>
                }
                safeAreaInsetTop
                renderLeft={<Image src={IconHistory} className={styles.history_icon} mode='aspectFit' />}
                onClickLeft={() => setShowDialog()}
            />
            <Swiper style={{ flex: 1 }} current={tabCurrent} onChange={changeTab}>
                <SwiperItem style={{ height: '100%' }}>
                    <View className={styles.page_body}>
                        <Block style={{ display: tab === HomeTab.EMAN ? 'block' : 'none' }}>
                            {(data?.records ?? []).length > 0 && (
                                <Swiper
                                    className={styles.swiper}
                                    circular
                                    previousMargin={(data?.records ?? []).length === 1 ? pxTransform(32) : '0'}
                                    nextMargin={pxTransform(32)}
                                    snapToEdge
                                    onChange={(e) => setCurrent(e.detail.current)}
                                    current={current}
                                >
                                    {(data?.records ?? []).map((item, index) => {
                                        return (
                                            <SwiperItem key={item.id}>
                                                <EmanCard
                                                    style={{
                                                        margin: current === index ? 'auto' : 'none',
                                                        transform: current !== index ? 'scale(0.96)' : 'none',
                                                        transition: 'transform 0.3s ease'
                                                    }}
                                                    {...item}
                                                    onOk={() => onEmanAction(item)}
                                                    shuziren={!!item.zipFileUrl && item.show3dFlag}
                                                    isEnterprise={isEnterprise}
                                                    isQnq={isQnq}
                                                    onEmanClick={() => {
                                                        handleLogin(() => openEmanDetail(item.id));
                                                    }}
                                                />
                                            </SwiperItem>
                                        );
                                    })}
                                </Swiper>
                            )}
                            {!loading && (data?.records ?? []).length === 0 && (
                                <View className={styles.empty}>
                                    <Image className={styles.icon} src={EmanEmpty} />
                                    <Text>暂无E人，请联系管理员</Text>
                                </View>
                            )}
                        </Block>
                    </View>
                </SwiperItem>
                <SwiperItem style={{ height: '100%' }}>
                    <View className={styles.page_body}>
                        <View
                            className={styles.list_recent}
                            style={{ display: tab === HomeTab.RECENT ? 'block' : 'none' }}
                        >
                            {dataRecent &&
                                dataRecent.length > 0 &&
                                dataRecentList.map((item) => (
                                    <EmanRecent
                                        key={item.id}
                                        {...item}
                                        shuziren={!!item.zipFileUrl && item.show3dFlag}
                                        isEnterprise={isEnterprise}
                                        isQnq={isQnq}
                                        onOk={() => onEmanAction(item)}
                                        onEmanClick={() => {
                                            handleLogin(() => openEmanDetail(item.id));
                                        }}
                                    />
                                ))}
                            {dataRecent && dataRecent.length > 0 && (
                                <Block>
                                    {dataRecentDelist.length > 0 && (
                                        <View className={styles.split_line}>
                                            <Text className={styles.split_line_text}>以下E人已下架</Text>
                                        </View>
                                    )}

                                    {dataRecentDelist.map((item) => (
                                        <EmanRecent
                                            key={item.id}
                                            {...item}
                                            shuziren={!!item.zipFileUrl && item.show3dFlag}
                                            isEnterprise={isEnterprise}
                                            isQnq={isQnq}
                                            onOk={() => {
                                                // Taro.showToast({ title: 'E人已下架', icon: 'none' });
                                            }}
                                            onEmanClick={() => {
                                                Taro.showToast({ title: 'E人已下架', icon: 'none' });
                                            }}
                                        />
                                    ))}
                                </Block>
                            )}
                            {(!dataRecent || dataRecent.length === 0) && (
                                <View className={styles.empty}>
                                    <Image className={styles.icon} src={RecentEmpty} />
                                    <Text>暂无最近对话E人</Text>
                                </View>
                            )}
                        </View>
                    </View>
                </SwiperItem>
            </Swiper>
            <ChatList
                show={chatListShow}
                refBase={refBase}
                onCloseFn={() => setChatListShow(false)}
                onClick={handleHistoryClick}
            />
            <Dialog_ />
        </Page>
    );
};

export default App;
