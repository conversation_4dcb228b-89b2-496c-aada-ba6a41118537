@import '@/styles/index.less';
.pk_index {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background: url('@/assets/background.png') no-repeat center center;
    --nav-bar-background-color: transparent;
}

.pk_container {
    flex: 1;
    overflow: auto;
}

.van-nav-bar {
    background-color: unset;
}

.content {
    flex: 1;
    box-sizing: border-box;
    height: 0;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 0 32px;
    overflow-y: auto;
}

.title_tab_item {
    color: #000000;
    font-weight: bold;
    font-size: 34px;
    text-align: center;
}
.pk_tabs {
    display: flex;
    gap: 16px;
    margin: 8px 0 20px;
    padding: 0 32px;
    font-size: 32px;
}

.pk_tabs .tab {
    color: #b8b2d6;
    display: flex;
    align-items: flex-end;
}

.pk_tabs .tab.active {
    color: #3c2f7a;
    font-weight: 700;
    font-size: 40px;
}

.banner_card {
    position: relative;
    overflow: hidden;
    background: rgba(162, 117, 255, 0.1);
    border-radius: 24px;
}

.banner_img {
    width: 100%;
    height: 360px;
    border-radius: 24px;
}

.banner_mask {
    position: absolute;
    right: 0;
    bottom: 0;
    left: 0;
    height: 120px;
    background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, rgba(0, 0, 0, 0.45) 100%);
}

.banner_tabs {
    position: absolute;
    // bottom: 16px;
    // bottom: 120px;
    top: 290px;
    right: 0;
    left: 0;
    display: flex;
    justify-content: space-between;
    padding: 0 16px;
}

.banner_tab {
    flex: 1;
    height: 56px;
    margin-right: 12px;
    color: #ffffff;
    font-size: 24px;
    line-height: 56px;
    text-align: center;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 12px;
}

.banner_tab.active {
    background: linear-gradient(90deg, #a275ff 0%, #7a45ff 100%);
    // color: #8F05E5;
}

.banner_tab:last-child {
    margin-right: 0;
}

.config_card {
    display: flex;
    // gap: 12px;
    align-items: center;
    justify-content: center;
    margin: 0;
    // margin-top: 16px;
    padding: 14px 16px;
    font-weight: 600;
    font-size: 30px;
    border-radius: 20px;
}

.config_icon {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.config_text {
    color: #3c2f7a;
    font-weight: 600;
    font-size: 28px;
}

/* 配置PK内容弹窗样式 */
.cfg_dialog {
    box-sizing: border-box;
    width: 640px;
    padding: 24px 24px 32px;
    // background: linear-gradient(180deg, #f6f7ff 0%, #ffffff 40%, #fff 100%);
    background: url('@/assets/pk/bg_dialog.png') no-repeat center center;
    background-size: 100% 100%;
    border-radius: 24px;
}
.cfg_title {
    margin-bottom: 12px;
    color: #272c47;
    font-weight: 700;
    font-size: 32px;
    text-align: center;
}
.cfg_item {
    margin-top: 16px;
}
.cfg_label {
    margin-bottom: 8px;
    color: #272c47;
    font-size: 26px;
}
.cfg_select {
    height: 88px;
    padding: 0 20px;
    color: #a3a4a9;
    font-size: 26px;
    line-height: 88px;
    // background: #ffffff;
    background-color: rgba(255, 255, 255, 0.3);
    border: 1px solid #ebedf0;
    border-radius: 12px;
}
.cfg_award_img {
    display: block;
    width: 80%;
    margin: 20px auto 8px;
}
.cfg_award_text {
    color: #272c47;
    font-weight: 600;
    font-size: 26px;
    text-align: center;
}
.cfg_desc {
    margin-top: 6px;
    color: #6b51ff;
    font-size: 24px;
    text-align: center;
}
.cfg_current_time {
    margin-top: 6px;
    color: #8d9094;
    font-size: 18px;
    text-align: center;
}
.cfg_btns {
    display: flex;
    gap: 16px;
    margin-top: 16px;
}
.btn {
    flex: 1;
    height: 88px;
    color: #6c6d75;
    font-size: 28px;
    line-height: 88px;
    text-align: center;
    background: #f3f4f6;
    border-radius: 999px;
}
.btn_cancel {
  background: rgba(0, 0, 0, 0.05);
  color: #8D9094;
}
.btn_primary {
    color: #fff;
    // background: linear-gradient(270deg, #6742ff 0.03%, #3d83ff 100.03%);
    background: linear-gradient(0deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%),
        linear-gradient(
            96deg,
            #31bcff 3.81%,
            #9676ff 27.76%,
            #be64fe 43.31%,
            #e157cb 53.48%,
            #ef5794 63.36%,
            #fd683f 70.73%,
            #fe7c2b 85.61%,
            #ffa10b 99.78%
        );
    background-blend-mode: hard-light, normal;
}
.cfg_tips {
    margin-top: 12px;
    color: #a3a4a9;
    font-size: 22px;
    text-align: center;
}

.pk_square {
    display: flex;
    flex-direction: column;
    width: 100%;
}

/* 我的PK样式 */
.sub_tabs {
    display: flex;
    margin: 20px 32px 16px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 20px;
    padding: 4px;
}

.sub_tab {
    flex: 1;
    height: 60px;
    line-height: 60px;
    text-align: center;
    color: #8f8aa8;
    font-size: 26px;
    border-radius: 16px;
    transition: all 0.3s;
}

.sub_tab_active {
    color: #ffffff;
    background: linear-gradient(90deg, #a275ff 0%, #7a45ff 100%);
    // background: url('@/assets/pk/tab_left.svg') no-repeat 100% 100%;
}

.info_bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 32px 20px;
    padding: 16px 20px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 16px;
}

.info_text {
    color: #272c47;
    font-size: 26px;
}

.countdown {
    color: #6b51ff;
    font-size: 24px;
    font-weight: 600;
}

.challenge_list {
    margin: 0 32px 20px;
}

.challenge_card {
    display: flex;
    align-items: center;
    padding: 24px 20px;
    background: linear-gradient(90deg, #31bcff 0%, #ffa10b 100%);
    border-radius: 20px;
    margin-bottom: 16px;
}

.challenge_initiator,
.challenge_challenger {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.challenge_label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 22px;
    margin-bottom: 8px;
}

.challenge_avatar {
    width: 60px;
    height: 60px;
    border-radius: 30px;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
}

.avatar_img {
    width: 56px;
    height: 56px;
    border-radius: 28px;
}

.plus_icon {
    color: #ffffff;
    font-size: 32px;
    font-weight: 300;
}

.challenge_name {
    color: #ffffff;
    font-size: 24px;
    font-weight: 600;
}

.challenge_reward {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 20px;
}

.coins_container {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
}

.coin_icon {
    width: 32px;
    height: 32px;
}

.points_badge {
    padding: 6px 16px;
    background: rgba(107, 81, 255, 0.9);
    border-radius: 20px;
}

.points_text {
    color: #ffffff;
    font-size: 22px;
    font-weight: 600;
}

.pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 32px 40px;
    gap: 20px;
}

.page_btn {
    padding: 12px 20px;
    color: #6b51ff;
    font-size: 24px;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 20px;
}

.page_numbers {
    display: flex;
    gap: 12px;
}

.page_number {
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    color: #8f8aa8;
    font-size: 24px;
    border-radius: 20px;
    background: rgba(255, 255, 255, 0.3);
}

.page_active {
    color: #ffffff;
    background: linear-gradient(90deg, #a275ff 0%, #7a45ff 100%);
}
