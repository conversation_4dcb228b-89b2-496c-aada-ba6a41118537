import IconConfig from '@/assets/pk/icon_config.svg';
import score from '@/assets/pk/score.png';
import { Dialog, NavBar, Pagination } from '@antmjs/vantui';
import { Image, Picker, ScrollView, Text, View } from '@tarojs/components';
import { useLoad } from '@tarojs/taro';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import styles from './index.less';
import PkSquare from './pkSquare';

export default function PkIndex() {
    useLoad(() => {
        console.log('Page loaded.');
    });
    const [activeTab, setActiveTab] = useState<number>(1); // 0: 竞技场 1: 广场 2: 我的
    const banners = [
        'https://obsqingnang.guoyaoplat.com/miniprom/banner1.png',
        'https://obsqingnang.guoyaoplat.com/miniprom/banner2.png',
        'https://obsqingnang.guoyaoplat.com/miniprom/banner3.png',
        'https://obsqingnang.guoyaoplat.com/miniprom/banner4.png'
    ];
    const [bannerIdx, setBannerIdx] = useState<number>(0); // 默认"情景模拟PK"
    const [showConfig, setShowConfig] = useState<boolean>(false);

    // 我的PK相关状态
    const [myPkSubTab, setMyPkSubTab] = useState<number>(0); // 0: 新擂台 1: 已结束擂台
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [currentTime, setCurrentTime] = useState<string>(dayjs().format('YYYY.MM.DD HH:mm:ss'));

    // 选择器数据
    const productOptions = ['金振口服液'];
    const scriptOptions = ['金振口服液问与答'];
    const pointsOptions = ['30积分'];


    const [productIdx, setProductIdx] = useState<number>(0);
    const [scriptIdx, setScriptIdx] = useState<number>(0);
    const [pointsIdx, setPointsIdx] = useState<number>(0);

    // 弹窗显示时开始计时，关闭时清理
    useEffect(() => {
        if (!showConfig) return;
        const timer = setInterval(() => {
            setCurrentTime(dayjs().format('YYYY.MM.DD HH:mm:ss'));
        }, 1000);
        return () => clearInterval(timer);
    }, [showConfig]);

    return (
        <View className={styles.pk_index}>
            <NavBar title={<View className={styles.title_tab_item}>PK赛</View>} safeAreaInsetTop />
            {/* 顶部标签 */}
            <View className={styles.pk_tabs}>
                <Text
                    className={`${styles.tab} ${activeTab === 0 ? styles.active : ''}`}
                    onClick={() => setActiveTab(0)}
                >
                    PK竞技场
                </Text>
                <Text
                    className={`${styles.tab} ${activeTab === 1 ? styles.active : ''}`}
                    onClick={() => setActiveTab(1)}
                >
                    PK广场
                </Text>
                <Text
                    className={`${styles.tab} ${activeTab === 2 ? styles.active : ''}`}
                    onClick={() => setActiveTab(2)}
                >
                    我的PK
                </Text>
            </View>

            <ScrollView className={styles.content} scrollY lowerThreshold={50}>
                {activeTab === 0 && (
                    <>
                        {/* 大横幅卡片 */}
                        <View className={styles.banner_card}>
                            <Image className={styles.banner_img} src={banners[bannerIdx]} mode='aspectFill' />
                            {/* <View className={styles.banner_mask} /> */}
                            <View className={styles.banner_tabs}>
                                <View
                                    className={`${styles.banner_tab} ${bannerIdx === 0 ? styles.active : ''}`}
                                    onClick={() => setBannerIdx(0)}
                                >
                                    答题PK
                                </View>
                                <View
                                    className={`${styles.banner_tab} ${bannerIdx === 1 ? styles.active : ''}`}
                                    onClick={() => setBannerIdx(1)}
                                >
                                    情景模拟PK
                                </View>
                                <View
                                    className={`${styles.banner_tab} ${bannerIdx === 2 ? styles.active : ''}`}
                                    onClick={() => setBannerIdx(2)}
                                >
                                    PPT演练PK
                                </View>
                                <View
                                    className={`${styles.banner_tab} ${bannerIdx === 3 ? styles.active : ''}`}
                                    onClick={() => setBannerIdx(3)}
                                >
                                    角色扮演PK
                                </View>
                            </View>
                            {/* 配置按钮卡片 */}
                            <View className={styles.config_card} onClick={() => setShowConfig(true)}>
                                {/* <View className={styles.config_icon} /> */}
                                <Image className={styles.config_icon} src={IconConfig} />
                                <Text className='linear_text'>配置PK内容</Text>
                            </View>
                        </View>
                    </>
                )}

                {activeTab === 1 && (
                    <View className={styles.pk_square}>
                        <PkSquare/>
                    </View>
                )}

                {activeTab === 2 && (
                    <>
                        {/* 子标签 */}
                        <View className={styles.sub_tabs}>
                            <View 
                                className={`${styles.sub_tab} ${myPkSubTab === 0 ? styles.sub_tab_active : ''}`}
                                onClick={() => setMyPkSubTab(0)}
                            >
                                新擂台
                            </View>
                            <View 
                                className={`${styles.sub_tab} ${myPkSubTab === 1 ? styles.sub_tab_active : ''}`}
                                onClick={() => setMyPkSubTab(1)}
                            >
                                已结束擂台
                            </View>
                        </View>

                        {/* 滚动信息栏 */}
                        <View className={styles.info_bar}>
                            <View className={styles.info_text}>PK产品 金振口服液</View>
                            <View className={styles.countdown}>剩余时间: 00:01:02</View>
                        </View>

                        {/* 挑战卡片列表 */}
                        <View className={styles.challenge_list}>
                            {/* 示例挑战卡片 */}
                            <View className={styles.challenge_card}>
                                <View className={styles.challenge_initiator}>
                                    <Text className={styles.challenge_label}>发起者</Text>
                                    <View className={styles.challenge_avatar}>
                                        <Image 
                                            src={require('@/assets/eMan/1.png')} 
                                            mode='aspectFill' 
                                            className={styles.avatar_img}
                                        />
                                    </View>
                                    <Text className={styles.challenge_name}>张三</Text>
                                </View>
                                
                                <View className={styles.challenge_reward}>
                                    <View className={styles.coins_container}>
                                        <Image src={score} className={styles.coin_icon} />
                                        <Image src={score} className={styles.coin_icon} />
                                        <Image src={score} className={styles.coin_icon} />
                                    </View>
                                    <View className={styles.points_badge}>
                                        <Text className={styles.points_text}>30积分</Text>
                                    </View>
                                </View>
                                
                                <View className={styles.challenge_challenger}>
                                    <Text className={styles.challenge_label}>挑战者</Text>
                                    <View className={styles.challenge_avatar}>
                                        <View className={styles.plus_icon}>+</View>
                                    </View>
                                    <Text className={styles.challenge_name}>等待加入</Text>
                                </View>
                            </View>
                        </View>

                        {/* 分页控件（VantUI Pagination） */}
                        <View style={{ padding: '16px 0 24px' }}>
                            <Pagination
                                totalItems={40}
                                itemsPerPage={10}
                                modelValue={currentPage}
                                onChange={(v) => setCurrentPage(v)}
                                showPageSize={4}
                                prevText='上一页'
                                nextText='下一页'
                            />
                        </View>
                    </>
                )}
            </ScrollView>

            {/* 配置PK内容弹窗 */}
            <Dialog show={showConfig} onClose={() => setShowConfig(false)} showConfirmButton={false} overlay>
                <View className={styles.cfg_dialog}>
                    <View className={styles.cfg_title}>配置PK内容</View>

                    <View className={styles.cfg_item}>
                        <View className={styles.cfg_label}>请选择PK产品</View>
                        <Picker
                            mode='selector'
                            range={productOptions}
                            value={productIdx}
                            onChange={(e) => setProductIdx(Number(e.detail.value))}
                        >
                            <View className={styles.cfg_select}>{productOptions[productIdx]}</View>
                        </Picker>
                    </View>

                    <View className={styles.cfg_item}>
                        <View className={styles.cfg_label}>请选择产品脚本</View>
                        <Picker
                            mode='selector'
                            range={scriptOptions}
                            value={scriptIdx}
                            onChange={(e) => setScriptIdx(Number(e.detail.value))}
                        >
                            <View className={styles.cfg_select}>{scriptOptions[scriptIdx]}</View>
                        </Picker>
                    </View>

                    <View className={styles.cfg_item}>
                        <View className={styles.cfg_label}>挑战积分</View>
                        <Picker
                            mode='selector'
                            range={pointsOptions}
                            value={pointsIdx}
                            onChange={(e) => setPointsIdx(Number(e.detail.value))}
                        >
                            <View className={styles.cfg_select}>{pointsOptions[pointsIdx]}</View>
                        </Picker>
                    </View>

                    <Image className={styles.cfg_award_img} src={score} mode='widthFix' />
                    <View className={styles.cfg_award_text}>挑战奖励</View>
                    <View className={`${styles.cfg_desc} linear_text`}>获胜一方获得{pointsOptions[pointsIdx]}</View>
                    <View className={styles.cfg_current_time}>发起时间: {currentTime}</View>

                    <View className={styles.cfg_btns}>
                        <View className={`${styles.btn} ${styles.btn_cancel}`} onClick={() => setShowConfig(false)}>
                            取消挑战
                        </View>
                        <View className={`${styles.btn} ${styles.btn_primary}`} onClick={() => {
                            setShowConfig(false);
                            setActiveTab(2); // 跳转到我的PK标签页
                        }}>
                            <Text className='linear_text'>发起挑战</Text>
                        </View>
                    </View>
                    <View className={styles.cfg_tips}>单次成绩最高者/相同成绩优先完成者获胜</View>
                </View>
            </Dialog>
        </View>
    );
}
