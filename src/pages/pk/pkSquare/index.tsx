import logo from '@/assets/dialogue/send_1.png';

import { I<PERSON>, Loading } from '@antmjs/vantui';
import { Image, Swiper, SwiperItem, Text, View } from '@tarojs/components';
import classNames from 'classNames';
import styles from './index.less';

export default function PkSquare() {
    const noticeItems = [
        { avatar: logo, text: '曹老板获得了30积分!' },
        { avatar: logo, text: '李经理获得了50积分!' }
    ];

    return (
        <View>
            <View className={styles.banner_container}>
                <Image
                    className={styles.top_banner}
                    src='https://obsqingnang.guoyaoplat.com/miniprom/squareBanner.png'
                />
                <View className={styles.rule}>活动规则</View>
            </View>
            <View className={styles.activity_num}>
                <Text>活动参与人数：123456</Text>
            </View>
            <View className={styles.scrolling_notice}>
                <Swiper
                    className={styles.swiper_container}
                    current={0}
                    vertical
                    circular
                    autoplay
                    interval={3000}
                    duration={500}
                    indicatorDots={false}
                    indicatorColor='rgba(0, 0, 0, .3)'
                    indicatorActiveColor='#000000'
                >
                    {noticeItems.map((item, index) => (
                        <SwiperItem key={index}>
                            <View className={styles.notice_item}>
                                <Image src={item.avatar} className={styles.notice_avatar} />
                                <Text>{item.text}</Text>
                            </View>
                        </SwiperItem>
                    ))}
                </Swiper>
            </View>
            <View className={styles.my_pk_list}>
                <View className={styles.product_title}>
                    <View className={styles.title_left}>
                        <Image
                            src={require('@/assets/pk/pkSquare/product-left.svg')}
                            className={styles.pk_product_icon}
                        />
                        <Text className={styles.pk_product_name}>PK产品&nbsp;&nbsp;金振口服液</Text>
                        <Image
                            src={require('@/assets/pk/pkSquare/product-right.svg')}
                            className={styles.pk_product_icon}
                        />
                    </View>
                    <View className={styles.title_right}>
                        <Text className={styles.time_box}>
                            剩余时间：<Text className={styles.time}>00:01:02</Text>
                        </Text>
                    </View>
                </View>
                <View className={styles.pk_item_box}>
                    <View className={styles.my_pk_item}>
                        <View className={styles.pk_item_title}>口服金振拜访口服液首口服金振拜访口服液首</View>
                    </View>
                    <View className={styles.initiator_box}>
                        <View className={styles.initiator}>
                            <Image className={styles.initiator_avatar} src={require('@/assets/dialogue/send_1.png')} />
                            <Text className={styles.initiator_name}>张三</Text>
                        </View>
                        <View className={styles.initiator}>
                            <Loading size='40px' color='#fff3f2' vertical className={styles.initiator_loading}>
                                等待加入
                            </Loading>
                            <View className={styles.initiator_icon}>
                                <Icon name='plus' size='16px' color='#fff' />
                            </View>
                        </View>
                    </View>
                    <View className={styles.pk_points}>
                        <Text>30积分</Text>
                    </View>
                </View>
            </View>
            <View className={styles.sub_title}>
                <View className={styles.pk_list_title}>擂台列表</View>
                <View className={styles.pk_list_more}>
                    <Text>查看全部</Text>
                    <Image src={require('@/assets/pk/pkSquare/right.png')} className={styles.arrow_icon} />
                </View>
            </View>
            <View className={styles.ring_list}>
                <View className={styles.ring_list_item}>
                    <View className={styles.ring_list_item_title}>
                        <Text className={styles.ring_product_name}>PK产品&nbsp;&nbsp;金振口服液</Text>
                    </View>
                    <View className={styles.ring_list_item_box}>
                        <View className={styles.ring_initiator}>
                            <Image
                                className={styles.ring_initiator_avatar}
                                src={require('@/assets/dialogue/send_1.png')}
                            />
                            <Text className={styles.ring_initiator_name}>张三</Text>
                        </View>
                        <View className={styles.initiator}>
                            <Loading size='30px' color='#fff3f2' vertical className={styles.ring_initiator_loading}>
                                等待加入
                            </Loading>
                            <View className={styles.ring_initiator_icon}>
                                <Icon name='plus' size='12px' color='#fff' />
                            </View>
                        </View>
                    </View>
                </View>
                <View className={styles.ring_list_item} >
                    <View className={styles.ring_list_item_title}>
                        <Text className={styles.ring_product_name}>PK产品&nbsp;&nbsp;金振口服液</Text>
                    </View>
                    <View className={styles.ring_list_item_box}>
                        <View className={styles.ring_initiator}>
                            <Image
                                className={styles.ring_initiator_avatar}
                                src={require('@/assets/dialogue/send_1.png')}
                            />
                            <Text className={styles.ring_initiator_name}>张三</Text>
                        </View>
                        <View className={styles.initiator}>
                            <Loading size='30px' color='#fff3f2' vertical className={styles.ring_initiator_loading}>
                                等待加入
                            </Loading>
                            <View className={styles.ring_initiator_icon}>
                                <Icon name='plus' size='12px' color='#fff' />
                            </View>
                        </View>
                    </View>
                </View>
            </View>

            <View className={styles.sub_title}>
                <View className={styles.pk_list_title}>
                    排行榜
                    <Image src={require('@/assets/pk/pkSquare/info.svg')} className={styles.info_icon} />
                </View>
                <View className={styles.pk_list_more}>
                    <Text>查看全部</Text>
                    <Image src={require('@/assets/pk/pkSquare/right.png')} className={styles.arrow_icon} />
                </View>
            </View>
            <View className={styles.rank_list}>
                <View className={styles.rank_container}>
                    <View className={styles.rank_item}>
                        <View className={classNames(styles.rank_bg_second, styles.rank_bg_next) }>
                            <View className={styles.rank_avatar}>
                                {/* 头像占位 */}
                            </View>
                            <View className={styles.rank_name}>
                                {/* 姓名占位 */}
                                张三
                            </View>
                            <View className={styles.rank_score}>
                                {/* 分数占位 */}
                                赢13场
                            </View>
                        </View>
                    </View>
                    <View className={styles.rank_item}>
                        <View className={styles.rank_bg_first}>
                            <View className={styles.rank_avatar}>
                                {/* 头像占位 */}
                            </View>
                            <View className={styles.rank_name}>
                                {/* 姓名占位 */}
                                张三
                            </View>
                            <View className={styles.rank_score}>
                                {/* 分数占位 */}
                                赢13场
                            </View>
                        </View>
                    </View>
                    <View className={styles.rank_item}>
                        <View className={ classNames(styles.rank_bg_third, styles.rank_bg_next)}>
                            <View className={styles.rank_avatar}>
                                {/* 头像占位 */}
                            </View>
                            <View className={styles.rank_name}>
                                {/* 姓名占位 */}
                                张三
                            </View>
                            <View className={styles.rank_score}>
                                {/* 分数占位 */}
                                赢13场
                            </View>
                        </View>
                    </View>
                </View>
                <View className={styles.rank_list_bottom}>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>4</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>曹老板</View>
                        <View className={styles.rank_score_bottom}>赢10场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>5</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>李经理</View>
                        <View className={styles.rank_score_bottom}>赢8场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>6</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>王总</View>
                        <View className={styles.rank_score_bottom}>赢7场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>7</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>张主任</View>
                        <View className={styles.rank_score_bottom}>赢6场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>8</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>刘总监</View>
                        <View className={styles.rank_score_bottom}>赢5场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>9</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>陈经理</View>
                        <View className={styles.rank_score_bottom}>赢4场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>9</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>陈经理</View>
                        <View className={styles.rank_score_bottom}>赢4场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>9</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>陈经理</View>
                        <View className={styles.rank_score_bottom}>赢4场</View>
                    </View>
                    <View className={styles.rank_list_item}>
                        <View className={styles.rank_number_bottom}>9</View>
                        <View className={styles.rank_avatar_bottom}>
                            {/* 头像占位 */}
                        </View>
                        <View className={styles.rank_name_bottom}>陈经理</View>
                        <View className={styles.rank_score_bottom}>赢4场</View>
                    </View>
                </View>
                <View className={styles.floating_column}>
                    <View className={styles.rank_number_bottom}>4</View>
                    <View className={styles.rank_avatar_bottom}>
                        {/* 头像占位 */}
                    </View>
                    <View className={styles.rank_name_bottom}>我自己</View>
                    <View className={styles.rank_score_bottom}>赢10场</View>
                </View>
            </View>
        </View>
    );
}
