@import '@/styles/index.less';
.banner_container {
  position: relative;
}
.top_banner {
  width: 100%;
  height: 340px;
  border-radius: 30px;
}
.rule {
  position: absolute;
  top: 80px;
  right: 0;
  width: 100px;
  height: 40px;
  padding: 0 10px;
  line-height: 40px;
  text-align: center;
  font-size: 24px;
  color: #8c9094;
  background: rgba(255, 255, 255, 0.5);
  border-top-left-radius: 20rpx;
  border-bottom-left-radius: 20rpx;
}
.activity_num {
  font-size: 20px;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
}
.scrolling_notice {
  width: 100%;
  height: 64px;
  margin-top: 10px;
  background: #F2E6FA;
  color: #9A6BF9;
  border-radius: 32px;
  padding: 0 30rpx;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
  margin-bottom: 20px;
}

.swiper_container {
  width: 100%;
  height: 100%;
}

.notice_item {
  display: flex;
  align-items: center;
  height: 64px;
  line-height: 64px;
  width: 100%;
}

.notice_avatar {
  width: 44px;
  height: 44px;
  border-radius: 50%;
  margin-right: 10px;
}

.my_pk_list {
  width: 100%;
  box-sizing: border-box;
  height: 340px;
  background: url("@/assets/pk/pkSquare/my_pk_bg.svg") no-repeat;
  background-size: 100% 100%;
  border-radius: 32px;
  box-shadow: rgba(149, 157, 165, 0.2) 0 8px 24px;
}

.product_title {
  height: 48px;
  padding-left: 6px;
  padding-top: 6rpx;
  padding-right: 26px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title_left {
  font-size: 32px;
  font-weight: 600;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
  display: flex;
  align-items: center;
}

.title_right {
  font-size: 20px;
  color: #8D9094;
  margin-bottom: 8px;
}

.time {
  font-weight: bold;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background:  linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
}

.pk_item_box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.my_pk_item {
  width: 94%;
  box-sizing: border-box;
  height: 254rpx;
  margin-top: 10rpx;
  background: url("@/assets/pk/pkSquare/my_pk_item.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
}

.pk_item_title {
  width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-top: 6rpx;
  padding-left: 22rpx;
  color: #9A6BF9;
  font-size: 20px;
}

.initiator_box {
  position: absolute;
  display: flex;
  justify-content: space-between;
  width: 94%;
  box-sizing: border-box;
  padding: 60px 26px 0;
}

.initiator {
  display: flex;
  align-items: center;
  flex-direction: column;
  position: relative;
}

.initiator_icon {
  position: absolute;
  top: 22rpx;
  right: 42rpx;
}

.initiator_loading {
  margin-right: 10px;
  :global {
    .van-loading__spinner--circular {
      border-width: 8px;
    }
    .van-loading__text {
      color: #fff3f2;
      font-size: 24px;
      font-weight: bold;
    }
    .van-loading__spinner {
      animation-duration: 1.2s;
    }
  }
}

.initiator_avatar {
  width: 86px;
  height: 86px;
  border-radius: 50%;
  border: 4rpx solid #fff;
}

.initiator_name {
  margin-top: 10rpx;
  color: #fff;
  font-size: 24px;
  font-weight: bold;
}

.pk_points {
  position: absolute;
  font-size: 32px;
  font-weight: bold;
  bottom: 48px;
  background:  linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.pk_product_icon {
  width: 66rpx;
  height: 20rpx;
}

.pk_product_name {
  display: inline-block;
  width: 278rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sub_title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 16px 0;
}

.pk_list_title {
  font-size: 32px;
  font-weight: bold;
  color: transparent;
  -webkit-text-fill-color: transparent;
  background: linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.pk_list_more {
  color: #B4B9BF;
  font-size: 24px;
  display: flex;
  align-items: center;
}

.ring_list {
  width: 100%;
  height: 170px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex: 1;
}

.ring_list_item {
  width: 49%;
  height: 100%;
  background: url("@/assets/pk/pkSquare/ring_list_item.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  .ring_initiator {
    display: flex;
    align-items: center;
    flex-direction: column;
  }
  .ring_initiator_avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 4rpx solid #fff;
  }
  .ring_initiator_name {
    margin-top: 10rpx;
    color: #fff;
    font-size: 20px;
    font-weight: bold;
  }
}

.ring_initiator_loading {
  :global {
    .van-loading__spinner--circular {
      border-width: 8px;
    }
    .van-loading__text {
      color: #fff3f2;
      font-size: 20px;
      font-weight: bold;
      margin-top: 10px;
    }
    .van-loading__spinner {
      animation-duration: 1.2s;
    }
  }
}

.ring_initiator_icon {
  position: absolute;
  top: 14rpx;
  right: 28rpx;
}

.ring_list_item_box {
  display: flex;
  justify-content: space-between;
  padding: 8px 24rpx 0;
}

.ring_list_item_title {
  width: 100%;
  height: 32rpx;
  padding-top: 12rpx;
  display: flex;
  justify-content: center;
  .ring_product_name {
    width: 176rpx;
    font-size: 20px;
    font-weight: bold;
    background: linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.arrow_icon {
  width: 32px;
  height: 32px;
  margin-top: 4px;
}

.info_icon {
  width: 32rpx;
  height: 40rpx;
  margin-left: 2rpx;
  margin-top: 2rpx;
}

.rank_list {
  width: 100%;
  min-height: 1000px;
  background: url("@/assets/pk/pkSquare/rank_bg.png") no-repeat;
  background-size: 100% 100%;
  box-sizing: border-box;
  padding: 40px 32px 14px;
  position: relative;
}

.rank_container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  gap: 20px;
}

.rank_item {
  flex: 1;
  display: flex;
  justify-content: center;
}

.rank_bg_first {
  width: 100%;
  height: 280px;
  background: url("@/assets/pk/pkSquare/first.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  // padding: 20px 10px;
  box-sizing: border-box;
}

.rank_bg_second {
  width: 100%;
  height: 280px;
  background: url("@/assets/pk/pkSquare/two.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  // padding: 20px 10px;
  box-sizing: border-box;
}

.rank_bg_third {
  width: 100%;
  height: 280px;
  background: url("@/assets/pk/pkSquare/three.png") no-repeat;
  background-size: 100% 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  // padding: 20px 10px;
  box-sizing: border-box;
}

.rank_bg_next {
  margin-top: 52px;
}

.rank_number {
  font-size: 48px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 10px;
}

.rank_avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  border: 4px solid #fff;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: absolute;
  top: 78px;

}

.rank_name {
  font-size: 24px;
  font-weight: bold;
  color: #2F3133;
  position: absolute;
  text-align: center;
  top: 196px;
}

.rank_score {
  font-size: 20px;
  font-weight: bold;
  color: #ffffff;
  text-align: center;
  position: absolute;
  bottom: 18px;
}

.floating_column {
  position: absolute;
  left: 0;
  bottom: 32px;
  z-index: 10;
  width: calc(100% - 58px);
  height: 66px;
  display: flex;
  align-items: center;
  //border-radius: 12px;
  padding: 0 16px;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  pointer-events: none;
  margin: 0 29px;
  border-radius: 40px;
  border: 2px solid  #B793FF;
  background: #F2E6FA;
  .rank_name_bottom{
    font-weight: bold;
    color: #2F3133;
  }
}

.rank_list_bottom {
  width: 100%;
  margin-top: 6px;
  box-sizing: border-box;
  max-height: 524px;
  overflow-y: auto;
  position: relative;
  
  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
}

.rank_list_item {
  width: 100%;
  height: 66px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  //margin-bottom: 10px;
  padding: 0 16px;
  box-sizing: border-box;
  &:last-child {
    margin-bottom: 0;
  }
}

.rank_number_bottom {
  width: 40px;
  font-size: 28px;
  font-weight: bold;
  color: #2F3133;
  text-align: center;
}

.rank_avatar_bottom {
  width: 46px;
  height: 46px;
  border-radius: 50%;
  margin-left: 16px;
  margin-right: 12px;
  border: 4px solid #fff;
}

.rank_name_bottom {
  flex: 1;
  font-size: 24px;
  color: #8D9094;
  font-weight: 500;
}

.rank_score_bottom {
  font-size: 24px;
  background: linear-gradient(180deg, #B793FF 18.75%, #6732E1 81.25%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: bold;
}
