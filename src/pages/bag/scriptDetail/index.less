@import '@/styles/index.less';

page{
    background: #fff;
}
.page {
   padding:32px ;
   box-sizing: border-box;
   height: 100vh;
}
.page_body {
    flex: 1;
    overflow-y: auto;
}
.title{
    font-size: 40px;
    font-weight: bold;
    margin-bottom: 48px;
    color: #272C47;
}
.container {
    margin-top: 42px;

    &_item {
        .wrap-box(16px 32px 16px 32px);

        // margin-bottom: 24px;
        min-height: 108px;
        border-radius: 16px;
        &_title {
            .font(32px,#272C47,600);
        }
        &_content {
            .font(28px,#67686F,400);
        }
    }
    &_horizontal {
        display: flex;
        align-items: center;
        justify-self: flex-start;

        .container_item_content {
            margin-left: 48px;
            line-height: 40px;
        }
    }
    &_vertical {
        display: flex;
        justify-content: center;
        flex-direction: column;

        .container_item_content {
            margin-top: 10px;
            line-height: 40px;
        }
    }
}