import { Page } from '@/components';
import { getScript } from '@/services/chat';
import { Block, Text, View } from '@tarojs/components';
import { useLoad, useRouter } from '@tarojs/taro';
import classnames from 'classnames';
import { useState } from 'react';

import type { ScriptVO } from '@/types/chat';
import styles from './index.less';

const App = () => {
    enum ChatType {
        ALL = 0,
        ANSWER = 2,
        SKILL = 1
    }
    const { params } = useRouter<{ id: string }>();
    const { id } = params; // 报告ID
    const [scriptDetail, setScriptDetail] = useState<ScriptVO>({} as ScriptVO);
    const [randomText, setRandomText] = useState<string>('');

    function handleRandomText(randomFlag: number, randomNum: number, questions: any[]) {
        let text = '';
        //  0-按顺序提问 1-随机提问顺序 2-随机指定数量题目 3-随机部分比例题目
        switch (randomFlag) {
            case 0:
                text = '按顺序提问所有问题';
                break;
            case 1:
                text = '随机提问所有题目';
                break;

            case 2:
                text = `随机提问${randomNum}题`;
                break;
            case 3:
                // 向上取整randomNum
                text = `随机提问${Math.ceil((randomNum / 100) * questions.length)}题`;
                break;
            default:
                text = '';
        }
        setRandomText(text);
    }

    const requestData = async () => {
        try {
            const listRes = await getScript(id);
            const { code, data } = listRes.data;
            if (code === 200) {
                setScriptDetail(data);
                handleRandomText(data.randomFlag, data.randomNum, data.question);
            }
        } catch (error) {
            console.log(error);
        }
    };
    useLoad(() => {
        requestData();
    });

    return (
        <Page className={styles.page}>
            <View className={styles.page_body}>
                <View className={styles.title}>{scriptDetail.name}</View>
                <View className={styles.container}>
                    {scriptDetail.type === ChatType.SKILL && (
                        <Block>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>背景</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.backdrop}</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>目标</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.goal}</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>产品</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.product}</Text>
                            </View>
                            {scriptDetail.location && (
                                <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                    <Text className={styles.container_item_title}>地点</Text>
                                    <Text className={styles.container_item_content}>{scriptDetail.location}</Text>
                                </View>
                            )}

                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>时限</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.timeLimit}分钟</Text>
                            </View>
                        </Block>
                    )}
                    {scriptDetail.type === ChatType.ANSWER && (
                        <Block>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>产品</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.product}</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>提问方式</Text>
                                <Text className={styles.container_item_content}>
                                    {/* {scriptDetail.randomFlag ? '随机提问所有题目' : '按顺序提问所有问题'} */}
                                    {randomText}
                                </Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_horizontal)}>
                                <Text className={styles.container_item_title}>时限</Text>
                                <Text className={styles.container_item_content}>{scriptDetail.timeLimit}分钟</Text>
                            </View>
                            <View className={classnames(styles.container_item, styles.container_vertical)}>
                                <Text className={styles.container_item_title}>试题</Text>
                                <View className={styles.container_item_content}>
                                    {scriptDetail.question
                                        ? scriptDetail.question.map((item, index) => (
                                              <View key={item.id}>
                                                  {index + 1}.{item.question}
                                              </View>
                                          ))
                                        : null}
                                </View>
                            </View>
                        </Block>
                    )}
                </View>
            </View>
        </Page>
    );
};

export default App;
