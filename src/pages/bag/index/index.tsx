import { Page } from '@/components';
import { getScriptList } from '@/services/chat';
import type { ScriptVO } from '@/types/chat';
import { Image, ScrollView, Text, View } from '@tarojs/components';
import Taro, { useLoad } from '@tarojs/taro';
import classnames from 'classnames';
import { useRef, useState } from 'react';
import styles from './index.less';
import config from '@/config';

const empty = `${config.cdnPrefix}mine/empty.png`;

const App = () => {
    enum ChatType {
        ALL = 0,
        ANSWER = 2,
        SKILL = 1
    }

    const [list, setList] = useState<ScriptVO[]>([]);
    const hasMore = useRef(true);
    const pageNo = useRef(1);
    const [currentScriptType, setCurrentScriptType] = useState<ChatType>(ChatType.ALL);

    // 分页处理
    const requestData = async () => {
        if (!hasMore.current) return;
        try {
            const listRes = await getScriptList();
            const { code, data } = listRes.data;

            if (code === 200) {
                if (hasMore) {
                    pageNo.current += 1;
                }
                setList((oldList) => {
                    const list = [...oldList, ...data.records];
                    if (list.length >= Number(data.total)) {
                        pageNo.current = 1;
                        hasMore.current = false;
                    }
                    return list;
                });
            }
        } catch (error) {
            console.log(error);
        }
    };

    useLoad(() => {
        pageNo.current = 1;
        requestData();
    });
    const onScroll = () => {
        requestData();
    };
    const linkToScript = (id: string) => {
        Taro.navigateTo({ url: `/pages/bag/scriptDetail/index?id=${id}` });
    };

    return (
        <Page className={styles.page}>
            <View className={styles.page_body}>
                <View className={styles.title}>脚本</View>
                <View className={styles.scripts_type}>
                    <View
                        className={classnames(
                            styles.scripts_type_item,
                            currentScriptType === ChatType.ALL && styles.scripts_type_item_active
                        )}
                        onClick={() => setCurrentScriptType(ChatType.ALL)}
                    >
                        全部脚本
                    </View>
                    <View
                        className={classnames(
                            styles.scripts_type_item,
                            currentScriptType === ChatType.SKILL && styles.scripts_type_item_active
                        )}
                        onClick={() => setCurrentScriptType(ChatType.SKILL)}
                    >
                        技巧类
                    </View>
                    <View
                        className={classnames(
                            styles.scripts_type_item,
                            currentScriptType === ChatType.ANSWER && styles.scripts_type_item_active
                        )}
                        onClick={() => setCurrentScriptType(ChatType.ANSWER)}
                    >
                        答题类
                    </View>
                </View>
                {list?.length > 0 && (
                    <View className={styles.list}>
                        <ScrollView
                            className={styles.paginationList}
                            onScrollToLower={onScroll}
                            scrollY
                            lowerThreshold={30}
                        >
                            {list?.map((item) => (
                                <View className={styles.item} onClick={() => linkToScript(item.id)} key={item.id}>
                                    <View className={styles.item_title}>{item.name}</View>
                                    <View className={styles.item_desc}>{item.backdrop}</View>
                                    <View className={styles.item_time}>{item.updateTime}</View>
                                </View>
                            ))}
                        </ScrollView>
                    </View>
                )}
                {list?.length === 0 && (
                    <View className={styles.empty}>
                        <Image src={empty} className={styles.icon} />
                        <Text>暂无脚本</Text>
                    </View>
                )}
            </View>
        </Page>
    );
};

export default App;
