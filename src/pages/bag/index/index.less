
@import '@/styles/index.less';

page{
    background: #F9F9F9;
}
.page {
   padding:32px ;
   box-sizing: border-box;
   height: 100vh;
   color: #272C47;
}
.page_body {
    flex: 1;
    overflow-y: auto;
}
.title{
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 24px;
}
.icon{
    width: 56px;
    height: 56px;
}
.item{
    width: 100%;
    background-color: #fff;
    box-sizing: border-box;
    padding: 42px 32px;
    border-radius: 24px;
    color: #272c47;
    margin-bottom: 32px;

    &_title {
        font-weight: bold;
        font-size: 32px;
        .ellipsis();
    }
    &_desc {
        font-size: 24px;
        margin-top: 16px;
        color: #999;
        .multi-ellipsis(2);
    }
}
.list{
    height:calc(100vh - 280px);
    overflow-y: hidden;
}
.paginationList{
    height:calc(100vh - 280px);
}
.empty{
    display: flex;
    align-items: center;
    flex-direction: column;
    gap:24px 0;
    margin-top: 25vh;
    font-size: 28px;
    color: #9597a0;
    font-weight: 400;
    .icon{
        width: 400px;
        height: 205px;
    }
}

.scripts_type {
    display: flex;
    gap: 16px;
    margin-bottom: 36px;
    &_item {
        border-radius: 16px;
        padding: 16px 30px;
        border: 1px solid #EDEDED;
        color: #272C47;
        font-size: 24px;
        &_active {
            border: 1px solid #4F66FF;
            color: #4F66FF;
        }
    }
}