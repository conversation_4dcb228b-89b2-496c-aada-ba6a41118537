@import '@/styles/index.less';

page {
  background: #F9F9F9;
  padding: 0 32px;

  --nav-bar-background-color: transparent;

  box-sizing: border-box;
  position: relative;
}

.avatar_button {
  background-color: transparent;
  padding: 0;
  margin: 0;
  // margin-right: 24px;
  &::after {
    display: none;
  }
}
.avatar_image {
  border-radius: 100%;
  width: 108px;
  height: 108px;
  display: block;
  // margin-right: 24px;
}
.placeholder {
  color: #3c3f3e;
  font-size: 36px;
  font-weight: 500;
}
.nickname {
  font-size: 36px;
 font-weight: 500;
 text-align: right;
}
.phoneButton {
  font-size: 36px;
  background: none;
 font-weight: 500;
 color: #3C3F3E;
 margin: 0;
 padding: 0;
 &::after{ border: none; }
}
.munuBox{
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 37px 32px;
  justify-content: space-between;
  border-radius: 24px;
  .left{
    display: flex;
    align-items: center;
 justify-content: flex-end;

  }
  .menuText{
    font-size: 32px;
    color:#272C47 ;
  }
}
.icon{
  width: 36px;
  height: 36px;
  margin-right: 16px;
}
.right{
  width: 36px;
  height: 36px;
}
.munuBoxs{
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  border-radius: 24px;
  overflow: hidden;

  .munuBox{
    border-radius: 0;
    width: calc(100% - 64px);
    position: relative;
   &::after{
    content: '';
    width: calc(100% - 64px);
    position: absolute;
    background-color: #EEEFF3;
    height: 1px;
    bottom: 0;
    left: 32px;
   }
  }

  
  :last-child{
    &::after{
      display: none;
     }
   }
}
.button {
  .footer-fixed;

  box-sizing: border-box;
  padding: 0 32px;
  bottom: 48px;
  z-index: 100;
}