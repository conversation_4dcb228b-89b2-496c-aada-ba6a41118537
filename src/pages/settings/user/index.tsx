import { Storage, useStorage } from '@/common';
import { Page } from '@/components';
import config from '@/config';
import { StorageEnvKey } from '@/constants/storage';
import { upload } from '@/services/common';
import { getWxWorkCode, updateUser, weworkLogin } from '@/services/user';
import type { UserInfo } from '@/types/common';
import { updateUserStorage } from '@/utils/login';
import { Button } from '@hygeia/ui';
import { Image, Input, View } from '@tarojs/components';
import Taro, { pxTransform, useDidShow } from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import { useState } from 'react';
import styles from './index.less';
const App = () => {
    const AvatarDefault = `${config.cdnPrefix}avatar_default_man.jpg`;
    const [USERINFO] = useStorage<UserInfo>(StorageEnvKey.USERINFO);
    const [avatar, setAvatar] = useState(AvatarDefault);
    const [nickname, setNickName] = useState('');
    const [isWework] = useStorage<string>(StorageEnvKey.IS_WEWORK);

    // TODO: 头像获取到的只是头像的临时地址，需要将头像上传到服务器存储获取永久地址
    const handleUploadAvatar = async (avatar: string) => {
        if (isWework === '1') {
            Taro.downloadFile({
                url: avatar,
                success: async (res: any) => {
                    const res1 = await upload(res.tempFilePath);
                    const obj = JSON.parse(res1);
                    setAvatar(obj.data);
                },
                fail: (res: any) => {
                    console.log('downloadFile fail', res);
                }
            });
        } else {
            const res = await upload(avatar);
            const obj = JSON.parse(res);
            setAvatar(obj.data);
        }
    };

    // 获取头像onChooseAvatar的方法
    const onChooseAvatar = (e: any) => {
        handleUploadAvatar(e.detail.avatarUrl);
    };

    const getWewrokAvatar = async () => {
        wx.qy.getAvatar({
            success(res: { avatar: string }) {
                console.log('getWeworkAvatar success', res);
                const { avatar } = res;
                handleUploadAvatar(avatar);
            },
            fail(res: any) {
                Taro.showToast({
                    title: '获取头像失败',
                    icon: 'none'
                });
                console.log('getWeworkAvatar fail', res.fail_reason);
            }
        });
    };

    const handleLogin = async (cb: () => void) => {
        const { code } = await getWxWorkCode();
        console.log('wxwork code', code);
        const loginRes = await weworkLogin(code);
        const { data: loginData } = loginRes.data;

        if (loginRes.data.code === 200) {
            const { userInfo } = loginData;
            Storage.set(StorageEnvKey.USERINFO, userInfo); // 更新用户信息
            cb();
        }
    };

    const { run: handleWeworkAvatar } = useThrottleFn(
        async () => {
            wx.qy.checkSession({
                success() {
                    console.log('checkSession success');
                    getWewrokAvatar();
                },
                fail(res) {
                    console.log('checkSession fail', res);
                    handleLogin(getWewrokAvatar);
                }
            });
        },
        {
            wait: 1200,
            leading: true,
            trailing: false
        }
    );

    // TODO: 获取昵称onChooseNickname的方法
    const onChooseNickname = (e: any) => {
        setNickName(e.detail.value);
    };

    const updateUserInfo = async () => {
        await updateUser({ name: nickname, avatar });
        USERINFO!.avatar = avatar;
        USERINFO!.name = nickname;
        updateUserStorage(USERINFO!);
        Taro.navigateBack();
    };

    const getWeworkUserInfo = () => {
        wx.qy.getEnterpriseUserInfo({
            async success(res: any) {
                const { userInfo } = res;
                const { name } = userInfo;
                setNickName(name);
                await updateUser({ name: nickname, avatar });
                USERINFO!.avatar = avatar;
                USERINFO!.name = nickname;
                updateUserStorage(USERINFO!);
            }
        });
    };

    const handleUserInfo = () => {
        console.log('avatar', avatar);
        console.log('nickname', nickname);
        if (isWework === '1') {
            wx.qy.checkSession({
                success() {
                    console.log('checkSession success');
                    getWeworkUserInfo();
                },
                fail(res) {
                    console.log('checkSession fail', res);
                    handleLogin(getWeworkUserInfo);
                }
            });
        }
    };

    useDidShow(() => {
        setNickName(USERINFO!.name || '');
        setAvatar(USERINFO!.avatar);
    });

    return (
        <Page className={styles.page}>
            <View className={styles.munuBoxs}>
                <View className={styles.munuBox}>
                    <View className={styles.menuText}>头像</View>
                    <View className={styles.left}>
                        {isWework === '1' ? (
                            <Image
                                src={avatar || AvatarDefault}
                                onClick={handleWeworkAvatar}
                                className={styles.avatar_image}
                                mode='aspectFill'
                            />
                        ) : (
                            <Button
                                open-type='chooseAvatar'
                                onChooseAvatar={onChooseAvatar} // 在taro中使用的是onChooseAvatar
                                className={styles.avatar_button}
                            >
                                <Image
                                    src={avatar || AvatarDefault}
                                    className={styles.avatar_image}
                                    mode='aspectFill'
                                />
                            </Button>
                        )}
                    </View>
                </View>
                <View className={styles.munuBox}>
                    <View className={styles.menuText}>昵称</View>
                    <View className={styles.left}>
                        <Input
                            type='nickname'
                            className={styles.nickname}
                            value={nickname}
                            disabled={isWework === '1'}
                            placeholderClass={styles.placeholder}
                            onClick={handleUserInfo}
                            onInput={onChooseNickname}
                            onBlur={onChooseNickname}
                        />
                    </View>
                </View>
            </View>
            <View className={styles.button}>
                <Button
                    style={{
                        '--padding-md': pxTransform(28),
                        '--button-normal-height': pxTransform(96)
                    }}
                    round
                    block
                    color='linear-gradient(270deg, #6742FF 0.03%, #3D83FF 100.03%)'
                    onClick={updateUserInfo}
                >
                    确定
                </Button>
            </View>
        </Page>
    );
};

export default App;
