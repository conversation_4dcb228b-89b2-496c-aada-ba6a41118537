import { Storage, useShow, useStorage } from '@/common';
import { Page } from '@/components';
import { HomePath } from '@/constants/homePath';
import { StorageEnvKey } from '@/constants/storage';
import { getUserInfoSetting, logout } from '@/services/user';
import type { UserInfo } from '@/types/common';
import frontLogout from '@/utils/fontLogout';
import { checkNetworkStatus } from '@/utils/networkUtils';
import { View } from '@tarojs/components';
import Taro from '@tarojs/taro';
import { useThrottleFn } from 'ahooks';
import { useRef, useState } from 'react';
import styles from './index.less';

const App = () => {
    const [USERINFO] = useStorage<UserInfo>(StorageEnvKey.USERINFO);
    const [isEnterprise] = useStorage<number>(StorageEnvKey.IS_ENTERPRISE);

    const [tenantName, setTenantName] = useState<string>();
    const logoutSubmit = useRef(true);

    const handleLogout = () => {
        frontLogout();
        Taro.showToast({
            title: '退出登录成功',
            icon: 'none',
            mask: true
        });

        setTimeout(() => {
            const homePath = Storage.get(StorageEnvKey.HOME_PATH);
            Taro.reLaunch({ url: homePath ? homePath : HomePath.DIALOG });
        }, 1200);
    };

    const { run: loginOut } = useThrottleFn(
        async () => {
            try {
                await checkNetworkStatus();
                if (!logoutSubmit.current) return;
                Taro.showModal({
                    title: '提示',
                    content: '是否确认退出登录',
                    async success(res) {
                        if (res.confirm) {
                            logoutSubmit.current = false;
                            try {
                                await logout();
                                handleLogout();
                            } catch (error: any) {
                                console.log(error);
                                if ('data' in error) {
                                    if (error.data.code === 401 || error.data.code === 20004) {
                                        handleLogout();
                                    } else {
                                        logoutSubmit.current = true;
                                        Taro.showToast({
                                            icon: 'none',
                                            title: '请求错误'
                                        });
                                    }
                                } else {
                                    logoutSubmit.current = true;
                                    if (
                                        'errMsg' in error &&
                                        (error.errMsg.includes('timeout') || error.errMsg.includes('time out'))
                                    ) {
                                        Taro.showToast({
                                            icon: 'none',
                                            title: '网络不给力，\r\n请检查网络设置'
                                        });
                                    } else {
                                        Taro.showToast({
                                            icon: 'none',
                                            title: '网络错误'
                                        });
                                    }
                                }
                            }
                        } else if (res.cancel) {
                            console.log('用户点击取消');
                        }
                    }
                });
            } catch (error) {
                console.log(error);
            }
        },
        {
            wait: 2000,
            leading: true,
            trailing: false
        }
    );

    /* const gotoChoose = () => {
        Taro.navigateTo({ url: '/pages/login/choose/index' });
    }; */

    useShow(async () => {
        if (isEnterprise === 1) {
            try {
                const { data } = await getUserInfoSetting();
                if (data.data.companyName) {
                    setTenantName(data.data.companyName);
                } else {
                    try {
                        await logout();
                        frontLogout();
                    } catch (error) {
                        console.log(error);
                    }

                    Taro.showToast({
                        icon: 'none',
                        title: '重新登录',
                        mask: true,
                        duration: 1000
                    });
                    setTimeout(() => {
                        Taro.reLaunch({
                            url: homePath ? homePath : HomePath.DIALOG
                        });
                    }, 1000);
                }
            } catch (error) {
                console.log(error);
            }
        }
    });

    return (
        <Page className={styles.page}>
            <View className={styles.munuBoxs}>
                <View className={styles.munuBox}>
                    <View className={styles.menuText}>登录账号</View>
                    <View className={styles.left}>{USERINFO?.phoneNumber}</View>
                </View>
            </View>
            {isEnterprise === 1 && (
                <View className={styles.munuBoxs}>
                    <View className={styles.munuBox}>
                        <View className={styles.menuText}>所属企业</View>
                        <View className={styles.left}>{tenantName}</View>
                    </View>
                </View>
            )}

            <View className={styles.button} onClick={loginOut}>
                退出登录
            </View>
        </Page>
    );
};

export default App;
