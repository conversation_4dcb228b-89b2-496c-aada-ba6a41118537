@import '@/styles/index.less';

page {
  background: #F9F9F9;
  padding: 32px 32px;

  --nav-bar-background-color: transparent;

  box-sizing: border-box;
  position: relative;
}


.munuBox{
  display: flex;
  background-color: #fff;
  justify-content: space-between;
  height: 108px;
  align-items: center;
  padding: 0 32px;
  font-size: 32px;
  .left{
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #9597A0;
  }
  .menuText{
    color: #272C47;
    font-size: 32px;
    font-weight: 500;
  }
}
.icon{
  width: 36px;
  height: 36px;
  margin-right: 16px;
}
.right{
  width: 36px;
  height: 36px;
}
.munuBoxs{
  display: flex;
  flex-direction: column;
  margin-top: 24px;
  border-radius: 24px;
  overflow: hidden;

  .munuBox{
    border-radius: 0;
    width: calc(100% - 64px);
    position: relative;
   &::after{
    content: '';
    width: calc(100% - 64px);
    position: absolute;
    background-color: #EEEFF3;
    height: 1px;
    bottom: 0;
    left: 32px;
   }
  }

  
  :last-child{
    &::after{
      display: none;
     }
   }
}
.button {
  height: 96px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  font-size: 32px;
  font-weight: 500;
  color: #9597a0;
  background-color: #fff;
  border-radius: 24px;
  margin-top: 24px;
}