{"name": "quantum-weapp", "version": "1.0.0", "private": true, "description": "语贝", "author": "MrYeZiqing", "scripts": {"build:alipay": "taro build --type alipay", "build:h5": "taro build --type h5", "build:jd": "taro build --type jd", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "build:rn": "taro build --type rn", "build:swan": "taro build --type swan", "build:tt": "taro build --type tt", "build:weapp": "taro build --type weapp", "build:weappyubeiai": "taro build --type weapp --mode yubeiai", "build:weappkangyuan": "taro build --type weapp --mode kangyuan", "commit-msg": "hera-fabric --hooks=commit-msg", "dev": "npm run build:weapp -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:weapp": "npm run build:weapp -- --watch", "dev:weappyubei": "npm run build:weapp -- --watch --mode yubeiai", "dev:weappkangyuan": "npm run build:weapp -- --watch --mode kangyuan", "hera-fabric:install": "hera-fabric install", "preinstall": "npx only-allow pnpm", "lint": "eslint src --ext .js,.jsx,.ts,.tsx --quiet && stylelint 'src/**/*.{less,css}'", "lint-fix": "eslint src --ext .js,.jsx,.ts,.tsx --quiet --fix && stylelint 'src/**/*.{less,css}' --fix", "pre-commit": "hera-fabric --hooks=pre-commit && lint-staged", "pre-merge-commit": "hera-fabric --hooks=pre-merge-commit", "prepare": "npx husky install", "prod:weapp": "npm run build:weapp --watch --env production", "prodbuild:weapp": "taro build --type weapp --watch --env production", "prodbuild:weappyubeiai": "taro build --type weapp --watch --env production --mode yubeiai", "prodbuild:weappkangyuan": "taro build --type weapp --watch --env production --mode kangyuan"}, "lint-staged": {"*.{js,jsx,ts,tsx}": "eslint --fix --quiet", "*.{css,less,scss}": "stylelint --fix", "*.{md,json,html}": "pretty-quick --staged"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@antmjs/vantui": "^3.4.7", "@babel/runtime": "^7.7.7", "@hera/fabric": "^3.0.0", "@hera/practical-utils": "^1.0.2", "@hera/react-utils": "^1.0.2", "@hera/request": "^2.0.1", "@hera/type-utils": "^1.0.2", "@hygeia/ui": "^2.0.34", "@tarojs/components": "3.6.32", "@tarojs/helper": "3.6.32", "@tarojs/plugin-framework-react": "3.6.32", "@tarojs/plugin-platform-alipay": "3.6.32", "@tarojs/plugin-platform-jd": "3.6.32", "@tarojs/plugin-platform-qq": "3.6.32", "@tarojs/plugin-platform-swan": "3.6.32", "@tarojs/plugin-platform-tt": "3.6.32", "@tarojs/plugin-platform-weapp": "3.6.32", "@tarojs/react": "3.6.32", "@tarojs/router": "3.6.32", "@tarojs/runtime": "3.6.32", "@tarojs/shared": "3.6.32", "@tarojs/taro": "3.6.32", "@tarojs/taro-h5": "3.6.32", "@thednp/dommatrix": "^2.0.11", "@types/crypto-js": "^4.2.2", "ahooks": "^3.7.7", "classnames": "^2.3.2", "crypto-js": "^4.2.0", "dayjs": "^1.11.9", "lottie-miniprogram": "^1.0.12", "qs": "^5.2.1", "react": "^18.0.0", "react-dom": "^18.0.0", "remarkable": "^2.0.1", "taro-ui": "^3.3.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@tarojs/cli": "3.6.32", "@tarojs/plugin-html": "3.6.32", "@tarojs/webpack5-runner": "3.6.32", "@types/react": "^18.0.0", "@types/webpack-env": "^1.13.6", "babel-plugin-import": "^1.13.5", "babel-preset-taro": "3.6.32", "eslint": "7.32.0", "eslint-plugin-taro": "^3.3.20", "husky": "8.0.2", "lint-staged": "13.0.3", "prettier": "2.7.1", "pretty-quick": "3.1.3", "react-refresh": "^0.11.0", "stylelint": "13.13.1", "typescript": "^4.9.4", "webpack": "5.69.0"}, "git": "https://e.gitee.com/hoosh/projects/541031/repos/hoosh/hygeia-dpcm-mini/sources", "templateInfo": {"name": "default", "typescript": true, "css": "less"}}