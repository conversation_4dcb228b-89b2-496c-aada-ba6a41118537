# quantum-weapp

语贝

## 技术架构

-   [taro - 构建框架](https://taro-docs.jd.com/docs/)
-   [react - UI 框架](https://react.docschina.org/)
-   [@antmjs/vantui - 组件库](https://antmjs.github.io/vantui/main/#/home)
-   [hera-utils - 方法库](https://utils.ecaisys.com/)
-   [@hera/request - 请求库](https://verdaccio.hooshine.com/-/web/detail/@hera/request)
-   [@hera/fabric - 开发规范](https://hera-frontteam.yuque.com/org-wiki-hera-frontteam-yatbd6/cdgunu)

其他更多信息请看团队文档 https://hera-frontteam.yuque.com/dashboard/org_wiki

## 微信开发者工具

-   需要设置关闭 ES6 转 ES5 功能，开启可能报错
-   需要设置关闭上传代码时样式自动补全，开启可能报错
-   需要设置关闭代码压缩上传，开启可能报错
-   基础库版本设置为 2.30.2
-   微信开发者工具模拟器关闭热重载

## 架构

-   .husky git 钩子
-   config 配置文件
-   src
    -   components 公共组件
    -   pages 页面
    -   services api 服务
    -   utils 公共方法
    -   app.tsx 全局配置
    -   model 数据流 （和 web 端一致，不同是模块需要从 model/index.ts 统一导出）
    -   enums 枚举
    -   hera 依赖收敛 （一定要看这个）
        -   common 第三方工具或者方法统一从这里导出
    -   utils 公共方法
    -   app.config.ts 全局配置，相当于小程序 app.json
    -   app.less 全局样式

## 注意事项

-   样式使用 less，统一 css module 处理
-   本地缓存 使用 Storge 或者 useStorage，需要在 types 中 LocalStorageKey 添加对应字段，禁止 直接 Taro.getStorageSync
-   由于在 taro 中不支持全局组件，所以所有页面都需要从 components 中引入 Page
-   报错`Cannot read property 'mount' of null`，pnpm-lock.yaml 中@pigjs/browser-utils 中的 qs 版本改成 6.5.3，删除 node_modules，重新安装
-   报错`tmpSc.get is not a function`，qs版本使用5.2.1

## 安装依赖

```js
pnpm install
```

## 启动服务

```js
npm run dev:weapp
npm run prod:weapp
```

## 打包

```js
npm run build:weapp
npm run buildprod:weapp
```
